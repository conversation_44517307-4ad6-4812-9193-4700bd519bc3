# Terraform Infrastructure Repository

Quick walkthrough of this repository and what's what

```
├── environments
│   ├── dev
│   │   ├── network.tf
│   │   ├── provider.tf
│   ├── prod
│   │   ├── network.tf
│   │   ├── provider.tf
│   └── stg
│       ├── network.tf
│       ├── provider.tf
└── modules
    └── networking
        ├── main.tf
        ├── output.tf
        └── variables.tf
```

# Environments
## ./environments folder

On the `./environments` folder you'll be able to find `dev`, `prod` and `stg` sub-folders. Each folder is independent from each other and manages their own state.

> *Why did we do this?* 
> 
> We've made the decision to separate them like this since production will be moved into it's own AWS account on a near future, and so the `provider.tf` file will be different than dev/stg and thus it needs to have a separate state file. 
> 
> Given this information, since we are not entirely sure wether dev and stg will be on separate accounts as well on the future, for the sake of standarization and simplicity we've made a separate folder for each environment.

Having separate environment folders makes it easy to deploy infrastructure that is currently being worked on the dev environment, into the staging environment by just copying a file and updating it's variables accordingly (probably with a quick search/replace).

## provider.tf file within an environment

The `provider.tf` file sets up the connection to our Cloud provider (most of the times will be to AWS). It's VERY important not to setup any access/secret keys on this file since it's public and everything in it will be copied to the terraform.tfstate files (on the S3 bucket).

At the moment, everything is on the same account so we are using the repository secret variables to setup the required access/secret keys via environment variables. On a near future we might need to switch to AWS profiles when multiple accounts are a thing.

This file is also responsible to setup where the state is saved and read from, on our case it's an S3 bucket called `keyway-terraform-states` with a subfolder for each environment. It uses the same keys as the AWS provider for now but we might switch it to a specific AWS profile so that we can avoid having to manually setup bucket ACL privileges for each new account.

## \*.tf files within an environment

All the files within environments should only define variables, all the terraform code should be defined as reusable modules.

The environments often will have a `network.tf` file as they start which setups the internal network for this environment using the `networking` module. 

If you look it up you'll notice that there's only a few variable definitions for arrays or objects required by the module to setup the network, but it doesn't contain any terraform code.

Environments will have files for each deployment (*TBD*). Our initial draft for this is to have (for instance) a file called `deal-room.tf` which will call the `beanstalk`, `codebuild`, `codepipeline`, `ecr` and/or `postgres-db` modules so that the apps from github can be deployed to AWS without having to do any manual steps other than ocasionally triggering CodePipeline or managing the postgres database data. It's also possible that we actually generate a `github-deployment` module which does all of this things at once.

# Modules
## ./modules folder

The modules folder will contain subfolders, one per each module. The idea is that modules are a self-contained block which is always run as-is without any modifications. Anything that needs to be flexible or elastic must be passed to it with variables.

## ./modules/networking

The networking module is actually a collection of things that has to be setup on a certain order. At the moment it groups the setup of VPCs, Subnets, Routing Tables, Gateways, Security Groups, Network ACLs and such so that you can define a couple of CIDRs and Availability Zones, and it does all the heavy lifting for you to generate a VPC with those features.

## ./modules/deployments

_**TBD**_


# Github Actions

## ./.github/workflows folder

On this folder we define the steps to be run on each folder to make sure that the terraform files and the AWS accounts stay on sync.

The main difference for the branches is that the develop branch only performs file validation and plan for changes, while the master branch also apply the changes.

The master branch will be protected so you need to make a PR in order for your changes to be actually applied. This process is currently being tested, since we'd like to have the least amount of bureaucracy while also making sure to never delete or change things that could cause issues to other apps (eg. if you change the name of certain items they need to be regenerated and that could cause databases to be flushed which is never a good thing). 

# Terraform Docs

The documentation is autogenerated by terraform-docs

You can setup the app using brew: `brew install terraform-docs`

And then execute it like this to update all modules:

```bash
terraform-docs -c .terraform-docs.yml ./
terraform-docs -c .terraform-docs-gcp.yml ./
```

You can get some examples at [Automate Terraform documentation like a pro!](https://medium.com/google-cloud/automate-terraform-documentation-like-a-pro-ed3e19998808)

There's also the documentation for terraform-docs at: [their official website](https://terraform-docs.io/user-guide/configuration/)

