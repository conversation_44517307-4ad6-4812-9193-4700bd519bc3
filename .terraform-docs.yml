formatter: "markdown table" # this is required
version: "0.16"
header-from: main.tf
footer-from: ""
recursive:
  enabled: true
  path: "./"
sections:
  hide: []
  show: []
  hide-all: false # deprecated in v0.13.0, removed in v0.15.0
  show-all: true # deprecated in v0.13.0, removed in v0.15.0
content: |-
  {{ .Requirements }}
  ## Usage
  Basic usage of this module is as follows:
  ```hcl
    module "example" {
      {{"\t"}} source  = "<module-path>"
  {{- if .Module.RequiredInputs }}
      {{"\n\t"}} # Required variables
      {{- range .Module.RequiredInputs }}
      {{"\t"}} {{ .Name }}  = {{ .GetValue }}
      {{- end }}
      {{- end }}
  {{- if .Module.OptionalInputs }}
      {{"\n\t"}} # Optional variables
      {{- range .Module.OptionalInputs }}
      {{"\t"}} {{ .Name }}  = {{ .GetValue | printf "%s" }}
      {{- end }}
      {{- end }}
    }
    ```
  {{ .Resources }}
  {{ .Inputs }}
  {{ .Outputs }}
output:
  file: README.md
  mode: inject
  template: |-
    <!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
    {{ .Content }}
    <!-- END_AUTOMATED_TF_DOCS_BLOCK -->
output-values:
  enabled: false
  from: ""
sort:
  enabled: true
  by: name
settings:
  anchor: true
  color: true
  default: true
  description: true
  escape: true
  hide-empty: false
  html: true
  indent: 2
  lockfile: true
  read-comments: true
  required: true
  sensitive: true
  type: true
