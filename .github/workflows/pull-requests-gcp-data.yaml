name: "Terraform GCP Plan (data)"

on:
  pull_request:
    paths:
      - "environments/data-gcp/**"
      - "modules-gcp/**"
      - ".github/workflows/pull-requests-gcp-data.yaml"
      - ".github/workflows/tf-plan.yaml"

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  data-gcp-plan:
    uses: ./.github/workflows/tf-plan.yaml
    with:
      directory: "./environments/data-gcp"
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}
      GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      DD_APP_KEY: ${{ secrets.DD_APP_KEY }}