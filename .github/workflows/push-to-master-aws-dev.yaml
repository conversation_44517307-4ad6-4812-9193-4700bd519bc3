name: "Terraform AWS Apply (dev)"

on:
  push:
    branches:
      - master
    paths:
      - "environments/dev/**"
      - "modules/**"
      - ".github/workflows/push-to-master-aws-dev.yaml"
      - ".github/workflows/tf-apply.yaml"

jobs:
  dev-apply:
    concurrency: dev_environment
    uses: ./.github/workflows/tf-apply.yaml
    with:
      directory: "./environments/dev"
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}
      GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      DD_APP_KEY: ${{ secrets.DD_APP_KEY }}