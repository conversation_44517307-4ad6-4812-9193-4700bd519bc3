name: "Terraform Plan (template)"

on:
  workflow_call:
    inputs:
      directory:
        required: true
        type: string
    secrets:
      GH_TOKEN:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_DEFAULT_REGION:
        required: true
      GOOGLE_APPLICATION_CREDENTIALS:
        required: true
      DD_API_KEY:
        required: true
      DD_APP_KEY:
        required: true

jobs:
  terraform-plan:
    name: "Terraform Plan"
    runs-on: self-hosted
    steps:
      # Checkout the code
      # Marketplace: https://github.com/marketplace/actions/checkout
      - name: "Setup - Checkout"
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_TOKEN }}

      # Downloads a specific version of Terraform CLI and adds it to PATH
      # Marketplace: https://github.com/marketplace/actions/hashicorp-setup-terraform
      - name: "Setup - Terraform CLI"
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.10.5"

      - name: Terraform Fmt (if this fails, run terraform fmt on your code)
        id: fmt
        run: terraform fmt -check -diff
        working-directory: ${{ inputs.directory }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      # https://github.com/google-github-actions/setup-gcloud#authorization
      - name: Configure GCloud Credentials
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: Terraform Init
        id: init
        run: terraform init
        working-directory: ${{ inputs.directory }}

      - name: Terraform Validate
        id: validate
        run: terraform validate -no-color
        working-directory: ${{ inputs.directory }}

      # Note: Color is disabled to prevent messy characters from appearing in the pull request comment
      - name: "Run - Terraform Plan"
        id: plan
        run: terraform plan -input=false -no-color -out=plan.out
        working-directory: ${{ inputs.directory }}
        env:
          DD_API_KEY: ${{ secrets.DD_API_KEY }}
          DD_APP_KEY: ${{ secrets.DD_APP_KEY }}

      - name: "Convert plan to JSON"
        id: plan-json
        run: |
          terraform show -json plan.out > plan.json
        working-directory: ${{ inputs.directory }}

      - name: "Find and Delete Previous Comments"
        run: |
          comments=$(curl -s -H "Authorization: token $GH_TOKEN" https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/comments)
          for comment_id in $(echo "$comments" | jq -r '.[] | select(.user.login == "unlockre-eng") | select(.body | contains("Directory: ${{ inputs.directory }}")) | .id'); do
            curl -s -X DELETE -H "Authorization: token $GH_TOKEN" https://api.github.com/repos/${{ github.repository }}/issues/comments/$comment_id
          done
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: "Post PR Comment"
        run: |
          GITHUB_RUN_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          comment_body=$(python3 parse-plan.py ${{ inputs.directory }}/plan.json ${{ inputs.directory }})
          comment_body=$(echo $comment_body | jq ".body += \"\n### [Click here to view the full plan details]($GITHUB_RUN_URL)\"")
          curl -s -X POST -H "Authorization: token $GH_TOKEN" -H "Content-Type: application/json" -d "$comment_body" \
              https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/comments
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
