name: "Terraform Apply (template)"

on:
  workflow_call:
    inputs:
      directory:
        required: true
        type: string
    secrets:
      GH_TOKEN:
        required: true
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_DEFAULT_REGION:
        required: true
      GOOGLE_APPLICATION_CREDENTIALS:
        required: true
      DD_API_KEY:
        required: true
      DD_APP_KEY:
        required: true

#  push:
#    branches:
#      - master

jobs:
  terraform-plan:
    name: "Terraform Apply"
    runs-on: self-hosted
    steps:
      # Checkout the code
      # Marketplace: https://github.com/marketplace/actions/checkout
      - name: "Setup - Checkout"
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GH_TOKEN }}

      # Downloads a specific version of Terraform CLI and adds it to PATH
      # Marketplace: https://github.com/marketplace/actions/hashicorp-setup-terraform
      - name: "Setup - Terraform CLI"
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.10.5"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_DEFAULT_REGION }}

      - name: Configure GCloud Credentials
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}

      - name: "Set up Cloud SDK"
        uses: "google-github-actions/setup-gcloud@v2"

      - name: "Run - Terraform Init"
        run: terraform init -input=false
        working-directory: ${{ inputs.directory }}

      - name: "Run - Terraform Apply"
        run: terraform apply -input=false -auto-approve
        working-directory: ${{ inputs.directory }}
        env:
          DD_API_KEY: ${{ secrets.DD_API_KEY }}
          DD_APP_KEY: ${{ secrets.DD_APP_KEY }}
