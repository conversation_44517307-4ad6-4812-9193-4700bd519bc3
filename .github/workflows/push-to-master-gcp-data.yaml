name: "Terraform GCP Apply (data)"

on:
  push:
    branches:
      - master
    paths:
      - "environments/data-gcp/**"
      - "modules-gcp/**"
      - ".github/workflows/push-to-master-gcp-data.yaml"
      - ".github/workflows/tf-apply.yaml"

jobs:
  data-gcp-apply:
    concurrency: gcp_data_environment
    uses: ./.github/workflows/tf-apply.yaml
    with:
      directory: "./environments/data-gcp"
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: ${{ secrets.AWS_DEFAULT_REGION }}
      GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_APPLICATION_CREDENTIALS }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      DD_APP_KEY: ${{ secrets.DD_APP_KEY }}