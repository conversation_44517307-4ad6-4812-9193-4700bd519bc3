/* NOTE: This procedure will only work when connecting two VPCs within the same account */

# resource "aws_vpc_peering_connection" "peering_connection" {
#   vpc_id      = var.origin.vpc_id
#   peer_vpc_id = var.remote.vpc_id
#   auto_accept = true
#   tags = {
#     Name = "peering_${var.origin.name}_with_${var.remote.name}"
#   }
# }

# resource "aws_vpc_peering_connection_options" "peering_options" {
#   vpc_peering_connection_id = aws_vpc_peering_connection.peering_connection.id

#   accepter {
#     allow_remote_vpc_dns_resolution = true
#   }

#   requester {
#     allow_vpc_to_remote_classic_link = false
#     allow_classic_link_to_remote_vpc = false
#   }
# }

# resource "aws_route" "peering_route_origin" {
#   count                     = length(var.origin.route_table_ids)
#   route_table_id            = var.origin.route_table_ids[count.index]
#   destination_cidr_block    = var.remote.cidr
#   vpc_peering_connection_id = aws_vpc_peering_connection.peering_connection.id
# }

# resource "aws_route" "peering_route_remote" {
#   count                     = length(var.remote.route_table_ids)
#   route_table_id            = var.remote.route_table_ids[count.index]
#   destination_cidr_block    = var.origin.cidr
#   vpc_peering_connection_id = aws_vpc_peering_connection.peering_connection.id
# }

resource "google_compute_network_peering" "a-to-b" {
  name         = "${var.vpc1.name}-to-${var.vpc2.name}"
  network      = var.vpc1.link
  peer_network = var.vpc2.link

  export_custom_routes = true
  import_custom_routes = true
}

resource "google_compute_network_peering" "b-to-a" {
  name         = "${var.vpc2.name}-to-${var.vpc1.name}"
  network      = var.vpc2.link
  peer_network = var.vpc1.link

  export_custom_routes = true
  import_custom_routes = true
}
