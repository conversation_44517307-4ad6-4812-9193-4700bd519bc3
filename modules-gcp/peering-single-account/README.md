<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 vpc1  = 
	 vpc2  = 
}
```
## Resources

| Name | Type |
|------|------|
| [google_compute_network_peering.a-to-b](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_network_peering) | resource |
| [google_compute_network_peering.b-to-a](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_network_peering) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_vpc1"></a> [vpc1](#input\_vpc1) | n/a | <pre>object({<br>    name = string<br>    link = string<br>  })</pre> | n/a | yes |
| <a name="input_vpc2"></a> [vpc2](#input\_vpc2) | n/a | <pre>object({<br>    name = string<br>    link = string<br>  })</pre> | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_peering_a"></a> [peering\_a](#output\_peering\_a) | n/a |
| <a name="output_peering_b"></a> [peering\_b](#output\_peering\_b) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->