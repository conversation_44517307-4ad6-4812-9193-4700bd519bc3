resource "google_compute_instance_template" "template" {
  count        = local.instance_template_location == "global" ? 1 : 0
  name         = var.force_names.instance_template != null ? local.instance_template_name : null
  name_prefix  = var.force_names.instance_template == null ? "${local.instance_template_name}-" : null
  machine_type = var.machine_type

  disk {
    source_image = var.source_image
    auto_delete  = true
    boot         = true
  }

  metadata_startup_script = var.metadata_startup_script

  service_account {
    email  = google_service_account.ids.email
    scopes = ["cloud-platform"]
  }

  network_interface {
    subnetwork = var.subnetwork
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [disk.0.source_image]
  }
}

resource "google_compute_region_instance_template" "template" {
  count        = local.instance_template_location != "global" ? 1 : 0
  name         = var.force_names.instance_template != null ? local.instance_template_name : null
  name_prefix  = var.force_names.instance_template == null ? "${local.instance_template_name}-" : null
  machine_type = var.machine_type
  region       = local.instance_template_location

  disk {
    source_image = var.source_image
    auto_delete  = true
    boot         = true
  }

  metadata_startup_script = var.metadata_startup_script

  service_account {
    email  = google_service_account.ids.email
    scopes = ["cloud-platform"]
  }

  network_interface {
    subnetwork = var.subnetwork
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [disk.0.source_image]
  }
}

resource "google_compute_instance_group_manager" "igm" {
  count = local.is_zonal ? 1 : 0

  name               = local.instance_group_name
  base_instance_name = local.instance_group_name
  target_size        = 1
  zone               = local.instance_group_location

  version {
    name              = "${var.app_name}-igm-tf"
    instance_template = local.instance_template_location == "global" ? google_compute_instance_template.template[0].id : google_compute_region_instance_template.template[0].id
  }

  auto_healing_policies {
    health_check      = local.health_check_id
    initial_delay_sec = 300
  }
}

resource "google_compute_region_instance_group_manager" "igm" {
  count = local.is_regional ? 1 : 0

  name               = local.instance_group_name
  base_instance_name = local.instance_group_name
  region             = local.instance_group_location
  target_size        = 1

  version {
    name              = "${var.app_name}-igm-tf"
    instance_template = local.instance_template_location == "global" ? google_compute_instance_template.template[0].id : google_compute_region_instance_template.template[0].id
  }

  auto_healing_policies {
    health_check      = local.health_check_id
    initial_delay_sec = 300
  }
}
