## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_google"></a> [google](#provider\_google) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [google_compute_address.tcp_ip](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_address) | resource |
| [google_compute_address.udp_ip](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_address) | resource |
| [google_compute_forwarding_rule.tcp_internal](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule) | resource |
| [google_compute_forwarding_rule.udp_internal](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule) | resource |
| [google_compute_global_forwarding_rule.tcp_internal](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_forwarding_rule) | resource |
| [google_compute_global_forwarding_rule.udp_internal](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_global_forwarding_rule) | resource |
| [google_compute_health_check.health_check](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_health_check) | resource |
| [google_compute_instance_group_manager.igm](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_instance_group_manager) | resource |
| [google_compute_instance_template.template](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_instance_template) | resource |
| [google_compute_region_backend_service.tcp_backend](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_backend_service) | resource |
| [google_compute_region_backend_service.udp_backend](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_backend_service) | resource |
| [google_compute_region_instance_group_manager.igm](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_instance_group_manager) | resource |
| [google_compute_region_instance_template.template](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_instance_template) | resource |
| [google_compute_region_health_check.health_check](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_region_health_check) | resource |
| [google_project_iam_binding.log_writer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_binding) | resource |
| [google_project_iam_binding.metric_writer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/project_iam_binding) | resource |
| [google_service_account.ids](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/service_account) | resource |
| [google_project.current](https://registry.terraform.io/providers/hashicorp/google/latest/docs/data-sources/project) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_app_name"></a> [app\_name](#input\_app\_name) | Name of the application | `string` | n/a | yes |
| <a name="input_force_names"></a> [force\_names](#input\_force\_names) | Force the name of each resource; useful when importing existing resources | `object({...})` | `{}` | no |
| <a name="input_health_check"></a> [health\_check](#input\_health\_check) | Health check configuration for the service | `object({...})` | `{type="TCP", port="8080", ...}` | no |
| <a name="input_load_balancer"></a> [load\_balancer](#input\_load\_balancer) | Load balancer protocol type | `string` | `"NONE"` | no |
| <a name="input_locations"></a> [locations](#input\_locations) | Location for each resource; useful when importing existing resources | `object({...})` | `{instance_group="us-central1-c", instance_template="us-central1", backend_service="global", health_check="global", forwarding_rule="global", ip_address="us-central1"}` | no |
| <a name="input_machine_type"></a> [machine\_type](#input\_machine\_type) | Machine type for the VM instance | `string` | `"e2-standard-2"` | no |
| <a name="input_metadata_startup_script"></a> [metadata\_startup\_script](#input\_metadata\_startup\_script) | Startup script for the VM instance | `string` | `null` | no |
| <a name="input_network_id"></a> [network\_id](#input\_network\_id) | Network for the VM instance | `string` | n/a | yes |
| <a name="input_source_image"></a> [source\_image](#input\_source\_image) | Source image for the VM instance | `string` | `"ubuntu-os-cloud/ubuntu-2404-lts-amd64"` | no |
| <a name="input_subnetwork"></a> [subnetwork](#input\_subnetwork) | Subnetwork for the VM instance | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_health_check_id"></a> [health\_check\_id](#output\_health\_check\_id) | Health check for the instance group manager |
| <a name="output_tcp_backend_service_id"></a> [tcp\_backend\_service\_id](#output\_tcp\_backend\_service\_id) | Backend service for the instance group manager |
| <a name="output_tcp_ip_address"></a> [tcp\_ip\_address](#output\_tcp\_ip\_address) | IP address for the load balancer |
| <a name="output_udp_backend_service_id"></a> [udp\_backend\_service\_id](#output\_udp\_backend\_service\_id) | Backend service for the instance group manager |
| <a name="output_udp_ip_address"></a> [udp\_ip\_address](#output\_udp\_ip\_address) | IP address for the load balancer |
