output "tcp_backend_service_id" {
  description = "Backend service for the instance group manager"
  value       = local.tcp_lb_enabled ? (local.tcp_backend_service_location == "global" ? google_compute_backend_service.tcp_backend[0].id : google_compute_region_backend_service.tcp_backend[0].id) : null
}

output "udp_backend_service_id" {
  description = "Backend service for the instance group manager"
  value       = local.udp_lb_enabled ? google_compute_region_backend_service.udp_backend[0].id : null
}

output "tcp_ip_address" {
  description = "IP address for the load balancer"
  value       = local.tcp_lb_enabled ? google_compute_address.tcp_ip[0].address : null
}

output "udp_ip_address" {
  description = "IP address for the load balancer"
  value       = local.udp_lb_enabled ? google_compute_address.udp_ip[0].address : null
}
