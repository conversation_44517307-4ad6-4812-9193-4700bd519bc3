resource "google_compute_backend_service" "tcp_backend" {
  count = local.tcp_backend_service_location == "global" && local.tcp_lb_enabled ? 1 : 0

  name          = local.tcp_backend_name
  health_checks = [local.health_check_id]
  protocol      = "TCP"
  backend {
    group          = local.is_zonal ? google_compute_instance_group_manager.igm[0].instance_group : google_compute_region_instance_group_manager.igm[0].instance_group
    balancing_mode = "CONNECTION"
  }
}

# UDP protocol is not supported by global backend services
# UDP backend services are only created regionally

resource "google_compute_region_backend_service" "tcp_backend" {
  count = local.tcp_backend_service_location != "global" && local.tcp_lb_enabled ? 1 : 0

  name          = local.tcp_backend_name
  region        = local.tcp_backend_service_location
  health_checks = [local.health_check_id]
  protocol      = "TCP"
  backend {
    group          = local.is_zonal ? google_compute_instance_group_manager.igm[0].instance_group : google_compute_region_instance_group_manager.igm[0].instance_group
    balancing_mode = "CONNECTION"
  }
}

resource "google_compute_region_backend_service" "udp_backend" {
  count = local.udp_lb_enabled ? 1 : 0

  name          = local.udp_backend_name
  region        = local.udp_backend_service_location
  health_checks = [local.health_check_id]
  protocol      = "UDP"
  backend {
    group          = local.is_zonal ? google_compute_instance_group_manager.igm[0].instance_group : google_compute_region_instance_group_manager.igm[0].instance_group
    balancing_mode = "UTILIZATION"
  }
}
