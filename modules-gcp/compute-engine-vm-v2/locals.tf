locals {
  tcp_lb_enabled = contains(["TCP", "BOTH"], var.load_balancer)
  udp_lb_enabled = contains(["UDP", "BOTH"], var.load_balancer)

  # Location detection
  instance_group_location     = lower(var.locations.instance_group)
  instance_template_location  = lower(var.locations.instance_template)
  backend_service_location    = lower(var.locations.backend_service)
  health_check_location      = lower(var.locations.health_check)
  forwarding_rule_location   = lower(var.locations.forwarding_rule)
  ip_address_location       = lower(var.locations.ip_address)

  # Scope detection
  is_zonal = can(regex("^(us|europe|asia|australia|southamerica|northamerica)-(central|east|west|south|north)[1-9]-[a-f]$", local.instance_group_location))
  is_regional = can(regex("^(us|europe|asia|australia|southamerica|northamerica)-(central|east|west|south|north)[1-9]$", local.instance_group_location))
  is_global = local.instance_group_location == "global"

  # UDP backend services must be regional (global backend services don't support UDP)
  tcp_backend_service_location = local.backend_service_location
  udp_backend_service_location = local.backend_service_location == "global" && local.udp_lb_enabled ? (local.is_zonal ? substr(local.instance_group_location, 0, length(local.instance_group_location) - 2) : "us-central1") : local.backend_service_location

  # UDP forwarding rules must match UDP backend service location
  tcp_forwarding_rule_location = local.forwarding_rule_location
  udp_forwarding_rule_location = local.udp_backend_service_location

  # Resource naming
  instance_group_name = var.force_names.instance_group_manager != null ? var.force_names.instance_group_manager : "${var.app_name}-igm"
  instance_template_name = var.force_names.instance_template != null ? var.force_names.instance_template : "${var.app_name}-template"
  health_check_name = var.force_names.health_check != null ? var.force_names.health_check : "${var.app_name}-healthcheck"
  
  # Backend service names
  tcp_backend_name = coalesce(try(var.force_names.backend_service.tcp, null), "${var.app_name}-svc-tcp")
  udp_backend_name = coalesce(try(var.force_names.backend_service.udp, null), "${var.app_name}-svc-udp")

  # Forwarding rule names
  tcp_forwarding_rule_name = coalesce(try(var.force_names.forwarding_rule.tcp, null), "${var.app_name}-endpoint")
  udp_forwarding_rule_name = coalesce(try(var.force_names.forwarding_rule.udp, null), "${var.app_name}-udp-fwd")

  # IP address names
  tcp_ip_name = coalesce(try(var.force_names.ip_addresses.tcp, null), "${var.app_name}-ilb-tcp-ip")
  udp_ip_name = coalesce(try(var.force_names.ip_addresses.udp, null), "${var.app_name}-ilb-udp-ip")
}

data "google_project" "current" {}
