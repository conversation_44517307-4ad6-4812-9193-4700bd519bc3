locals {
  health_check_type = upper(var.health_check.type)
  health_check_id   = var.locations.health_check == "global" ? google_compute_health_check.health_check[0].id : google_compute_region_health_check.health_check[0].id
}

output "health_check_id" {
  value       = local.health_check_location == "global" ? google_compute_health_check.health_check[0].id : google_compute_region_health_check.health_check[0].id
  description = "Health check for the instance group manager"
}

resource "google_compute_health_check" "health_check" {
  count = local.health_check_location == "global" ? 1 : 0
  name  = local.health_check_name

  check_interval_sec  = var.health_check.check_interval_sec
  timeout_sec         = var.health_check.timeout_sec
  healthy_threshold   = var.health_check.healthy_threshold
  unhealthy_threshold = var.health_check.unhealthy_threshold

  dynamic "tcp_health_check" {
    for_each = local.health_check_type == "TCP" ? [var.health_check] : []
    content {
      port = tcp_health_check.value.port
    }
  }
  dynamic "http_health_check" {
    for_each = local.health_check_type == "HTTP" ? [var.health_check] : []
    content {
      port         = http_health_check.value.port
      request_path = http_health_check.value.request_path
    }
  }
}

resource "google_compute_region_health_check" "health_check" {
  count  = local.health_check_location != "global" ? 1 : 0
  name   = local.health_check_name
  region = local.health_check_location

  check_interval_sec  = var.health_check.check_interval_sec
  timeout_sec         = var.health_check.timeout_sec
  healthy_threshold   = var.health_check.healthy_threshold
  unhealthy_threshold = var.health_check.unhealthy_threshold

  dynamic "tcp_health_check" {
    for_each = local.health_check_type == "TCP" ? [var.health_check] : []
    content {
      port = tcp_health_check.value.port
    }
  }

  dynamic "http_health_check" {
    for_each = local.health_check_type == "HTTP" ? [var.health_check] : []
    content {
      port         = http_health_check.value.port
      request_path = http_health_check.value.request_path
    }
  }
}
