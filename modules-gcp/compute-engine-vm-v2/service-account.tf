resource "google_service_account" "ids" {
  account_id   = "${var.app_name}-sa"
  display_name = "${var.app_name} service account"
}

resource "google_project_iam_binding" "log_writer" {
  project = data.google_project.current.project_id
  role    = "roles/logging.logWriter"
  members = [
    "serviceAccount:${google_service_account.ids.email}"
  ]
}

resource "google_project_iam_binding" "metric_writer" {
  project = data.google_project.current.project_id
  role    = "roles/monitoring.metricWriter"
  members = [
    "serviceAccount:${google_service_account.ids.email}"
  ]
}
