variable "app_name" {
  type        = string
  description = "Name of the application"
}

variable "locations" {
  type = object({
    instance_group    = optional(string, "global")
    instance_template = optional(string, "global")
    backend_service   = optional(string, "global")
    health_check      = optional(string, "global")
    forwarding_rule   = optional(string, "global")
    ip_address       = optional(string, "global")
  })
  default = {
    instance_group    = "us-central1-c"
    instance_template = "us-central1"
    backend_service   = "global"
    health_check      = "global"
    forwarding_rule   = "global"
    ip_address       = "us-central1"
  }
  description = "Location for each resource; useful when importing existing resources"
  validation {
    condition = alltrue([
      for loc in [var.locations.instance_group, var.locations.instance_template, var.locations.backend_service, var.locations.health_check, var.locations.forwarding_rule, var.locations.ip_address] :
      loc == "global" || can(regex("^(us|europe|asia|australia|southamerica|northamerica)-(central|east|west|south|north)[1-9](-[a-f])?$", loc))
    ])
    error_message = "Each location must be either 'global', a valid GCP region (e.g., us-central1, europe-west1) or a valid GCP zone (e.g., us-central1-a)"
  }
}

variable "force_names" {
  type = object({
    instance_group_manager  = optional(string)
    instance_template      = optional(string)
    backend_service        = optional(object({
      tcp = optional(string)
      udp = optional(string)
    }))
    health_check           = optional(string)
    forwarding_rule        = optional(object({
      tcp = optional(string)
      udp = optional(string)
    }))
    ip_addresses           = optional(object({
      tcp = optional(string)
      udp = optional(string)
    }))
  })
  default = {
    instance_group_manager = null
    instance_template     = null
    backend_service       = null
    health_check          = null
    forwarding_rule       = null
    ip_addresses         = null
  }
  description = "Force the name of each resource; useful when importing existing resources"
}

variable "network_id" {
  type        = string
  description = "Network for the VM instance"
}

# variable "ip_addresses" {
#   type = object({
#     tcp = optional(object({
#       region  = optional(string, "global")
#       type    = optional(string, "INTERNAL")
#     }))
#   })
# }

variable "health_check" {
  type = object({
    type                = optional(string, "TCP")
    port                = optional(string, "8080")
    request_path        = optional(string, "/health")
    check_interval_sec  = optional(number, 300)
    timeout_sec         = optional(number, 20)
    healthy_threshold   = optional(number, 2)
    unhealthy_threshold = optional(number, 2)
    log_config          = optional(object({
      enable      = optional(bool, false)
      sample_rate = optional(number, 0)
    }), null)
  })
  default = {
    type                = "TCP"
    port                = "8080"
    check_interval_sec  = 300
    timeout_sec         = 20
    healthy_threshold   = 2
    unhealthy_threshold = 2
    log_config          = null
  }
  description = "Health check configuration for the service"
  validation {
    condition     = contains(["TCP", "HTTP"], upper(var.health_check.type))
    error_message = "Allowed values are TCP or HTTP."
  }
  validation {
    condition     = upper(var.health_check.type) != "HTTP" || (upper(var.health_check.type) == "HTTP" && var.health_check.request_path != null)
    error_message = "When health_check.type is HTTP, request_path must be specified."
  }
}

variable "subnetwork" {
  type        = string
  description = "Subnetwork for the VM instance"
}

variable "source_image" {
  type        = string
  description = "Source image for the VM instance"
  default     = "ubuntu-os-cloud/ubuntu-2404-lts-amd64"
}

variable "machine_type" {
  type        = string
  description = "Machine type for the VM instance"
  default     = "e2-standard-2"
}

variable "metadata_startup_script" {
  type        = string
  description = "Startup script for the VM instance"
  default     = null
}

variable "load_balancer" {
  description = "Load balancer protocol type"
  type        = string
  default     = "NONE"
  validation {
    condition     = contains(["TCP", "UDP", "BOTH", "NONE"], var.load_balancer)
    error_message = "Allowed values are TCP, UDP, BOTH or NONE."
  }
}
