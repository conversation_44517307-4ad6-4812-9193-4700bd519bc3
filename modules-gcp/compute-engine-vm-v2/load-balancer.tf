# there is not a load balancer resource, you have to define each part separately
# https://cloud.google.com/load-balancing/docs/l7-internal/setting-up-l7-cross-reg-internal#lb-config


resource "google_compute_address" "tcp_ip" {
  count        = local.tcp_lb_enabled ? 1 : 0
  name         = local.tcp_ip_name
  address_type = "INTERNAL"
  subnetwork   = var.subnetwork
  region       = local.ip_address_location != "global" ? local.ip_address_location : null
}

resource "google_compute_global_forwarding_rule" "tcp_internal" {
  count = local.tcp_backend_service_location == "global" && local.tcp_lb_enabled ? 1 : 0

  target                = google_compute_backend_service.tcp_backend[0].id
  name                  = local.tcp_forwarding_rule_name
  load_balancing_scheme = "INTERNAL_SELF_MANAGED"
  ip_protocol           = "TCP"
  ip_address            = google_compute_address.tcp_ip[0].address
  network               = var.network_id
  subnetwork            = var.subnetwork
}

resource "google_compute_forwarding_rule" "tcp_internal" {
  count = local.tcp_backend_service_location != "global" && local.tcp_lb_enabled ? 1 : 0

  name                  = local.tcp_forwarding_rule_name
  load_balancing_scheme = "INTERNAL"
  ip_protocol           = "TCP"
  backend_service       = google_compute_region_backend_service.tcp_backend[0].id
  ip_address            = google_compute_address.tcp_ip[0].address
  all_ports             = true
  network               = var.network_id
  subnetwork            = var.subnetwork
  region                = local.tcp_forwarding_rule_location
}

resource "google_compute_address" "udp_ip" {
  count        = local.udp_lb_enabled ? 1 : 0
  name         = local.udp_ip_name
  address_type = "INTERNAL"
  subnetwork   = var.subnetwork
  region       = local.ip_address_location != "global" ? local.ip_address_location : null
}

# UDP global forwarding rules are not supported since UDP global backend services don't exist

resource "google_compute_forwarding_rule" "udp_internal" {
  count = local.udp_lb_enabled ? 1 : 0

  name                  = local.udp_forwarding_rule_name
  load_balancing_scheme = "INTERNAL"
  ip_protocol           = "UDP"
  backend_service       = google_compute_region_backend_service.udp_backend[0].id
  ip_address            = google_compute_address.udp_ip[0].address
  all_ports             = true
  network               = var.network_id
  subnetwork            = var.subnetwork
  region                = local.udp_backend_service_location
}
