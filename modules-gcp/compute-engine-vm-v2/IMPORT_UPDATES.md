# Module Updates for Import Compatibility

## Overview
This document outlines the changes made to the `compute-engine-vm-v2` module to support importing existing GCP resources with flexible location configurations.

## Key Changes Made

### 1. Enhanced Location Support
- **Updated default locations** in `variables.tf`:
  - `instance_group`: Changed from `"us-central1"` to `"us-central1-c"` (zonal)
  - `instance_template`: Changed from `"global"` to `"us-central1"` (regional)  
  - `ip_address`: Changed from `"global"` to `"us-central1"` (regional)
- All resources now support global, regional, and zonal deployments as appropriate

### 2. Resource Naming Flexibility
- **Fixed forwarding rule naming** in `locals.tf`:
  - TCP forwarding rule default changed from `"${var.app_name}-tcp-fwd"` to `"${var.app_name}-endpoint"`
- **Enhanced instance template naming** in `instance-group.tf`:
  - Uses exact `name` when `force_names.instance_template` is provided
  - Uses `name_prefix` with suffix when no forced name is specified

### 3. Load Balancer Logic Improvements
- **Updated backend service creation** in `service.tf`:
  - Added TCP/UDP load balancer enablement checks to resource counts
  - TCP backend services only created when `local.tcp_lb_enabled = true`
  - UDP backend services only created when `local.udp_lb_enabled = true`
  - **UDP backend services are always regional** (GCP limitation: global backend services don't support UDP protocol)
- **Enhanced output safety** in `outputs.tf`:
  - Backend service outputs now check if load balancer is enabled before referencing resources
- **Automatic location handling** in `locals.tf`:
  - UDP backend services automatically use regional location even when global is specified
  - When zonal instance group is used, extracts region from zone for UDP services

### 4. Health Check Compatibility
- Health checks support both global and regional deployment
- Automatic selection based on `locations.health_check` configuration

### 5. Documentation Updates
- **Updated README.md** to reflect:
  - New resource types (regional variants)
  - Updated input variables and defaults
  - New output variables (`health_check_id`)

## Import Configuration Example

The module now supports importing resources like the keyget example:

```hcl
locations = {
  instance_group    = "us-central1-c"    # zonal
  backend_service   = "global"           # global for TCP, auto-converted to regional for UDP
  instance_template = "us-central1"      # regional
  health_check      = "global"           # global
  forwarding_rule   = "global"           # global
  ip_address       = "us-central1"       # regional
}

force_names = {
  instance_group_manager = "keyget-prod-instance-group"
  instance_template     = "keyget-prod-instance-v4-dd-updated"
  health_check          = "healthcheck"
  backend_service = {
    tcp = "keyget-be"
    udp = null
  }
  forwarding_rule = {
    tcp = "keyget-endpoint"
    udp = null
  }
  ip_addresses = {
    tcp = "keyget-global-ip"
    udp = null
  }
}
```

## Compatible Import Statements

The module now supports importing the following resource patterns:

- `google_compute_region_instance_template.template[0]` (regional templates)
- `google_compute_health_check.health_check[0]` (global health checks)
- `google_compute_global_forwarding_rule.tcp_internal[0]` (global TCP forwarding rules)
- `google_compute_address.tcp_ip[0]` (regional IP addresses)
- `google_compute_instance_group_manager.igm[0]` (zonal instance group managers)
- `google_compute_backend_service.tcp_backend[0]` (global TCP backend services)
- `google_compute_region_backend_service.udp_backend[0]` (regional UDP backend services only)

## Important Constraints

- **UDP Backend Services**: Must be regional due to GCP limitations. Even if `backend_service = "global"` is specified, UDP services will automatically be created regionally.
- **UDP Forwarding Rules**: Must match the region of UDP backend services.

## Validation

All changes maintain backward compatibility while adding the flexibility needed for importing existing infrastructure. The module automatically selects the appropriate resource type (global vs regional vs zonal) based on the location configuration provided.