variable "composer" {
  type = object({
    name   = string
    region = string
  })
}

variable "workers" {
  type = object({
    cpu              = number # cores
    memory           = number # gb
    storage          = number # gb
    max_workers      = number # default: 3
    environment_size = string
    # ENVIRONMENT_SIZE_SMALL, ENVIRONMENT_SIZE_MEDIUM, ENVIRONMENT_SIZE_LARGE
    # https://cloud.google.com/composer/docs/composer-2/optimize-environments#presets
    # https://cloud.google.com/composer/docs/composer-2/scale-environments#environment-size
  })
  default = {
    cpu              = 0.5
    memory           = 1.875
    storage          = 1
    max_workers      = 3
    environment_size = "ENVIRONMENT_SIZE_SMALL"
  }
}

variable "network" {
  type = object({
    network    = string
    subnetwork = string
    cidrs = object({
      master_cidr_block         = string
      cloud_sql_cidr_block      = string
      cluster_cidr_block        = string
      services_cidr_block       = string
      cloud_composer_cidr_block = string
    })
  })
}

variable "image_version" {
  type    = string
  default = "composer-2.1.11-airflow-2.4.3"
}

variable "pypi_packages" {
  type    = map(string)
  default = {}
}

variable "dags_are_paused_at_creation" {
  type    = string
  default = null
}
