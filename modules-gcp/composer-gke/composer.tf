resource "google_composer_environment" "composer" {
  name   = var.composer.name
  region = var.composer.region

  lifecycle {
    ignore_changes = [
      config[0].software_config[0].pypi_packages,
      config[0].software_config[0].env_variables
    ]
  }

  config {
    software_config {
      image_version = var.image_version

      airflow_config_overrides = {
        // core-dags_are_paused_at_creation = "True"
        core-killed_task_cleanup_time       = "300"
        scheduler-catchup_by_default        = "False"
        core-dagbag_import_timeout          = "240"
        core-enable_xcom_pickling           = "True"
        core-dag_file_processor_timeout     = "400"
        scheduler-min_file_process_interval = "200"
        core-dags_are_paused_at_creation    = var.dags_are_paused_at_creation
      }

      pypi_packages = var.pypi_packages

      env_variables = {
        // FOO = "bar"
        PYTHONPATH = "/home/<USER>/gcs/dags"
      }
    }

    master_authorized_networks_config {
      enabled = true
      cidr_blocks {
        display_name = "internal network"
        cidr_block   = "10.0.0.0/8"
      }
    }

    private_environment_config {
      enable_private_endpoint = true

      master_ipv4_cidr_block                 = var.network.cidrs.master_cidr_block
      cloud_sql_ipv4_cidr_block              = var.network.cidrs.cloud_sql_cidr_block
      cloud_composer_network_ipv4_cidr_block = var.network.cidrs.cloud_composer_cidr_block
    }

    workloads_config {
      scheduler {
        cpu        = 2
        memory_gb  = 2
        storage_gb = 1
        count      = 1
      }
      web_server {
        cpu        = 1
        memory_gb  = 2
        storage_gb = 1
      }
      worker {
        cpu        = var.workers.cpu
        memory_gb  = var.workers.memory
        storage_gb = var.workers.storage
        min_count  = 1
        max_count  = var.workers.max_workers
      }
    }

    environment_size = var.workers.environment_size

    node_config {
      network    = var.network.network
      subnetwork = var.network.subnetwork
      ip_allocation_policy {
        cluster_ipv4_cidr_block  = var.network.cidrs.cluster_cidr_block
        services_ipv4_cidr_block = var.network.cidrs.services_cidr_block
      }
    }

    maintenance_window {
      start_time = "2022-01-01T03:00:00Z"
      end_time   = "2022-01-01T07:00:00Z"
      recurrence = "FREQ=WEEKLY;BYDAY=FR,SA,SU"
    }
  }
}
