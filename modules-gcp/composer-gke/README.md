<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 composer  = 
	 network  = 

	 # Optional variables
	 image_version  = "composer-2.1.11-airflow-2.4.3"
	 pypi_packages  = {}
	 workers  = {
  "cpu": 0.5,
  "environment_size": "ENVIRONMENT_SIZE_SMALL",
  "max_workers": 3,
  "memory": 1.875,
  "storage": 1
}
}
```
## Resources

| Name | Type |
|------|------|
| [google_composer_environment.composer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/composer_environment) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_composer"></a> [composer](#input\_composer) | n/a | <pre>object({<br>    name   = string<br>    region = string<br>  })</pre> | n/a | yes |
| <a name="input_image_version"></a> [image\_version](#input\_image\_version) | n/a | `string` | `"composer-2.1.11-airflow-2.4.3"` | no |
| <a name="input_network"></a> [network](#input\_network) | n/a | <pre>object({<br>    network    = string<br>    subnetwork = string<br>    cidrs = object({<br>      master_cidr_block         = string<br>      cloud_sql_cidr_block      = string<br>      cluster_cidr_block        = string<br>      services_cidr_block       = string<br>      cloud_composer_cidr_block = string<br>    })<br>  })</pre> | n/a | yes |
| <a name="input_pypi_packages"></a> [pypi\_packages](#input\_pypi\_packages) | n/a | `map(string)` | `{}` | no |
| <a name="input_workers"></a> [workers](#input\_workers) | n/a | <pre>object({<br>    cpu              = number # cores<br>    memory           = number # gb<br>    storage          = number # gb<br>    max_workers      = number # default: 3<br>    environment_size = string<br>    # ENVIRONMENT_SIZE_SMALL, ENVIRONMENT_SIZE_MEDIUM, ENVIRONMENT_SIZE_LARGE<br>    # https://cloud.google.com/composer/docs/composer-2/optimize-environments#presets<br>    # https://cloud.google.com/composer/docs/composer-2/scale-environments#environment-size<br>  })</pre> | <pre>{<br>  "cpu": 0.5,<br>  "environment_size": "ENVIRONMENT_SIZE_SMALL",<br>  "max_workers": 3,<br>  "memory": 1.875,<br>  "storage": 1<br>}</pre> | no |
## Outputs

No outputs.
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->