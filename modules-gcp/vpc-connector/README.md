<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 connector  = 
}
```
## Resources

| Name | Type |
|------|------|
| [google_vpc_access_connector.connector](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/vpc_access_connector) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_connector"></a> [connector](#input\_connector) | n/a | <pre>object({<br>    name    = string<br>    cidr    = string<br>    network = string<br>  })</pre> | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_vpc-conn"></a> [vpc-conn](#output\_vpc-conn) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->