data "google_compute_default_service_account" "default" {
}

resource "google_compute_instance" "vm" {
  name         = var.instance_name
  machine_type = var.instance_type
  zone         = var.availability_zone

  tags = var.tags

  boot_disk {
    initialize_params {
      size  = var.volume_size
      type  = var.volume_type
      image = var.image_version
    }
  }

  // Local SSD disk
  # scratch_disk {
  #   interface = "SCSI"
  # }

  network_interface {
    subnetwork = var.subnetwork

    access_config {
      // Ephemeral public IP
    }
  }

  metadata_startup_script = var.metadata_startup_script

  allow_stopping_for_update = true

  service_account {
    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    email = data.google_compute_default_service_account.default.email
    scopes = [
      "https://www.googleapis.com/auth/devstorage.read_only",
      "https://www.googleapis.com/auth/logging.write",
      "https://www.googleapis.com/auth/monitoring.write",
      "https://www.googleapis.com/auth/service.management.readonly",
      "https://www.googleapis.com/auth/servicecontrol",
      "https://www.googleapis.com/auth/trace.append",
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }

  lifecycle {
    ignore_changes = [metadata_startup_script, boot_disk, metadata]
  }
}
