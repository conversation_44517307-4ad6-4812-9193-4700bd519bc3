variable "instance_name" {
  type = string
}

variable "availability_zone" {
  type = string
}

variable "subnetwork" {
  type = string
}

# https://cloud.google.com/compute/docs/general-purpose-machines
variable "instance_type" {
  type    = string
  default = "e2-micro"
}

# https://console.cloud.google.com/compute/images?project=keyway-data
# ubuntu 22.04 x86 => ubuntu-2204-lts
# ubuntu 22.04 arm64 => ubuntu-2204-lts-arm64
variable "image_version" {
  type    = string
  default = "ubuntu-2404-lts-amd64"
}

variable "tags" {
  type    = list(string)
  default = []
}

variable "ssh_keys" {
  type    = string
  default = ""
}

# example: volume_size = 20 (GiB)
variable "volume_size" {
  type    = number
  default = 20
}

# https://cloud.google.com/compute/docs/disks#introduction
# https://cloud.google.com/compute/disks-image-pricing#disk
variable "volume_type" {
  type    = string
  default = "pd-standard"
}

# example: metadata_startup_script = "echo 'some script goes here'; exit 0"
variable "metadata_startup_script" {
  default = null
  type    = string
}
