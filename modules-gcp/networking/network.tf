/*==== The VPC ======*/
resource "google_compute_network" "vpc_network" {
  name                    = "${var.vpc.name}-vpc"
  auto_create_subnetworks = false
  routing_mode            = "REGIONAL"
}

/*==== Subnets ======*/
resource "google_compute_subnetwork" "subnets" {
  count         = var.vpc.subnets
  network       = google_compute_network.vpc_network.name
  name          = "${var.vpc.name}-${var.vpc.region}-${count.index}"
  ip_cidr_range = cidrsubnet(var.vpc.cidr, ceil(var.vpc.subnets / 2), count.index)
  region        = var.vpc.region

  private_ip_google_access = var.private_ip_google_access

  log_config {
    aggregation_interval = "INTERVAL_5_SEC"
    flow_sampling        = 0.5
    metadata             = "INCLUDE_ALL_METADATA"
    filter_expr          = "!(connection.src_ip.startsWith(\"10.\") && connection.dest_ip.startsWith(\"10.\"))"
  }
}
