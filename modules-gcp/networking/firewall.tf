resource "google_compute_firewall" "private-ingress" {
  name      = "${var.vpc.name}-fw-private-ingress"
  network   = google_compute_network.vpc_network.name
  direction = "INGRESS"
  allow {
    protocol = "icmp"
  }
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  source_ranges = [
    "10.0.0.0/8" # internal network
  ]
}

resource "google_compute_firewall" "private-egress" {
  name      = "${var.vpc.name}-fw-private-egress"
  network   = google_compute_network.vpc_network.name
  direction = "EGRESS"
  allow {
    protocol = "icmp"
  }
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  destination_ranges = [
    "10.0.0.0/8" # internal network
  ]
}

resource "google_compute_firewall" "public-ingress" {
  name      = "${var.vpc.name}-fw-public-ingress"
  network   = google_compute_network.vpc_network.name
  direction = "INGRESS"
  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }
  source_ranges = ["0.0.0.0/0"]
}

resource "google_compute_firewall" "public-healthcheck" {
  name      = "${var.vpc.name}-fw-public-healthcheck"
  network   = google_compute_network.vpc_network.name
  direction = "INGRESS"
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  source_ranges = ["35.191.0.0/16", "130.211.0.0/22"]
}

resource "google_compute_firewall" "public-web-console-ssh" {
  name      = "${var.vpc.name}-fw-public-web-console-ssh"
  network   = google_compute_network.vpc_network.name
  direction = "INGRESS"
  allow {
    protocol = "tcp"
    ports    = ["22"]
  }
  source_ranges = ["35.235.240.0/20"]
}

resource "google_compute_firewall" "public-egress" {
  name      = "${var.vpc.name}-fw-public-egress"
  network   = google_compute_network.vpc_network.name
  direction = "EGRESS"
  allow {
    protocol = "icmp"
  }
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  destination_ranges = ["0.0.0.0/0"]
}
