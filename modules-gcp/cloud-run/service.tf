resource "google_cloud_run_service" "service" {
  name       = var.name
  location   = var.location
  depends_on = [google_project_iam_member.secret_manager]

  template {
    spec {
      timeout_seconds       = var.request_timeout
      container_concurrency = var.container_concurrency
      service_account_name  = google_service_account.cloud_run.email

      containers {
        name  = var.name
        image = var.docker_image

        dynamic "env" {
          for_each = var.environment_variables
          content {
            name  = env.key
            value = env.value
          }
        }

        dynamic "env" {
          for_each = var.environment_secrets
          content {
            name = env.key
            value_from {
              secret_key_ref {
                key  = "latest"
                name = env.value
              }
            }
          }
        }

        resources {
          limits = {
            cpu    = "${var.cpu}000m"
            memory = "${var.ram}Gi"
          }
        }

        startup_probe {
          initial_delay_seconds = 30
          timeout_seconds       = 3
          period_seconds        = 10
          failure_threshold     = 2
          tcp_socket {
            port = 8080
          }
        }

        liveness_probe {
          http_get {
            path = "/health"
          }
        }
      }
    }

    metadata {
      annotations = {
        "run.googleapis.com/vpc-access-connector" = var.vpc_connector_id,
        "client.knative.dev/user-image"           = var.docker_image,
        "run.googleapis.com/vpc-access-egress"    = "private-ranges-only",
        "autoscaling.knative.dev/minScale"        = var.min_scale,
        "autoscaling.knative.dev/maxScale"        = var.max_scale
        "run.googleapis.com/cpu-throttling"       = var.cpu_throttling,
        "run.googleapis.com/startup-cpu-boost"    = var.startup_cpu_boost,
      }
    }
  }

  metadata {
    annotations = {
      "run.googleapis.com/ingress"    = "all" # "internal-and-cloud-load-balancing",
      "client.knative.dev/user-image" = var.docker_image,
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  lifecycle {
    ignore_changes = [
      metadata[0].annotations["run.googleapis.com/client-name"],                # varies for the last user
      metadata[0].annotations["run.googleapis.com/client-version"],             # varies for the last user
      template[0].metadata[0].annotations["run.googleapis.com/client-name"],    # varies for the last user
      template[0].metadata[0].annotations["run.googleapis.com/client-version"], # varies for the last user
    ]
  }
}

data "google_iam_policy" "auth" {
  binding {
    role    = "roles/run.invoker"
    members = var.public_access ? ["allUsers"] : flatten(["serviceAccount:${google_service_account.cloud_run.email}", [compact(var.extra_service_accounts)]])
  }
}

resource "google_cloud_run_service_iam_policy" "proxy_binding" {
  location    = google_cloud_run_service.service.location
  project     = google_cloud_run_service.service.project
  service     = google_cloud_run_service.service.name
  policy_data = data.google_iam_policy.auth.policy_data
}
