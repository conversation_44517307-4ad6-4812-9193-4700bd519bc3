### General configuration

# Name of the application (must be unique within the project)
variable "name" {
  type = string
}

# Region where the application will be deployed
variable "location" {
  type = string
}

# Docker image to deploy (latest is implied)
variable "docker_image" {
  type = string
}

# ID of the VPC connector to attach to the application
variable "vpc_connector_id" {
  type = string
}

variable "public_access" {
  type    = bool
  default = false
}

# Service Accounts
variable "extra_service_accounts" {
  type    = list(string)
  default = []
}

# environment vars as key/value pair
variable "environment_variables" {
  type    = map(string)
  default = {}
}

# environment vars as key/secret_name pair
variable "environment_secrets" {
  type    = map(string)
  default = {}
}

# Cloud Function GS bucket
variable "cloud_function_bucket" {
  type = string
  # default = null
}

# suffix for the SSL and custom_domain
variable "custom_domains" {
  type    = list(string)
  default = []
}

### Autoscaling configuration

# Minimum number of instances to keep running
variable "min_scale" {
  type    = number
  default = 0
}

# Maximum number of instances to keep running
variable "max_scale" {
  type    = number
  default = 10
}

# CPU allocated to each instance
variable "cpu" {
  type    = number
  default = 1000
}

# RAM allocated to each instance
variable "ram" {
  type    = number
  default = 512
}

# Timeout for requests (in seconds)
variable "request_timeout" {
  type    = number
  default = 30
}

# Number of concurrent requests each container can handle
variable "container_concurrency" {
  type    = number
  default = 80
}

variable "project_name" {
  type    = string
  default = "keyway-data"
}

# whether to use more cpu on-boot
variable "startup_cpu_boost" {
  type    = bool
  default = true
}

# whether to allow cpu throttling
variable "cpu_throttling" {
  type    = bool
  default = false
}
