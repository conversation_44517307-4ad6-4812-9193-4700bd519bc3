# service account for cloud-run
resource "google_service_account" "cloud_run" {
  account_id   = "${var.name}-sa"
  display_name = "Service Account for ${var.name}-proxy"
}

# Attach the "Secret Manager Secret Accessor" role to the service account
resource "google_project_iam_member" "secret_manager" {
  project = var.project_name
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.cloud_run.email}"
}

resource "google_service_account_key" "cloud_run" {
  service_account_id = google_service_account.cloud_run.name
  public_key_type    = "TYPE_X509_PEM_FILE"
}
