resource "google_secret_manager_secret" "public_key" {
  secret_id = "${var.name}_cr-public-key"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret" "private_key" {
  secret_id = "${var.name}_cr-private-key"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "public_key" {
  secret      = google_secret_manager_secret.public_key.name
  secret_data = google_service_account_key.cloud_run.public_key
}

resource "google_secret_manager_secret_version" "private_key" {
  secret      = google_secret_manager_secret.private_key.name
  secret_data = google_service_account_key.cloud_run.private_key
}
