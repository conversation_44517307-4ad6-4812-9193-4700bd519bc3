resource "google_compute_router" "router" {
  name    = "${var.router.name}-router"
  network = var.router.network_name
  bgp {
    asn               = var.router.asn
    advertise_mode    = "CUSTOM"
    advertised_groups = ["ALL_SUBNETS"]
    dynamic "advertised_ip_ranges" {
      for_each = var.advertised_ip_ranges
      content {
        range = advertised_ip_ranges.value
      }
    }
  }
}

resource "google_compute_router_interface" "router_interface" {
  count      = length(var.router.aws_gateway_ips) == 4 ? 4 : 0
  name       = "${var.router.name}-router-interface-${count.index}"
  router     = google_compute_router.router.name
  region     = google_compute_router.router.region
  ip_range   = var.router.aws_gateway_ips[count.index].gcp_inside_range
  vpn_tunnel = element(google_compute_vpn_tunnel.vpn_conn_tunnel, count.index).name
}

resource "google_compute_router_peer" "peer" {
  count                     = length(var.router.aws_gateway_ips) == 4 ? 4 : 0
  name                      = "${var.router.name}-router-bgp-peer-${count.index}"
  router                    = google_compute_router.router.name
  region                    = google_compute_router.router.region
  peer_ip_address           = var.router.aws_gateway_ips[count.index].aws_inside_ip
  peer_asn                  = var.router.aws_gateway_ips[count.index].aws_asn
  advertised_route_priority = 100
  interface                 = element(google_compute_router_interface.router_interface, count.index).name
}

resource "google_compute_router_nat" "nat" {
  name                               = "router-nat"
  router                             = google_compute_router.router.name
  region                             = google_compute_router.router.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}
