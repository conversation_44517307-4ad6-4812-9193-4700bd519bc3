resource "google_compute_ha_vpn_gateway" "ha_gateway" {
  region  = var.router.region
  name    = "${var.router.name}-ha-vpn"
  network = var.router.network_name
}

resource "google_compute_external_vpn_gateway" "external_gateway" {
  count           = length(var.router.aws_gateway_ips) == 4 ? 1 : 0
  name            = "${var.router.name}-external-gateway"
  description     = "${var.router.name}-external-gateway"
  redundancy_type = "FOUR_IPS_REDUNDANCY"

  interface {
    id         = 0
    ip_address = var.router.aws_gateway_ips[0].gw_ip
  }
  interface {
    id         = 1
    ip_address = var.router.aws_gateway_ips[1].gw_ip
  }
  interface {
    id         = 2
    ip_address = var.router.aws_gateway_ips[2].gw_ip
  }
  interface {
    id         = 3
    ip_address = var.router.aws_gateway_ips[3].gw_ip
  }
}

resource "google_compute_vpn_tunnel" "vpn_conn_tunnel" {
  count = length(var.router.aws_gateway_ips) == 4 ? 4 : 0
  name  = "${var.router.name}-vpn-conn-${count.index}"

  peer_external_gateway           = google_compute_external_vpn_gateway.external_gateway[0].name
  peer_external_gateway_interface = count.index

  region                = var.router.region
  ike_version           = var.router.aws_gateway_ips[count.index].ike_version
  shared_secret         = var.router.aws_gateway_ips[count.index].shared_secret
  router                = google_compute_router.router.name
  vpn_gateway           = google_compute_ha_vpn_gateway.ha_gateway.name
  vpn_gateway_interface = floor(count.index / 2)
}
