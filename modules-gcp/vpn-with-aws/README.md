<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 advertised_ip_ranges  = 
	 router  = 
}
```
## Resources

| Name | Type |
|------|------|
| [google_compute_external_vpn_gateway.external_gateway](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_external_vpn_gateway) | resource |
| [google_compute_ha_vpn_gateway.ha_gateway](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_ha_vpn_gateway) | resource |
| [google_compute_router.router](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_router) | resource |
| [google_compute_router_interface.router_interface](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_router_interface) | resource |
| [google_compute_router_nat.nat](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_router_nat) | resource |
| [google_compute_router_peer.peer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_router_peer) | resource |
| [google_compute_vpn_tunnel.vpn_conn_tunnel](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_vpn_tunnel) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_advertised_ip_ranges"></a> [advertised\_ip\_ranges](#input\_advertised\_ip\_ranges) | n/a | `list(string)` | n/a | yes |
| <a name="input_router"></a> [router](#input\_router) | n/a | <pre>object({<br>    name         = string<br>    network_name = string<br>    region       = string<br>    asn          = number<br>    aws_gateway_ips = list(object({<br>      gw_ip            = string<br>      ike_version      = number # 1 or 2<br>      shared_secret    = string<br>      aws_inside_ip    = string<br>      gcp_inside_range = string<br>      aws_asn          = number # default: 64512<br>    }))<br>  })</pre> | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_router"></a> [router](#output\_router) | Expected output: router.interface\_ips = [ "*******", "*******", ... ] |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->