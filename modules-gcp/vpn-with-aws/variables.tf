variable "router" {
  type = object({
    name         = string
    network_name = string
    region       = string
    asn          = number
    aws_gateway_ips = list(object({
      gw_ip            = string
      ike_version      = number # 1 or 2
      shared_secret    = string
      aws_inside_ip    = string
      gcp_inside_range = string
      aws_asn          = number # default: 64512
    }))
  })
}

variable "advertised_ip_ranges" {
  type = list(string)
}
