module "enricher_sqs_new_property_to_enrich_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-new_property_to_enrich"

  max_receive_count         = 1
  message_retention_seconds = 345600

  tags = local.default_tags
}

module "enricher_sqs_new_property_to_enrich_reonomy_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-new_property_to_enrich_reonomy"

  max_receive_count         = 1
  message_retention_seconds = 345600

  tags = local.default_tags
}

module "enricher_sqs_new_scraped_property_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-new_scraped_property"

  receive_wait_time_seconds  = 20
  visibility_timeout_seconds = 40
  max_receive_count          = 3
  message_retention_seconds  = 604800

  tags = local.default_tags
}

module "enricher_sqs_reonomy_enriched_property_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-reonomy_enriched_property"

  visibility_timeout_seconds = 60

  tags = local.default_tags
}

module "enricher_sqs_reonomy_new_tenant_to_find_competitors_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-reonomy_new_tenant_to_find_competitors"

  max_receive_count         = 2
  message_retention_seconds = 345600

  tags = local.default_tags
}

module "enricher_sqs_reonomy_new_tenant_to_find_rating_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-reonomy_new_tenant_to_find_rating"

  max_receive_count = 2

  tags = local.default_tags
}

module "enricher_sqs_tenant_competitors_identified_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-enricher-tenant_competitors_identified"

  tags = local.default_tags
}
