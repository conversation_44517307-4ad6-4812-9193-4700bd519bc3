module "enricher_enriched_properties_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "${local.app_name}-enriched_properties_dev"
  capacity_mode = "ON_DEMAND"
  hash_key      = "KeywayId"

  attributes = {
    KeywayId = "S"
  }
  # TODO: Check terraform repo for warm throughput feature

  tags = merge(local.default_tags, {})
}

module "enricher_enriched_tenants_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-enriched_tenants_dev"
  capacity_mode       = "PROVISIONED"
  hash_key            = "KeywayId"
  range_key           = "RangeKey"
  deletion_protection = false

  attributes = {
    KeywayId = "S"
    RangeKey = "S"
  }

  default_capacity = {
    read  = 1
    write = 1
  }
  write_autoscaling = {
    enabled = true
    min     = 1
    max     = 5
  }
  read_autoscaling = {
    enabled = true
    min     = 1
    max     = 5
  }
  # TODO: Check terraform repo for warm throughput feature

  tags = merge(local.default_tags, {})
}
