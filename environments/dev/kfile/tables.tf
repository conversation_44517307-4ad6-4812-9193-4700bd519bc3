module "staging_files_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-staging_files-dev"
  capacity_mode       = "PROVISIONED"
  deletion_protection = false
  hash_key            = "k"
  range_key           = "sk"

  attributes = {
    k    = "S"
    sk   = "S"
    path = "S"
  }
  ttl_attribute = "expirationDate"

  global_indexes = [{
    name            = "path-index"
    hash_key        = "path"
    read_capacity   = 1
    write_capacity  = 1
    projection_type = "ALL"
  }]

  default_capacity = {
    read  = 1
    write = 1
  }
  write_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }
  read_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }

  tags = merge(local.default_tags, {})
}

