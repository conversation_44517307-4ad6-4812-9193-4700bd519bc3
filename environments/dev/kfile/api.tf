module "api" {
  app_name = "kfile-api"
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/kfile-service"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_DYNAMO_DB_REGION                    = "/any/aws/region",
    AWS_DYNAMO_DB_STAGING_FILES_TABLE_NAME  = "/dev/kfile-api/dynamodb_table_name",
    AWS_DYNAMO_DB_STAGING_FILES_TABLE_INDEX = "/dev/kfile-api/dynamodb_table_index_name",

    AWS_S3_ACCESS_KEY  = "/any/aws/access-key-id",
    AWS_S3_SECRET_KEY  = "/any/aws/secret-access-key",
    AWS_S3_DB_REGION   = "/any/aws/region",
    AWS_S3_BUCKET_NAME = "/dev/kfile-api/s3_bucket",

    SCOPE           = "/dev/kfile-api/scope",
    DATADOG_ENABLED = "/dev/kfile-api/datadog_enabled",
  }

  tags = local.default_tags
}
