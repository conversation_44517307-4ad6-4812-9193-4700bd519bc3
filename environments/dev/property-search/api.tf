module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-search-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    DEFAULT_SQS_GROUP_DELAY   = 100,
    AWS_DATABASE_SERVICE_NAME = "develop-db",
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    POSTGRES_DB_URL      = "/dev/property-search-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/property-search-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/property-search-db/db-api-password",

    API_URL         = "/dev/global/property-search-api-url",
    DATADOG_ENABLED = "/dev/property-search-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AWS_SQS_PROPERTY_NEWS = "/dev/property-search-api/aws-sqs-property-news",

    GEOGRAPHIC_API_URL = "/dev/global/geographics-api-url",

    SPLIT_IO_TOKEN   = "/dev/property-search-api/split-io-token",
    SPLIT_IO_TIMEOUT = "/dev/property-search-api/split-io-timeout",

    LUCENE_REFRESH_RATE_IN_MINUTES = "/dev/property-search-api/lucene-refresh-rate-in-minutes",

    DD_PROFILING_ENABLED = "/dev/property-search-api/profiling-enabled",

    CLOUD_SEARCH_SEARCH_ENDPOINT    = "/dev/property-search-api/cloudsearch-search-endpoint",
    CLOUD_SEARCH_DOCUMENTS_ENDPOINT = "/dev/property-search-api/cloudsearch-documents-endpoint",
    CLOUD_SEARCH_DOMAIN_NAME        = "/dev/property-search-api/cloudsearch-domain-name",

    SECURITY_ENABLED       = "/dev/property-search-api/security_enabled",
    AUTH0_DOMAIN           = "/dev/property-search-api/auth0_domain",
    AUTH0_AUDIENCES        = "/dev/property-search-api/auth0_audiences",
    AUTH0_ISSUER           = "/dev/property-search-api/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID = "/dev/global/keyway-organization-id"
    KEYWAY_KEY             = "/any/property-search-api/keyway-key"
  }

  tags = local.default_tags
}
