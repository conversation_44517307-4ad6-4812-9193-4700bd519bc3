module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2 * 1024
    memory = 4 * 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  iam_extra_policies = ["arn:aws:iam::aws:policy/AmazonBedrockFullAccess"]

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/workflows-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_image           = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    ENV     = "dev"
    SCOPE   = "server"
    PORT    = "8080"
    WORKERS = "2"

    LANGCHAIN_TRACING_V2 = "true"
    LANGCHAIN_ENDPOINT   = "https://api.smith.langchain.com"
    LANGCHAIN_PROJECT    = "workflows-api-dev"

    PROPERTY_ANALYZER_BASE_PATH = "https://property-analyzer-api.dev.whykeyway.com"

    TOPICS = jsonencode({ "workflow_tasks_topic" : "dev-workflows-workflow_task" })
    QUEUES = jsonencode({ "workflow_task_queue" : { "queue_name" : "dev-workflows-workflow_task", "workers" : 2, "visibility_timeout" : 120, "max_messages" : 10, "wait_time_seconds" : 10 } })

    DD_PROFILING_ENABLED          = "true"
    DATADOG_ENABLED               = "true"
    DD_RUNTIME_METRICS_ENABLED    = "true"
    DD_PROFILING_TIMELINE_ENABLED = "true"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION                = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"
    GH_SSH_PRIVATE_KEY    = "/any/github/private-ssh-key"

    DD_API_KEY = "/any/datadog/api-key"

    OPENAI_API_KEY               = "${local.ecs_parameter_prefix}/OPENAI_API_KEY"
    LANGCHAIN_API_KEY            = "${local.ecs_parameter_prefix}/LANGCHAIN_API_KEY"
    PROPERTY_ANALYZER_SECRET_KEY = "/any/property-analyzer-api/keyway-key"

    POSTGRES_DB_URL      = "/dev/workflows-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/workflows-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/workflows-db/db-api-password",
  }

  tags = local.default_tags
}
