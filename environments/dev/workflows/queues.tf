module "sqs-dev-workflows-workflow_task" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-workflows-workflow_task"
  max_receive_count = 3

  tags = local.default_tags
}

module "sns-dev-workflows-workflow_task" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-workflows-workflow_task"
  sqs_subscriptions = [module.sqs-dev-workflows-workflow_task.arn]

  tags = local.default_tags
}
