module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/keygen"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    AWS_SNS_IDENTIFICATION_NEWS = "dev-keygen-identification_news"
    GEOGRAPHICS_URL             = "https://geographics-api.dev.whykeyway.com"
  }

  ecs_secrets = {
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",

    DATADOG_API_KEY = "/dev/keygen/datadog-api-key",
    DATADOG_ENABLED = "/dev/keygen/datadog-enabled",

    GOOGLE_MAPS_API_KEY = "/dev/keygen/google-maps-api-key",
    PLACEKEY_API_KEY    = "/dev/keygen/placekey-api-key",

    ENV                       = "/dev/global/env",
    FIRST_AMERICAN_LAMBDA_URL = "/dev/global/first-american-lambda-url"

    ADDRESS_NORMALIZER_LAMBDA_URL = "/dev/global/address-normalizer-lambda-url"

    POSTGRES_DB_URL      = "/dev/keygen-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/keygen-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/keygen-db/db-api-password"
  }

  tags = local.default_tags
}

module "sns-dev-keygen-identification_news" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-keygen-identification_news"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:************:dev-property_assets-identification_news"]

  tags = local.default_tags
}
