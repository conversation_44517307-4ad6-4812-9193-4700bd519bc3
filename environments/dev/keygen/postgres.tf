module "postgres" {
  source = "../../../modules/postgres-config"

  database_users      = ["ezequiel", "gonzalo_huer<PERSON>", "javi", "raul", "mauro_titimo<PERSON>", "ignacio_boudgouste"]
  database_extensions = ["postgis"]

  # don't modify these
  skip_user_creation = true
  rds_server_config  = var.environment.rds_server_config
  database_name      = local.database_name
  database_api_user  = local.database_name
  parameter_prefix   = local.rds_parameter_prefix
}
