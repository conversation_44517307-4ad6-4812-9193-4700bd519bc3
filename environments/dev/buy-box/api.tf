module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/buy-box-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    API_URL         = "/dev/global/buy-box-api-url",
    DATADOG_ENABLED = "/dev/buy-box-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AUTH0_DOMAIN            = "/dev/global/auth0_domain",
    AUTH0_AUDIENCES         = "/dev/buy-box-api/auth0_audiences",
    AUTH0_ISSUER            = "/dev/global/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID  = "/dev/global/keyway-organization-id",
    ORG_WITH_PRIVATE_CONFIG = "/dev/buy-box-api/org-with-private-config"

    POSTGRES_DB_URL      = "/dev/buy-box-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/buy-box-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/buy-box-db/db-api-password",

    DEMOGRAPHICS_API_URL = "/dev/global/demographics-api-url",
  }

  tags = local.default_tags
}
