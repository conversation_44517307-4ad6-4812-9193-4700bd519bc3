module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/geographics-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    POSTGRES_DB_URL      = "/dev/geographics-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/geographics-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/geographics-db/db-api-password",

    API_URL         = "/dev/global/geographics-api-url",
    DATADOG_ENABLED = "/dev/geographics-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",
  }

  tags = local.default_tags
}
