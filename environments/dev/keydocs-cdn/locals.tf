locals {
  app_name          = "keydocs-pdf-image"
  kfile_bucket_name = "kfile-service-dev"

  # important variables:
  # ecs_variables on api.tf
  # database_users on postgres.tf
  # route_auth_map on gateway.tf
  # modules on queues.tf

  # stop editing here

  # NOTE: document-parser doesn't have the -api suffix
  lambda_name = local.app_name
  # database_name = replace(local.app_name, "-", "_")

  lambda_parameter_prefix = "/${var.environment.private_network.vpc_name}/${local.app_name}-api"
  # rds_parameter_prefix = "/${var.environment.private_network.vpc_name}/${local.app_name}-db"

  gateway_name = "${local.app_name}-${var.environment.private_network.vpc_name}"
  gateway_url  = join(".", compact(["${local.app_name}-gw", var.environment.cluster.dns_prefix, "whykeyway.com"]))
  service_url  = join(".", compact(["${local.lambda_name}", var.environment.cluster.dns_prefix, "whykeyway.com"]))

  default_tags = merge(aws_servicecatalogappregistry_application.application.application_tag, {
    "service" = local.lambda_name
    "env"     = var.environment.private_network.vpc_name
    "cluster" = var.environment.cluster.name
    "Datadog" = "true"
  })
}
