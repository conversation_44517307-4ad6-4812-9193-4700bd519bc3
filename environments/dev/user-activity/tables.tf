module "user_activity_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}_dev"
  deletion_protection = false
  capacity_mode       = "PROVISIONED"

  hash_key  = "PK"
  range_key = "SK"
  attributes = {
    PK      = "S"
    SK      = "S"
    LSI1-SK = "S"
  }
  local_indexes = [
    {
      name            = "LSI1"
      range_key       = "LSI1-SK"
      projection_type = "ALL"
    }
  ]

  default_capacity = {
    read  = 5
    write = 5
  }
  write_autoscaling = {
    enabled = true
    min     = 5
    max     = 10
  }
  read_autoscaling = {
    enabled = true
    min     = 5
    max     = 10
  }

  tags = merge(local.default_tags, {})
}
