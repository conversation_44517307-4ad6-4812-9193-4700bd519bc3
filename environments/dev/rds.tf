module "dev_rds" {
  source = "../../modules/rds-postgres"

  server_name      = "develop-db"
  root_username    = "develop_root"     # only az and underscores (_)
  parameter_prefix = "/sre/develop-db/" # root user and password
  rds_network      = module.dev_vpc.private_network

  instance_class        = "db.t4g.medium"
  max_allocated_storage = 300

  engine_version         = "16.6"
  parameter_group_family = "postgres16"
  custom_parameters = {
    "max_connections" = "300"
  }

  tags = {
    "service" = "dev-shared-db"
    "env"     = module.dev_vpc.private_network.vpc_name
    "cluster" = module.dev_cluster.cluster.name
  }
}

module "dev_postgres" {
  source = "../../modules/postgres-config"

  # NOTE: all the developers are listed since it's a shared database, roles will be assigned per application
  # same as email but dots are underscores (_)
  database_users = [
    "chiri",
    "estebanwasinger",
    "ezequiel",
    "fer",
    "gonzalo_huertas",
    "guiller<PERSON>_podesta",
    "ignacio_boudgouste",
    "javi",
    "javier_valeriano",
    "mauro_titimoli",
    "raul",
    "recheconea"
  ]

  # don't modify these
  rds_server_config = module.dev_rds.rds_server_config
  database_name     = "developers"
  database_api_user = "developers"
  parameter_prefix  = "/sre/develop-db/" # developers user and password
}

module "dev_rds_backup" {
  source = "../../modules/aws-backup"
  vault  = "develop-db"
  # schedule = "cron(0 3 * * ? *)"
  resources_arn = [
    module.dev_rds.arn
  ]
}
