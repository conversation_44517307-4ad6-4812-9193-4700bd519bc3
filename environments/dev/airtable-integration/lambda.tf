module "lambda" {
  source          = "../../../modules/lambda-function-v2/"
  name            = local.app_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  disable_lb      = true
  description     = ""

  environment_variables = {
    "API_KEY"             = "pat8FlvjpEDpaqbpq.9ae751599336dc7b037d0615bf80e3ad9c45e128393a50fb87f837c927b76b31"
    "BASE_URL"            = "https://api.airtable.com/v0/"
    "MF_BASE_ID"          = "appLDnszUa4aZ9gIw"
    "MULTIFAMILY_TABLE"   = "MF%20Properties"
    "NNN_BASE_ID"         = "appTBNDZhWDLMhRrM"
    "NNN_TABLE"           = "Properties"
    "S3_BUCKET"           = "airtable-records-bkp"
    "SQS_DESTINATION"     = "https://sqs.us-east-1.amazonaws.com/681574592108/dev-deal_room-airtable_events"
  }

  sqs_trigger = {
    queue_arn   = "arn:aws:sqs:us-east-1:681574592108:dev-deal_room-deal-events"
    batch_size  = 1
    enabled     = false
  }

  function_source = local.function_source

  runtime = "python3.9"
  handler = "lambda_function.lambda_handler"
  timeout = 30

  tags = merge(local.default_tags, {})
}
