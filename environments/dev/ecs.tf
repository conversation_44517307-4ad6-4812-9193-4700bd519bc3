module "dev_cluster" {
  source = "../../modules/ecs-fargate-cluster"

  cluster_name = "develop"
  dns_prefix   = "dev"

  fargate_spot_mode = "full"

  auth0_settings = {
    OPENID_CONFIGURATION = "https://auth.dev.whykeyway.com/.well-known/openid-configuration",
    AUDIENCE             = "https://keyway-api.dev.whykeyway.com",
    TOKEN_ISSUER         = "https://auth.dev.whykeyway.com/"
  }

  private_network = module.dev_vpc.private_network
  public_network  = module.dev_vpc.public_network
}
