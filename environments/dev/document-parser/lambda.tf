module "lambda" {
  source = "../../../modules/lambda-docker-v2/"

  name            = local.lambda_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  public_endpoint = false

  memory_size       = (10 * 1024) # in MB
  ephemeral_storage = (10 * 1024) # in MB
  timeout           = 600         # 1-900 seconds

  environment_variables = {
    # ENV_KEY = "value"
    K_FILE_SERVICE_URL = "https://kfile-api.dev.whykeyway.com"
  }

  # these are not updated on runtime, terraform needs to be applied after changes
  environment_secrets = {
    # ENV_KEY = "/env/app/secret_path"
  }

  tags = local.default_tags
}
