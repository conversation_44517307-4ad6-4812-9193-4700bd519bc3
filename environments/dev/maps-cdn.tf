module "maps_cdn_dev" {
  source               = "../../modules/cloudfront-s3"
  cloudfront_domain    = "maps-cdn.dev.whykeyway.com"
  s3_bucket_name       = "maps-cdn-dev-whykeyway"
  cors_allowed_origins = ["keyway-dev.us.auth0.com", "auth.dev.whykeyway.com", "*.dev.whykeyway.com", "cdn.dev.whykeyway.com"]

  lambda_edge_functions = { cdn-image-optimization-rewrite-dev : module.lambda_cdn_image_rewrite_dev.lambda_arn }
  origin_shield_regions = ["us-east-1"]
  origin_failover       = replace(replace(module.lambda_cdn_image_import_dev.lambda_url, "https://", ""), "/", "")
  origin_failover_headers = {
    "x-origin-secret-header" = data.aws_ssm_parameter.cdn_image_optimizer_key.value
  }
  query_string_whitelist    = ["fetch_url"]
  s3_cleanup_lifecycle_days = 90
}

data "aws_ssm_parameter" "cdn_image_optimizer_key" {
  name = "/dev/cdn-image-optimizer/secret-key"
}

# based on image-optimization from https://github.com/aws-samples/image-optimization
# expects lambda-layer-sharp to be installed: https://github.com/Umkus/lambda-layer-sharp
module "lambda_cdn_image_rewrite_dev" {
  source          = "../../modules/lambda-function/"
  name            = "cdn-image-optimization-rewrite-dev"
  source_code_zip = "./maps-cdn/image-optimization-rewrite.zip"
}
module "lambda_cdn_image_import_dev" {
  source          = "../../modules/lambda-function/"
  name            = "cdn-image-optimization-import-dev"
  source_code_zip = "./maps-cdn/image-optimization-import.zip"
  lambda_layers   = ["arn:aws:lambda:us-east-1:681574592108:layer:sharp:1"]
  create_url      = true
  custom_headers  = ["x-origin-secret-header"]
  timeout         = 100

  environment_vars = {
    SECRET_KEY                  = data.aws_ssm_parameter.cdn_image_optimizer_key.value
    S3_TRANSFORMED_IMAGE_BUCKET = "maps-cdn-dev-whykeyway"
    TRANSFORMED_IMAGE_CACHE_TTL = "3600"
  }

  extra_iam_statements = [
    {
      Action   = "s3:GetObject"
      Effect   = "Allow"
      Resource = "arn:aws:s3:::maps-cdn-dev-whykeyway/*"
    },
    {
      Action   = "s3:PutObject"
      Effect   = "Allow"
      Resource = "arn:aws:s3:::maps-cdn-dev-whykeyway/*"
    },
    {
      Action   = "s3:PutObjectAcl"
      Effect   = "Allow"
      Resource = "arn:aws:s3:::maps-cdn-dev-whykeyway/*"
    }
  ]
}
