module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network

  port                 = 5572
  health_check_path    = "/noop"
  health_check_matcher = 404

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/rclone"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    RC_USER = "/dev/rclone/rc-user",
    RC_PASS = "/dev/rclone/rc-pass",
  }

  tags = local.default_tags
}
