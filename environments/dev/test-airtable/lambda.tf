module "lambda" {
  source          = "../../../modules/lambda-function-v2/"
  name            = local.app_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  disable_lb      = true
  description     = ""

  environment_variables = {
    "API_KEY"             = "key0ovozw7jB2CZty"
    "AT_PROPERTIES_TABLE" = "tbl74TjfQty0r5WXF"
    "BASE_ID"             = "appY47M46UaUyDKam"
    "BASE_URL"            = "https://api.airtable.com/v0/"
    "MULTIFAMILY_TABLE"   = "MF%20Properties"
    "NNN_TABLE"           = "Properties"
    "S3_BUCKET"           = "airtable-records-bkp"
    "SQS_DESTINATION"     = "https://sqs.us-east-1.amazonaws.com/681574592108/stg-deal_room-airtable_events"
  }

  function_source = local.function_source

  runtime = "python3.9"
  handler = "lambda_function.lambda_handler"
  timeout = 60

  tags = merge(local.default_tags, {})
}
