module "postgres" {
  source = "../../../modules/postgres-config"

  # the users on dev are created by the parent rds.tf file, make sure they're listed there
  database_users = ["recheconea", "estebanwasinger"]

  # don't modify these
  skip_user_creation = true
  rds_server_config  = var.environment.rds_server_config
  database_name      = local.database_name
  database_api_user  = local.database_name
  parameter_prefix   = local.rds_parameter_prefix
}
