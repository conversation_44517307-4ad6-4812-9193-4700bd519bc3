module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  codebuild_image = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  chatbot_arn     = var.environment.chatbot_arn

  github_repository  = "unlockre/whats-new-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    ENV   = "dev"
    SCOPE = "server"
    PORT  = "8080"

    USE_AZURE = "1"
    DAYS_TTL  = "2"

    DD_PROFILING_ENABLED = "true"
    DATADOG_ENABLED      = "true"

    API_URL     = local.service_url
    GATEWAY_URL = local.gateway_url
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"

    POSTGRES_DB_URL      = "/dev/whats-new-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/whats-new-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/whats-new-db/db-api-password",
  }

  tags = local.default_tags
}
