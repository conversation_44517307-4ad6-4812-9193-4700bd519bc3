module "ecs_autoscaling" {
  source     = "../../../modules/ecs-auto-scaling"
  depends_on = [module.api]

  app_name            = local.app_name
  service_resource_id = module.api.resource_id

  scaling_method = "schedule"

  # shutdown at night
  scheduled_actions = [
    { cron = "0 1 * * ? *", min_capacity = 0, max_capacity = 0 },  # 22:00 ART
    { cron = "0 11 * * ? *", min_capacity = 1, max_capacity = 1 }, # 08:00 ART
  ]

}
