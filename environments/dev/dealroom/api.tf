module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/deal-room-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_image           = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
  codebuild_migration_stage = true
  codebuild_compute_type    = "BUILD_GENERAL1_MEDIUM"

  ecs_variables = {
    # VAR_NAME = "string"
    AWS_SQS_USER_EVENT = "dev-deal_room-user_events"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"

    # AWS
    AWS_ACCOUNT_ID     = "/any/aws/account-id",
    AWS_REGION         = "/any/aws/region",
    AWS_S3_ACCESS_KEY  = "/any/aws/access-key-id",
    AWS_S3_BUCKET_NAME = "deal_room_api_dev_aws_s3_bucket_name",
    AWS_S3_SECRET_KEY  = "/any/aws/secret-access-key",

    AWS_SNS_TASK_CHANGED_TOPIC_ARN         = "deal_room_api_dev_aws_sns_task_changed_topic_arn",
    AWS_SNS_DEAL_EVENTS_TOPIC_ARN          = "/dev/deal-room-api/sns_deal_events_topic_arn",
    AWS_SQS_LOI_SIGNED_QUEUE_NAME          = "deal_room_api_dev_aws_sqs_loi_signed_queue_name",
    AWS_SQS_TASK_CHANGED_QUEUE_NAME        = "deal_room_api_dev_aws_sqs_task_changed_queue_name",
    AWS_SQS_CRON_JOBS_QUEUE_NAME           = "/dev/deal-room-api/aws_sqs_cron_jobs_queue_name",
    AWS_SQS_DEAL_EVENTS_QUEUE_NAME         = "/dev/deal-room-api/aws_deal_events_sqs",
    AWS_SQS_READ_EXCEL_FILE_QUEUE_NAME     = "/dev/deal-room-api/aws_sqs_read_excel_file_queue_name",
    AWS_SQS_AIRTABLE_EVENTS_QUEUE_NAME     = "/dev/deal-room-api/aws_airtable_events_sqs",
    AWS_SQS_EXCEL_FILE_RESULT_QUEUE_NAME   = "/dev/deal-room-api/aws_sqs_excel_file_result_queue_name",
    AWS_SQS_CHAT_GPT_FILE_SENT_QUEUE_NAME  = "/dev/deal-room-api/gpt-file-sent-queue-name",
    AWS_SQS_CHAT_GPT_FILE_READY_QUEUE_NAME = "/dev/deal-room-api/gpt-file-ready-queue-name",
    AWS_SQS_OM_FILE_SENT_QUEUE_NAME        = "/dev/deal-room-api/om-file-sent-queue-name",
    AWS_SQS_OM_FILE_RESULT_QUEUE_NAME      = "/dev/deal-room-api/om-file-ready-queue-name",

    # Frontend apps urls
    DEAL_ROOM_ADMIN_APP = "deal_room_api_dev_admin_app",
    DEAL_ROOM_APP       = "deal_room_api_dev_app",
    # Docusign
    DOCUSIGN_ACCOUNT_ID          = "deal_room_api_dev_docusign_account_id",
    DOCUSIGN_APP_INTEGRATION_KEY = "deal_room_api_dev_docusign_app_integration_key",
    DOCUSIGN_BASE_URL            = "deal_room_api_dev_docusign_base_url",
    DOCUSIGN_USER_ID             = "deal_room_api_dev_docusign_user_id",
    DOCUSIGN_BUYER_USER_ID       = "deal_room_api_dev_docusign_buyer_user_id",
    DOCUSIGN_PRIVATE_KEY         = "deal_room_api_dev_docusign_private_key"

    SCOPE           = "deal_room_api_dev_scope",
    ENV             = "deal_room_api_dev_env",
    DATADOG_ENABLED = "deal_room_api_dev_datadog_enabled",

    # DB
    POSTGRES_DB_URL      = "/dev/deal-room-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/deal-room-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/deal-room-db/db-api-password",

    # Sendgrid
    SENDGRID_API_KEY                           = "deal_room_api_dev_sendgrid_api_key",
    SENDGRID_APP_URL                           = "deal_room_api_dev_sendgrid_app_url",
    SENDGRID_EMAIL                             = "deal_room_api_dev_sendgrid_email",
    SENDGRID_FORGOT_PASSWORD_EMAIL_TEMPLATE_ID = "deal_room_api_dev_sendgrid_forgot_password_em_id",
    SENDGRID_TASK_CHANGED_EMAIL_TEMPLATE_ID    = "deal_room_api_dev_sendgrid_task_changed_em_id",
    SENDGRID_WELCOME_EMAIL_SELLER_TEMPLATE_ID  = "deal_room_api_dev_sendgrid_welcome_seller_em_id",
    SENDGRID_WELCOME_EMAIL_TEMPLATE_ID         = "deal_room_api_dev_sendgrid_welcome_em_id",
    SENDGRID_LOI_WITH_LEASE_OFFER_EMAIL_ID     = "/dev/deal-room-api/sendgrid/sendgrid_loi_with_lease_offer_email_id",
    SENDGRID_LOI_WITH_LEASE_SIGN_EMAIL         = "/dev/deal-room-api/sendgrid/sendgrid_loi_with_lease_sign_email",
    SENDGRID_LOI_OFFER_EMAIL_ID                = "/dev/deal-room-api/sendgrid/sendgrid_loi_offer_email_id",

    # Services urls
    PROPERTY_SAGE_URL         = "/dev/global/property-sage-api-url",
    KEYGEN_URL                = "/dev/global/keygen-api-url",
    K_FILE_SERVICE_URL        = "/dev/global/kfile-api-url",
    PROPERTY_ESTIMATE_API_URL = "/dev/deal_room_api/property_estimate_api_url",
    PROPERTY_ASSETS_API_URL   = "/dev/global/property-assets-api-url",
    ORGANIZATIONS_API_URL     = "/dev/global/organizations-api-url",


    TOP_PRIORITY_TASKS_SIZE = "deal_room_api_dev_top_priority_tasks_size",

    # LOI
    LOI_SENT_EMAIL_FROM    = "deal_room_api_dev_loi_sent_email_from",
    LOI_SENT_EMAIL_CCS     = "deal_room_api_dev_loi_sent_email_carbon_copies",
    LOI_SENT_EMAIL_BCCS    = "deal_room_api_dev_loi_sent_email_background_carbon_copies",
    LOI_SIGN_EMAIL_BODY    = "deal_room_api_dev_loi_sign_email_body",
    LOI_SIGN_EMAIL_SUBJECT = "deal_room_api_dev_loi_sign_email_subject",

    #Split IO
    SPLIT_IO_TOKEN   = "deal_room_api_dev_split_io_token",
    SPLIT_IO_TIMEOUT = "deal_room_api_dev_split_io_timeout",

    # Auth0
    AUTH0_DOMAIN                    = "/dev/deal-room-api/auth0_domain",
    AUTH0_DEAL_ROOM_CLIENT_ID       = "/dev/deal-room-api/auth0_deal_room_client_id",
    AUTH0_DEAL_ROOM_ADMIN_CLIENT_ID = "/dev/deal-room-api/auth0_deal_room_admin_client_id",

    # Auth0 management API
    AUTH0_MANAGEMENT_AUDIENCE = "/dev/deal-room-api/auth0_management_audience",
    AUTH0_CLIENT_ID           = "/dev/deal-room-api/auth0_client_id",
    AUTH0_CLIENT_SECRET       = "/dev/deal-room-api/auth0_client_secret",
    AUTH0_USERS_DATABASE_NAME = "/dev/deal-room-api/auth0_users_db_name",

    # Auth0 jwt validation
    AUTH0_DEAL_ROOM_AUDIENCE  = "/dev/deal-room-api/auth0_deal_room_audience",
    AUTH0_DEAL_ROOM_AUDIENCES = "/dev/deal-room-api/auth0_deal_room_audiences",
    AUTH0_ISSUER              = "/dev/deal-room-api/auth0_token_issuer",

    # Activate account jwt validation
    ACTIVATE_ACCOUNT_TOKEN_SECRET                = "/dev/deal-room-api/activate_account_token_secret",
    ACTIVATE_ACCOUNT_TOKEN_ISSUER                = "/dev/deal-room-api/activate_account_token_issuer",
    ACTIVATE_ACCOUNT_TOKEN_EXPIRATION_IN_SECONDS = "/dev/deal-room-api/activate_account_token_expiration_in_seconds",

    # Sign Email Token Params
    EMAIL_TOKEN_SECRET                = "/dev/deal-room-api/email_token_secret",
    EMAIL_TOKEN_ISSUER                = "/dev/deal-room-api/email_token_issuer",
    EMAIL_TOKEN_EXPIRATION_IN_SECONDS = "/dev/deal-room-api/email_token_expiration_in_seconds",

    # Chat GPT
    CHAT_GPT_API_URL       = "/dev/deal-room-api/chat-gpt-service-url",
    CHAT_GPT_SERVICE_TOKEN = "/dev/deal-room-api/chat-gpt-service-token",

    # Google Calendar
    GOOGLE_CALENDAR_JSON_KEY = "/dev/deal-room-api/google_calendar_json_key",

    DEFAULT_SELLER_BROKER_EMAIL = "/dev/deal-room-api/default_seller_broker_email",
    KOS_URL                     = "/dev/global/kos-url"
  }

  tags = local.default_tags
}
