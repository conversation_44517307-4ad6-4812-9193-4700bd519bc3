module "deal_room_dev_sqs_cron_jobs" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-cron_jobs"

  tags = local.default_tags
}

module "deal_room_dev_sqs_loi_signed" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-loi_signed"

  receive_wait_time_seconds = 20

  tags = local.default_tags
}

module "deal_room_dev_sqs_task_updated" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-task_updated"

  tags = local.default_tags
}

module "deal_room_sqs_deal_events_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-deal-events"

  tags = local.default_tags
}

module "deal_room_sqs_read_excel_file_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-read_excel_file"

  tags = local.default_tags
}

module "deal_room_sqs_airtable_events_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-airtable_events"

  tags = local.default_tags
}

module "deal_room_sqs_excel_file_result_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-excel_file_result"

  tags = local.default_tags
}

module "deal_room_dev_sqs_gpt_file_sent" {
  source        = "../../../modules/sqs-queue"
  queue_name    = "dev-deal_room-gpt-file-sent"
  delay_seconds = 20

  tags = local.default_tags
}

module "deal_room_dev_sqs_gpt_file_ready" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-gpt-file-ready"

  tags = local.default_tags
}

module "deal_room_dev_sqs_om_file_sent" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-om-file-sent"

  tags = local.default_tags
}

module "deal_room_dev_sqs_om_file_ready" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-om-file-ready"

  tags = local.default_tags
}

module "sns-dev-deal-room-deal_events" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-deal_room-deal_events"
  sqs_subscriptions = [
    module.deal_room_dev_data_integration_deal_events.arn
  ]

  tags = local.default_tags
}

module "deal_room_dev_data_integration_deal_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-data_integration_deal_events"

  tags = local.default_tags
}

module "deal_room_dev_user_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-deal_room-user_events"

  tags = local.default_tags
}
