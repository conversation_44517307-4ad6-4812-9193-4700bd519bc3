locals {
  app_name = "deal-room" # <<< CHANGE ME

  # important variables:
  # ecs_variables on api.tf
  # database_users on postgres.tf
  # route_auth_map on gateway.tf
  # modules on queues.tf

  # stop editing here

  ecs_name      = "${local.app_name}-api"
  database_name = replace(local.app_name, "-", "_")

  ecs_parameter_prefix = "/${var.environment.private_network.vpc_name}/${local.app_name}-api"
  rds_parameter_prefix = "/${var.environment.private_network.vpc_name}/${local.app_name}-db"

  # TODO: fix this to remove the -api T_T
  gateway_name = "${local.app_name}-api-${var.environment.private_network.vpc_name}"
  gateway_url  = join(".", compact(["${local.app_name}-gw", var.environment.cluster.dns_prefix, "whykeyway.com"]))
  service_url  = join(".", compact(["${local.ecs_name}", var.environment.cluster.dns_prefix, "whykeyway.com"]))

  default_tags = merge(aws_servicecatalogappregistry_application.application.application_tag, {
    "service" = local.ecs_name
    "env"     = var.environment.private_network.vpc_name
    "cluster" = var.environment.cluster.name
  })
}
