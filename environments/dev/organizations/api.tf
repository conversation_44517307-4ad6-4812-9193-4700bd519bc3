module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/organizations-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_image           = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    AWS_SNS_USER_EVENTS = "dev-organizations-user_events"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    SCOPE   = "/any/global/server-scope",
    API_URL = "/dev/organizations-api/api-url",

    # DATADOG
    DATADOG_ENABLED = "/dev/organizations-api/datadog-enabled",
    DATADOG_API_KEY = "/any/datadog/api-key",

    # AWS
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    # DB
    POSTGRES_DB_URL      = "/dev/organizations-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/organizations-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/organizations-db/db-api-password",

    # Auth0
    AUTH0_DOMAIN   = "/dev/organizations-api/auth0_domain",
    AUTH0_AUDIENCE = "/dev/organizations-api/auth0_audience",
    AUTH0_ISSUER   = "/dev/organizations-api/auth0_token_issuer",


    # Auth0 Managment API
    AUTH0_MANAGEMENT_AUDIENCE = "/dev/organizations-api/auth0_management_audience",
    AUTH0_CLIENT_ID           = "/dev/organizations-api/auth0_client_id",
    AUTH0_CLIENT_SECRET       = "/dev/organizations-api/auth0_client_secret",
    AUTH0_USERS_DATABASE_NAME = "/dev/organizations-api/auth0_users_db_name",
  }

  tags = local.default_tags
}
