module "api" {
  # WARNING: The length of the app_name needs to be less or equal than 18 characters
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = false

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-sage-synchropy"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    PROPERTY_SAGE_DB_USER     = "/dev/property-sage-db/db-api-username",
    PROPERTY_SAGE_DB_PASSWORD = "/dev/property-sage-db/db-api-password",
    PROPERTY_SAGE_DB_HOST     = "/dev/property-sage-synchropy/sage-database-host",
    PROPERTY_SAGE_DB_PORT     = "/dev/property-sage-synchropy/sage-database-port",
    PROPERTY_SAGE_DB_DATABASE = "/dev/property-sage-synchropy/sage-database-name",
    PROPERTY_SAGE_API_URL     = "/dev/property-sage-db/db-api-username",

    PROPERTY_SEARCH_DB_USER     = "/dev/property-search-db/db-api-username",
    PROPERTY_SEARCH_DB_PASSWORD = "/dev/property-search-db/db-api-password",
    PROPERTY_SEARCH_DB_HOST     = "/dev/property-sage-synchropy/search-database-host",
    PROPERTY_SEARCH_DB_PORT     = "/dev/property-sage-synchropy/search-database-port",
    PROPERTY_SEARCH_DB_DATABASE = "/dev/property-search-db/db-api-username",

    GEOGRAPHICS_DB_USER     = "/dev/geographics-db/db-api-username",
    GEOGRAPHICS_DB_PASSWORD = "/dev/geographics-db/db-api-password",
    GEOGRAPHICS_DB_HOST     = "/dev/property-sage-synchropy/geographics-database-host",
    GEOGRAPHICS_DB_PORT     = "/dev/property-sage-synchropy/geographics-database-port",
    GEOGRAPHICS_DB_DATABASE = "/dev/geographics-db/db-api-username",

    BIGQUERY_SCHEMA    = "/dev/property-sage-synchropy/bigquery-schema",
    API_URL            = "/dev/global/property-sage-synchropy-api-url",
    GOOGLE_KEY_PATH    = "/any/property-sage-synchropy/google-key-path",
    GOOGLE_KEY         = "/dev/property-sage-synchropy/google-key", # used in codebuild
    DATADOG_API_KEY    = "/any/datadog/api-key",
    SCOPE              = "/any/global/server-scope",
    GEOGRAPHIC_API_URL = "/dev/global/geographics-api-url",

    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id",
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key",
    AWS_ACCOUNT_ID        = "/any/aws/account-id",
    AWS_REGION            = "/any/aws/region",

    DEMOGRAPHICS_STATS_TOPIC     = "/dev/property-sage-synchropy/demographics-stats-topic",
    MARKET_STATS_TOPIC           = "/dev/property-sage-synchropy/market-stats-topic",
    NEW_DEVELOPMENTS_STATS_TOPIC = "/dev/property-sage-synchropy/new-developments-stats-topic",
    COMP_RECORD_TOPIC            = "/dev/property-sage-synchropy/comp-record-topic",
  }

  tags = local.default_tags
}
