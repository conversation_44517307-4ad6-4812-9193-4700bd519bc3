module "property_sage_synchropy_schedule_daily_sync_dev" {
  event_name = "dev-synchropy-daily-sync" # make it short
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 22 * * ? *)" # 19:00 ART

  url    = "https://${local.service_url}/sync"
  method = "POST"

  body = jsonencode({
    entities = [
      "new_developments",
      "new_developments_stats",
      "notify_market_stats",
      "demographic_data_and_percentiles",
      "flood_score",
      "comps_record"
    ],
    update_scores = true,
    time_delta = {
      time_delta_unit  = "hours",
      time_delta_value = 36
    }
  })
}
