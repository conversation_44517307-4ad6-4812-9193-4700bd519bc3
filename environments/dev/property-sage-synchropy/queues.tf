module "pr_sage_synchropy_sqs_demographics_stats_dev" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-synchropy-demographics_stats"

  tags = local.default_tags
}

module "sns-dev-synchropy-new_developments_stats" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-synchropy-new_developments_stats"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-demographics-new_developments_stats"]

  tags = local.default_tags
}

module "sns-dev-synchropy-comp_record" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "dev-synchropy-comp_record"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-comps-calculate_record_priority"]

  tags = local.default_tags
}
