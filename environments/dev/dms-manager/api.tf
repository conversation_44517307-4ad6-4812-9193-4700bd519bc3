module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/dms-manager"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    DATADOG_ENABLED = "true",
    AUTH0_AUDIENCE  = "https://keyway-api.dev.whykeyway.com",

    API_URL                   = "https://dms-manager-api.dev.whykeyway.com",
    PROPERTY_ANALYZER_API_URL = "https://property-analyzer-api.dev.whykeyway.com",
    RCLONE_API_URL            = "https://rclone-api.dev.whykeyway.com",
    DOCUMENT_PARSER_API_URL   = "https://document-parser.dev.whykeyway.com"

    KEYWAY_PROVIDER_PREFIX_PATH = "kfile-service-dev/document-intelligence",
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AUTH0_DOMAIN = "/dev/global/auth0_domain",
    AUTH0_ISSUER = "/dev/global/auth0_token_issuer",

    POSTGRES_DB_URL      = "/dev/dms-manager-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/dms-manager-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/dms-manager-db/db-api-password",

    KEYWAY_PROVIDER_ACCESS_KEY = "/any/aws/access-key-id",
    KEYWAY_PROVIDER_SECRET_KEY = "/any/aws/secret-access-key",
  }

  tags = local.default_tags
}
