module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-sage-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    AWS_SQS_SYNC_PROPERTIES_SCORE_JOB = "property_sage_api_dev_aws_sqs_sync_properties_score_job",
    AWS_SNS_SYNC_PROPERTY_SCORE       = "property_sage_api_dev_aws_sns_sync_property_score",
    AWS_SQS_SYNC_PROPERTY_SCORE       = "property_sage_api_dev_aws_sqs_sync_property_score",

    POSTGRES_DB_URL      = "/dev/property-sage-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/property-sage-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/property-sage-db/db-api-password",

    SCOPE           = "property_sage_api_dev_scope",
    DATADOG_ENABLED = "property_sage_api_dev_datadog_enabled",

    PROSPECT_API_URL = "/dev/global/prospect-api-url",
    COMPS_API_URL    = "/dev/global/comps-api-url",
    API_URL          = "/dev/global/property-sage-api-url",

    DEMOGRAPHICS_API_URL   = "/dev/global/demographics-api-url",
    KEYWAY_ORGANIZATION_ID = "/dev/global/keyway-organization-id",
  }

  tags = local.default_tags
}
