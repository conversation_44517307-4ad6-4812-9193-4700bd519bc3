module "first_pass_analyses_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "first-pass-analyses_dev"
  capacity_mode       = "ON_DEMAND"
  deletion_protection = false
  hash_key            = "PK"
  range_key           = "SK"

  attributes = {
    PK = "S"
    SK = "S"
  }
  # TODO: Check terraform repo for warm throughput feature

  tags = merge(local.default_tags, {})
}

module "files_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}_files_dev"
  capacity_mode       = "PROVISIONED"
  deletion_protection = false
  hash_key            = "PK"

  attributes = {
    PK = "S"
  }
  # TODO: Check terraform repo for warm throughput feature

  default_capacity = {
    write = 2
    read  = 2
  }
  read_autoscaling = {
    enabled = true
    min     = 2
    max     = 10
  }
  write_autoscaling = {
    enabled = true
    min     = 2
    max     = 10
  }

  tags = merge(local.default_tags, {})
}
