module "api" {
  app_name   = local.ecs_name
  cluster    = var.environment.cluster
  source     = "../../../modules/ecs-service-deployment-v2"
  depends_on = [module.postgres]

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-analyzer-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    AWS_SNS_EXTRACTORS_TO_PROCESS   = "dev-property_analyzer-extractors_to_process"
    AWS_SNS_DATATABLE_EXTRACTORS    = "dev-property_analyzer-datatable_extractors"
    AWS_SQS_METADATA_FROM_FILE_NEWS = "dev-property_analyzer-metadata_news"
    AWS_SQS_EXTRACTORS_TO_PROCESS   = "dev-property_analyzer-extractors_to_process"
    AWS_SQS_EXTRACTORS_NEWS         = "dev-property_analyzer-extractors_news"
    FILE_METADATA_BUCKET_NAME       = "property-analyzer-files-dev"
    DOCUMENT_PARSER_API_URL         = "https://document-parser.dev.whykeyway.com"
    GEOGRAPHIC_API_URL              = "https://geographics-api.dev.whykeyway.com"
    RENT_API_URL                    = "https://rent-api.dev.whykeyway.com"
    COMP_SELECTOR_API_URL           = "https://comp-selector-api.dev.whykeyway.com"

    USE_KEYDOCS_AI = "True"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    API_URL         = "/dev/global/property-analyzer-api-url",
    DATADOG_ENABLED = "/dev/property-analyzer-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AUTH0_DOMAIN           = "/dev/global/auth0_domain",
    AUTH0_AUDIENCES        = "/dev/property-analyzer-api/auth0_audiences",
    AUTH0_ISSUER           = "/dev/global/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID = "/dev/global/keyway-organization-id",
    SECRET_KEY             = "/any/property-analyzer-api/keyway-key"

    DATA_GPT_API_URL               = "/dev/global/data-gpt-api-url",
    DATA_GPT_API_AUTH_TOKEN        = "/any/data-gpt-api/auth-token",
    DEMOGRAPHICS_API_URL           = "/dev/global/demographics-api-url",
    COMPS_API_URL                  = "/dev/global/comps-api-url",
    PROPERTY_ASSETS_API_URL        = "/dev/global/property-assets-url",
    PROPERTY_SEARCH_API_URL        = "/dev/global/property-search-api-url",
    PROPERTY_SEARCH_API_AUTH_TOKEN = "/any/property-search-api/keyway-key",
    DEAL_ROOM_API_URL              = "/dev/global/deal-room-api-url",
    KFILE_URL                      = "/dev/global/kfile-api-url",
    BUY_BOX_API_URL                = "/dev/global/buy-box-api-url",

    FIRST_PASS_ANALYSES_BUCKET_NAME = "/dev/property-analyzer-api/first-pass-bucket",
    FIRST_PASS_ANALYSES_TABLE_NAME  = "/dev/property-analyzer-api/first-pass-table-name",
    FILES_TABLE_NAME                = "/dev/property-analyzer-api/files-table-name",

    AWS_SQS_CLASSIFY_FILE_NEWS           = "/dev/property-analyzer-api/aws-sqs-classify-file-news",
    AWS_SQS_PROPERTY_DATA_FROM_FILE_NEWS = "/dev/property-analyzer-api/aws-sqs-property-data-news",

    POSTGRES_DB_URL      = "/dev/property-analyzer-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/property-analyzer-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/property-analyzer-db/db-api-password",
  }

  tags = local.default_tags
}
