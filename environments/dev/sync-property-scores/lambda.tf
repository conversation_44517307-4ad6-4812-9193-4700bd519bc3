module "lambda" {
  source          = "../../../modules/lambda-function-v2/"
  name            = local.app_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  disable_lb      = true
  description     = ""

  function_source = local.function_source

  runtime = "nodejs14.x"
  handler = "index.handler"
  timeout = 3

  tags = merge(local.default_tags, {})
}
