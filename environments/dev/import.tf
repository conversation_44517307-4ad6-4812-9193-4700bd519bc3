### Lambda Functions
import {
  id = "arn:aws:lambda:us-east-1:681574592108:function:sync-property-scores-dev"
  to = module.sync_property_scores.module.lambda.aws_lambda_function.function
}
import {
  id = "arn:aws:lambda:us-east-1:681574592108:function:TestAirtable"
  to = module.test_airtable.module.lambda.aws_lambda_function.function
}
import {
  id = "arn:aws:lambda:us-east-1:681574592108:function:airtable-integration-dev"
  to = module.airtable_integration.module.lambda.aws_lambda_function.function
}

### Lambda IAM
import {
  id = "sync-property-scores-dev-role-7t5uvq3w"
  to = module.sync_property_scores.module.lambda.aws_iam_role.function_role
}
import {
  id = "TestAirtable-role-x8tggzen"
  to = module.test_airtable.module.lambda.aws_iam_role.function_role
}
import {
  id = "airtable-integration-dev-role-hy9olwqx"
  to = module.airtable_integration.module.lambda.aws_iam_role.function_role
}

### Lambda SQS Trigger
import {
  id = "5326156b-a1d9-4486-b3a4-b192bb162b0a"
  to = module.airtable_integration.module.lambda.aws_lambda_event_source_mapping.sqs_trigger[0]
}
