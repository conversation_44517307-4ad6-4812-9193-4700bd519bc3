module "sqs-dev-property_assets-medical_off_market_property_news" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-medical_off_market_property_news"

  tags = local.default_tags
}

module "sqs-dev-property_assets-medical_on_market_property_news" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-medical_on_market_property_news"

  tags = local.default_tags
}

module "sqs-dev-property_assets-medical_office_calculated_values" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-medical_office_calculated_values"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_strategic_scores" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_strategic_scores"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_summarized_property_news" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_summarized_property_news"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_estimated_prices" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_estimated_prices"

  tags = local.default_tags
}

module "sqs-dev-property_assets-school_metrics" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-school_metrics"

  tags = local.default_tags
}

module "sqs-dev-property_assets-flood_zone_metrics" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-flood_zone_metrics"

  tags = local.default_tags
}

module "sqs-dev-property_assets-debt_and_transactability" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-debt_and_transactability"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_rent_percentiles" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_rent_percentiles"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_rent_thermometers" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_rent_thermometers"

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_rent_thermometer_scores" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_rent_thermometer_scores"

  tags = local.default_tags
}

module "sns-dev-property_assets-property_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-property_assets-property_news"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:dev-property_search-property_news",
  "arn:aws:sqs:us-east-1:681574592108:dev-comps-selector_properties-news"]

  tags = local.default_tags
}

module "sqs-dev-property_assets-multifamily_estimated_values" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-multifamily_estimated_values"

  tags = local.default_tags
}

module "sqs-dev-property_assets-property_multifamily_contacts" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-property_multifamily_contacts"

  tags = local.default_tags
}

module "sqs-dev-property_assets-identification_news" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-identification_news"

  tags = local.default_tags
}

module "sqs-dev-property_assets-properties_units_data" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-properties_units_data"

  tags = local.default_tags
}

module "sqs-dev-property_assets-properties_quality_data" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-property_assets-properties_quality_data"

  tags = local.default_tags
}


module "sns-dev-property_assets-multifamily_units_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-property_assets-multifamily_units_news"
  raw_sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-rent-multifamily_units_data"
  ]
  tags = local.default_tags
}

module "sns-dev-property_assets-multifamily_quality_news" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "dev-property_assets-multifamily_quality_news"
  raw_sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:dev-comps-selector_properties-quality-news"
  ]
  tags = local.default_tags
}