module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-assets-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    AWS_SQS_MULTIFAMILY_ESTIMATED_VALUES                   = "dev-property_assets-multifamily_estimated_values",
    AWS_SQS_MULTIFAMILY_CONTACTS                           = "dev-property_assets-property_multifamily_contacts",
    AWS_SQS_IDENTIFICATION_NEWS                            = "dev-property_assets-identification_news",
    AWS_SNS_MULTIFAMILY_UNITS_NEWS                         = "dev-property_assets-multifamily_units_news",
    AWS_SNS_MULTIFAMILY_QUALITY_NEWS                       = "dev-property_assets-multifamily_quality_news",
    DEFAULT_SQS_GROUP_DELAY                                = "100",
    AWS_DATABASE_SERVICE_NAME                              = "develop-db",
    AWS_SQS_PROPERTIES_UNITS_DATA                          = "dev-property_assets-properties_units_data",
    UNIT_RENOVATION_THRESHOLD                              = "0.65",
    AWS_SQS_PROPERTIES_QUALITY_DATA                        = "dev-property_assets-properties_quality_data"
    AWS_SQS_MULTIFAMILY_SUMMARIZED_PROPERTY_NEWS_WORKERS   = "2",
    AWS_SQS_MULTIFAMILY_SUMMARIZED_PROPERTY_NEWS_WAIT_TIME = "20",
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",

    AWS_SQS_MULTIFAMILY_PROPERTY_NEWS            = "/dev/property-assets-api/aws-sqs-multifamily-property-news",
    AWS_SQS_MULTIFAMILY_SUMMARIZED_PROPERTY_NEWS = "/dev/property-assets-api/aws-sqs-multifamily-summarized-property-news",
    AWS_SQS_MULTIFAMILY_ESTIMATED_PRICES         = "/dev/property-assets-api/aws-sqs-multifamily-estimated-prices",
    AWS_SQS_MULTIFAMILY_STRATEGIC_SCORES         = "/dev/property-assets-api/aws-sqs-multifamily-strategic-scores",
    AWS_SQS_MEDICAL_OFF_MARKET_PROPERTY_NEWS     = "/dev/property-assets-api/aws-sqs-medical-off-market-property-news",
    AWS_SQS_MEDICAL_ON_MARKET_PROPERTY_NEWS      = "/dev/property-assets-api/aws-sqs-medical-on-market-property-news",
    AWS_SQS_DEAL_EVENTS                          = "/dev/property-assets-api/aws-sqs-deal-events",
    AWS_SQS_MEDICAL_OFFICE_CALCULATED_VALUES     = "/dev/property-assets-api/aws-sqs-medical-office-calculated-values",
    AWS_SQS_FLOOD_ZONE_METRICS                   = "/dev/property-assets-api/aws-sqs-flood-zone-metrics",
    AWS_SQS_SCHOOL_METRICS                       = "/dev/property-assets-api/aws-sqs-school-metrics",
    AWS_SQS_DEBT_AND_TRANSACTABILITY             = "/dev/property-assets-api/aws-sqs-debt-and-transactability",
    AWS_SQS_MULTIFAMILY_MARKET_RENT              = "/dev/property-assets-api/aws-sqs-multifamily-market-rent",
    AWS_SQS_MULTIFAMILY_RENT_THERMOMETER         = "/dev/property-assets-api/aws_sqs_multifamily_rent_thermometer",
    AWS_SQS_MULTIFAMILY_RENT_THERMOMETER_SCORE   = "/dev/property-assets-api/aws_sqs_multifamily_rent_thermometer_score",

    API_URL = "/dev/global/property-assets-api-url",

    KEYGEN_URL = "/dev/global/keygen-api-url",

    POSTGRES_DB_URL      = "/dev/property-assets-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/property-assets-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/property-assets-db/db-api-password",

    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",
    DATADOG_ENABLED = "/any/datadog/enabled",

    AWS_SNS_PROPERTY_NEWS = "/dev/property-assets-api/aws-sns-property-news",
  }

  tags = local.default_tags
}
