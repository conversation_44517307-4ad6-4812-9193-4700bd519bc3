module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/demographics-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    API_URL         = "/dev/global/demographics-api-url",
    AWS_ACCESS_KEY  = "/any/aws/access-key-id",
    AWS_SECRET_KEY  = "/any/aws/secret-access-key",
    SCOPE           = "/any/global/server-scope",
    DATADOG_ENABLED = "/dev/demographics-api/datadog-enabled",
    DATADOG_API_KEY = "/any/datadog/api-key",

    # DYNAMO DB 
    DEMOGRAPHICS_DATA_TABLE = "/dev/demographics-api/demographics-table",
    DD_PROFILING_ENABLED    = "/dev/demographics-api/profiling-enabled",
    DYNAMODB_TIMEOUT        = "/dev/demographics-api/dynamodb-timeout",

    # CLOUD SEARCH
    CLOUD_SEARCH_SEARCH_ENDPOINT    = "/dev/demographics-api/cloudsearch-search-endpoint",
    CLOUD_SEARCH_DOCUMENTS_ENDPOINT = "/dev/demographics-api/cloudsearch-documents-endpoint",
    CLOUD_SEARCH_DOMAIN_NAME        = "/dev/demographics-api/cloudsearch-domain-name",

    # SQS
    AWS_SQS_NEW_DEVELOPMENT_STATS = "/dev/demographics-api/new-development-stats-queue",
    AWS_SQS_DEMOGRAPHICS_STATS    = "/dev/demographics-api/demographics-stats-queue",
    AWS_SQS_MARKET_STATS          = "/dev/demographics-api/market-stats-queue",
    AWS_SQS_STRATEGIC_SCORES      = "/dev/demographics-api/strategic-scores-queue",
    AWS_SQS_ZIP_MF_UNITS          = "/dev/demographics-api/zip-mf-units-queue",
    AWS_SQS_ZIP_NEWS              = "/dev/demographics-api/zip-news-queue",

    GEOGRAPHIC_API_URL = "/dev/global/geographics-api-url",
  }

  tags = local.default_tags
}
