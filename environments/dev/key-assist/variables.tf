variable "environment" {
  type = object({
    cluster = object({
      name             = string
      arn              = string
      dns_prefix       = string
      private_listener = string
      private_cname    = string
      public_listener  = string
      public_cname     = string
    })
    public_network = object({
      vpc             = string
      vpc_name        = string
      cidr_block      = string
      subnets         = list(string)
      route_tables    = list(string)
      security_groups = list(string)
      network_acls    = list(string)
      vpc_link        = string
      rds_subnet      = string
    })
    private_network = object({
      vpc             = string
      vpc_name        = string
      cidr_block      = string
      subnets         = list(string)
      route_tables    = list(string)
      security_groups = list(string)
      network_acls    = list(string)
      vpc_link        = string
      rds_subnet      = string
    })
    rds_server_config = object({
      host = string
      port = string
      user = string
      pass = string
    })
    chatbot_arn = string
  })
}
