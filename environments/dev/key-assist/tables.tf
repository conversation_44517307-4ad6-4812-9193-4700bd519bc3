module "api_sessions_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}_api_sessions_dev"
  capacity_mode       = "PAY_PER_REQUEST"
  deletion_protection = false
  hash_key            = "PK"
  range_key           = "SK"

  attributes = {
    PK        = "S"
    SK        = "S"
    UPDATE_AT = "S"
  }
  local_indexes = [
    {
      name            = "UPDATE_AT-index"
      projection_type = "ALL"
      range_key       = "UPDATE_AT"
    }
  ]
  # TODO: Check terraform provider for warm throughput feature

  tags = merge(local.default_tags, {})
}
