module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080


  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/key-assist-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_migration_stage = false

  # loads environment variables from parameter store:
  ecs_variables = {
    # VAR_NAME = "string"
    DD_PROFILING_ENABLED           = true
    KEY_ASSIST_SESSIONS_TABLE_NAME = "key-assist_api_sessions_dev"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    DATA_GPT_API_URL                   = "/dev/key-assist-api/data-gpt-api-url",
    DATA_GPT_SOCKET_TIMEOUT_CONFIG     = "/dev/key-assist-api/data-gpt-socket-timeout",
    DATA_GPT_CONNECTION_TIMEOUT_CONFIG = "/dev/key-assist-api/data-gpt-connection-timeout",
    QUERY_BY_PROPERTY_ID_AUTH_TOKEN    = "/dev/key-assist-api/data-gpt-query-auth-token",

    SCOPE   = "/any/global/server-scope",
    API_URL = "/dev/global/key-assist-api-url",

    DATADOG_ENABLED = "/dev/key-assist-api/datadog-enabled",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    KEY_ASSIST_BUCKET_NAME = "/dev/key-assist-api/s3-bucket-name",
    OPEN_AI_TOKEN          = "/dev/key-assist-api/open-ai-token",

    SECURITY_ENABLED       = "/dev/key-assist-api/security_enabled",
    AUTH0_DOMAIN           = "/dev/key-assist-api/auth0_domain",
    AUTH0_AUDIENCES        = "/dev/key-assist-api/auth0_audiences",
    AUTH0_ISSUER           = "/dev/key-assist-api/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID = "/dev/global/keyway-organization-id"
  }

  tags = local.default_tags
}
