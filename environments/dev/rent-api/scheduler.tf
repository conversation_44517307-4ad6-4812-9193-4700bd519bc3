locals {
  rent_api_dev_delete_unit_rent_data_requests = {
    "delete_data_day" : {
      "cron" : "0 15 * * ? *",
      "limit" : 5000,
      "type" : "DELETE_DATA"
    },
    # "night" : { 
    #     "cron" : "0 01-11 * * ? *",
    #     "limit": 10000
    # },

    "last_rent_notification" : {
      "cron" : "0 1 * * ? *",
      "limit" : 1000,
      "type" : "LAST_RENT_NOTIFICATION"
    }
  }
}

resource "aws_iam_role" "rent_api_dev_schedule_events_sqs_role" {
  name = "rent_api_dev_schedule_events_sqs_role"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "scheduler.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      },
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_policy" "rent_api_dev_schedule_events_sqs_policy" {
  name = "rent_api_dev_schedule_events_sqs_policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessage"
        ],
        "Resource" : [
          "arn:aws:sqs:us-east-1:681574592108:dev-rent-background_tasks",
          "arn:aws:sqs:us-east-1:681574592108:dev-rent-background_tasks"
        ]
      }
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_role_policy_attachment" "rent_api_dev_schedule_events_sqs_role_attach" {
  role       = aws_iam_role.rent_api_dev_schedule_events_sqs_role.name
  policy_arn = aws_iam_policy.rent_api_dev_schedule_events_sqs_policy.arn
}

resource "aws_scheduler_schedule_group" "rent_api_dev_schedule_group_name" {
  name = "rent_api_dev"
  tags = local.default_tags
}


resource "aws_scheduler_schedule" "rent_api_dev_schedule_events" {
  for_each            = local.rent_api_dev_delete_unit_rent_data_requests
  name                = "rent_api_dev_delete_unit_rent_data_${each.key}"
  group_name          = aws_scheduler_schedule_group.rent_api_dev_schedule_group_name.name
  schedule_expression = "cron(${each.value.cron})"
  flexible_time_window {
    mode = "OFF"
  }
  target {
    arn      = module.sqs-dev-rent-background_tasks.arn
    role_arn = aws_iam_role.rent_api_dev_schedule_events_sqs_role.arn
    input = jsonencode(
      {
        "request_type" : each.key,
        "environment" : "dev",
        "limit" : each.value.limit,
        "type" : each.value.type
      }
    )
  }
}
