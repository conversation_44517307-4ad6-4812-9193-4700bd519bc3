module "sqs-dev-rent-background_tasks" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-background_tasks"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-dev-rent-unit_rent_data" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-unit_rent_data"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-dev-rent-property_concessions_fifo" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-property_concessions"
  fifo_queue        = true
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-dev-rent-property_concessions" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-property_concessions"
  fifo_queue        = false
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-dev-rent-multifamily_units_dat" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-multifamily_units_data"
  tags              = local.default_tags
  max_receive_count = 3
}

module "sqs-dev-rent-property_last_rent_data" {
  source            = "../../../modules/sqs-queue"
  queue_name        = "dev-rent-property_last_rent_data"
  tags              = local.default_tags
  max_receive_count = 3
}