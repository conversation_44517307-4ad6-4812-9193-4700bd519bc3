locals {
  asset_manager_dev_realpage_requests = {
    "floorPlans" : { "cron" : "0 10 ? * 2-6 *" },
    "units" : { "cron" : "15 10 ? * 2-6 *" },
    "leases" : { "cron" : "30 10 ? * 2-6 *" },
    "leaseCharges" : { "cron" : "45 10 ? * 2-6 *" },
    "accountBalances" : { "cron" : "0 11 ? * 2-6 *" }
  }
}

resource "aws_iam_role" "asset_manager_dev_schedule_events_sqs_role" {
  name = "asset_manager_dev_schedule_events_sqs_role"

  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Service" : "scheduler.amazonaws.com"
        },
        "Action" : "sts:AssumeRole"
      },
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_policy" "asset_manager_dev_schedule_events_sqs_policy" {
  name = "asset_manager_dev_schedule_events_sqs_policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessage"
        ],
        "Resource" : [
          "arn:aws:sqs:us-east-1:************:dev-real_page_client_requests",
          "arn:aws:sqs:us-east-1:************:dev-real_page_client_requests-DLQ"
        ]
      }
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_policy" "asset_manager_dev_schedule_event_generator_sqs_policy" {
  name = "asset_manager_dev_schedule_event_generator_sqs_policy"

  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessage"
        ],
        "Resource" : [
          "arn:aws:sqs:us-east-1:************:dev-real_page_client_event_generator",
          "arn:aws:sqs:us-east-1:************:dev-real_page_client_event_generatorr-DLQ"
        ]
      }
    ]
  })

  tags = local.default_tags
}

resource "aws_iam_role_policy_attachment" "asset_manager_dev_schedule_events_sqs_role_attach" {
  role       = aws_iam_role.asset_manager_dev_schedule_events_sqs_role.name
  policy_arn = aws_iam_policy.asset_manager_dev_schedule_event_generator_sqs_policy.arn
}

resource "aws_scheduler_schedule_group" "asset_manager_dev_schedule_group_name" {
  name = "asset_manager_dev"
  tags = local.default_tags
}

resource "aws_scheduler_schedule" "asset_manager_dev_schedule_events" {
  for_each            = local.asset_manager_dev_realpage_requests
  name                = "asset_manager_dev_${each.key}"
  group_name          = aws_scheduler_schedule_group.asset_manager_dev_schedule_group_name.name
  schedule_expression = "cron(${each.value.cron})"
  flexible_time_window {
    mode = "OFF"
  }
  target {
    arn      = module.sqs_realpage_event_generator.arn
    role_arn = aws_iam_role.asset_manager_dev_schedule_events_sqs_role.arn
    input = jsonencode(
      {
        "request_type" : each.key,
        "environment" : "dev"
      }
    )
  }
}
