# Examples and optional values moved to
# https://www.notion.so/whykeyway/New-ECS-Deployment-1c5074d59c8e452991a4b33891558b5f
module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/asset-manager-api"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 12 ? * MON *)" # Every Monday at 12:00 UTC

  codebuild_image           = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    SCOPE   = "/any/global/server-scope",
    API_URL = "/dev/asset-manager-api/api-url",

    # DATADOG
    DATADOG_ENABLED = "/dev/asset-manager-api/datadog-enabled",
    DATADOG_API_KEY = "/any/datadog/api-key",

    # AWS
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    # SQS
    READ_EXCEL_FILE_SQS     = "/dev/asset-manager-api/aws-sqs-read-excel-file-queue-name",
    EXCEL_FILE_RESULT_SQS   = "/dev/asset-manager-api/excel_file_result_sqs",
    REAL_PAGE_EVENTS_SQS    = "/dev/asset-manager-api/real_page_events_sqs",
    YARDI_NOTIFICATIONS_SQS = "/dev/asset-manager-api/yardi-updates-queue-name",

    # DB
    POSTGRES_DB_URL      = "/dev/asset-manager-db/db-connection-string",
    POSTGRES_DB_USER     = "/dev/asset-manager-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/dev/asset-manager-db/db-api-password",

    # Services urls
    K_FILE_SERVICE_URL      = "/dev/global/kfile-api-url",
    PROPERTY_ASSETS_API_URL = "/dev/global/property-assets-api-url",

    # Auth0
    AUTH0_DOMAIN   = "/dev/asset-manager-api/auth0_domain",
    AUTH0_AUDIENCE = "/dev/asset-manager-api/auth0_audience",
    AUTH0_ISSUER   = "/dev/asset-manager-api/auth0_token_issuer",

    # SPLIT IO
    SPLIT_IO_TOKEN = "/dev/asset-manager-api/split_io_token"
  }

  tags = local.default_tags
}
