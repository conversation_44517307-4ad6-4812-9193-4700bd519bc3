module "sqs_plan_loaded" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-asset_manager-plan_loaded"

  receive_wait_time_seconds = 20

  tags = local.default_tags
}

module "sqs_realpage_requests" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-real_page_client_requests"

  receive_wait_time_seconds = 20

  tags = local.default_tags
}

module "sqs_realpage_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-real_page_client_events"

  receive_wait_time_seconds = 20

  tags = local.default_tags
}

module "sqs_realpage_event_generator" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-real_page_client_event_generator"

  receive_wait_time_seconds = 20

  tags = local.default_tags
}

module "sqs_yardi_notifications" {
  source     = "../../../modules/sqs-queue"
  queue_name = "dev-asset_manager-yardi-notifications"

  tags = local.default_tags
}
