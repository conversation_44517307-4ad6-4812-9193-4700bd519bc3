module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1 * 1024
    memory = 2 * 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  iam_extra_policies = ["arn:aws:iam::aws:policy/AmazonBedrockFullAccess"]

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/keyway-mcp-tools"
  github_branch_name = "develop"

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
    ENV   = "dev"
    SCOPE = "server"
    PORT  = "8080"

    LANGCHAIN_TRACING_V2 = "true"
    LANGCHAIN_ENDPOINT   = "https://api.smith.langchain.com"
    LANGCHAIN_PROJECT    = "mcp-tools-dev"

    DD_PROFILING_ENABLED          = "true"
    DATADOG_ENABLED               = "true"
    DD_RUNTIME_METRICS_ENABLED    = "true"
    DD_PROFILING_TIMELINE_ENABLED = "true"

    # Number of pages to retrieve from the documents
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION                = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"
    GH_SSH_PRIVATE_KEY    = "/any/github/private-ssh-key"

    OPENAI_API_KEY               = "${local.ecs_parameter_prefix}/OPENAI_API_KEY"
    LANGCHAIN_API_KEY            = "${local.ecs_parameter_prefix}/LANGCHAIN_API_KEY"
    PROPERTY_ANALYZER_SECRET_KEY = "/any/property-analyzer-api/keyway-key"
    KEYWAY_API_KEY               = "${local.ecs_parameter_prefix}/KEYWAY_API_KEY"
  }

  tags = local.default_tags
}
