module "lambda" {
  source = "../../../modules/lambda-function-v2/"

  name            = local.lambda_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  public_endpoint = false

  function_source = {
    source = "ECR"
  }
  memory_size = 512
  timeout     = 600 # 1-900 seconds

  disable_lb  = true
  sqs_trigger = {
    queue_arn = "arn:aws:sqs:us-east-1:681574592108:sre-mostacho-champions-issues-to-reassign.fifo"
    enabled   = true
    tags      = local.default_tags
  }

  extra_iam_policy_arns = [
    aws_iam_policy.dynamodb_champions.arn,
    aws_iam_policy.sqs_queue.arn
  ]

  environment_variables = {
    DYNAMODB_TABLE = "mostacho-protector-champions"
    GITHUB_ORG     = "unlockre"
  }

  # these are not updated on runtime, terraform needs to be applied after changes
  environment_secrets = {
    GITHUB_TOKEN = "/sre/mostacho-protector/github_token"
  }

  tags = local.default_tags
}
