module "cloudtrail_s3" {
  source = "../../modules/s3-bucket"

  bucket_name            = "cloudtrail-keyway"
  cleanup_lifecycle_days = 365
  policy_extra_services  = ["cloudtrail.amazonaws.com", "guardduty.amazonaws.com"]
}

resource "aws_cloudtrail" "cloudtrail" {
  name           = "cloudtrail-keyway"
  s3_bucket_name = "cloudtrail-keyway"
  depends_on     = [module.cloudtrail_s3]

  include_global_service_events = true
  is_multi_region_trail         = true
  enable_log_file_validation    = true

  event_selector {
    read_write_type           = "WriteOnly" # "All" | "ReadOnly"
    include_management_events = true
    # data_resource {
    #   type   = "AWS::S3::Object"
    #   values = ["arn:aws:s3:::"]
    # }
    # data_resource {
    #   type   = "AWS::Lambda::Function"
    #   values = ["arn:aws:lambda"]
    # }
    # data_resource {
    #   type   = "AWS::DynamoDB::Table"
    #   values = ["arn:aws:dynamodb:*:*:table/*"]
    # }
  }
}

resource "aws_cloudwatch_event_rule" "guardduty_findings" {
  name        = "guardduty-findings-rule"
  description = "Trigger for GuardDuty findings"

  event_pattern = jsonencode({
    source        = ["aws.guardduty"],
    "detail-type" = ["GuardDuty Finding"]
  })
}

resource "aws_cloudwatch_event_target" "sns_target" {
  rule      = aws_cloudwatch_event_rule.guardduty_findings.name
  target_id = "SendToSNS"
  arn       = module.chatbot_guardduty.chatbot_sns_arn
}
