resource "aws_cloudwatch_event_rule" "fargate_spot_instance_reclaim" {
  name        = "fargate-spot-instance-reclaim-rule"
  description = "ECS Spot Instance reclaimed"
  event_pattern = jsonencode({
    "source" : ["aws.ecs"],
    "detail-type" : ["ECS Task State Change"],
    "detail" : {
      "stopCode" : ["SpotInterruption"],
      "lastStatus" : ["STOPPED"]
    }
  })
}

resource "aws_cloudwatch_event_target" "chatbot_target" {
  depends_on = [aws_cloudwatch_event_rule.fargate_spot_instance_reclaim]
  rule       = aws_cloudwatch_event_rule.fargate_spot_instance_reclaim.name
  arn        = module.chatbot_fargate_spots.chatbot_sns_arn
}

resource "aws_cloudwatch_event_rule" "fargate_spot_placement_failure" {
  name        = "fargate-spot-instance-placement-rule"
  description = "ECS Fargate Task failed to place on Spot Instance"
  event_pattern = jsonencode({
    "source" : ["aws.ecs"],
    "detail-type" : ["ECS Deployment State Change"],
    "detail" : {
      "eventName" : ["SERVICE_TASK_PLACEMENT_FAILURE"],
      "reason" : ["RESOURCE:FARGATE"]
    }
  })
}

resource "aws_cloudwatch_event_target" "chatbot_failures" {
  depends_on = [aws_cloudwatch_event_rule.fargate_spot_placement_failure]
  rule       = aws_cloudwatch_event_rule.fargate_spot_placement_failure.name
  arn        = module.chatbot_fargate_spots.chatbot_sns_arn
}
