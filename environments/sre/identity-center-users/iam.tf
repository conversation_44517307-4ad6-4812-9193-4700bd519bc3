resource "aws_iam_policy" "restrict_iam_access" {
  name        = "RestrictIAMAccess"
  description = "Restricts IAM and IAM Identity Center access to users"

  policy = jsonencode({
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Deny",
            "Action": [
                "iam:*",
                "identitystore:*"
            ],
            "Resource": "*"
        }
    ]
  })
}
