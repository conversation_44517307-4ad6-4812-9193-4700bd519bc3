module "identity_center_users" {
  source = "../../../modules/iam-identity-center-users"

  user_assignments = [
    {
      user            = "<EMAIL>"
      permission_sets = ["Leader"]
    },
    {
      user            = "esteban<PERSON><PERSON>@whykeyway.com"
      permission_sets = ["Developer", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Developer", "Frontend", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["SRE"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["SRE"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Developer", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["SRE"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Developer", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Frontend", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Bedrock", "Developer", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Developer", "Lambda"]
    },
    {
      user            = "<EMAIL>"
      permission_sets = ["Leader"]
    }
  ]

  permission_sets = [
    {
      name             = "SRE"
      description      = "Grants administrator permissions"
      session_duration = "PT12H"
      aws_policies = [
        "arn:aws:iam::aws:policy/AdministratorAccess",
        "arn:aws:iam::aws:policy/AmazonSSMFullAccess",
        "arn:aws:iam::aws:policy/job-function/Billing",
        "arn:aws:iam::aws:policy/job-function/SystemAdministrator"
      ]
    },
    {
      name        = "Lambda"
      description = "Access to Lambda resources, related pipelines, and more"
      aws_policies = [
        "arn:aws:iam::aws:policy/AWSLambda_FullAccess",
        "arn:aws:iam::aws:policy/CloudWatchFullAccess",
        "arn:aws:iam::aws:policy/CloudWatchFullAccessV2",
        "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
      ]
      customer_policies = [
        "arn:aws:iam::681574592108:policy/CodeBuild-ECRFull",
        aws_iam_policy.restrict_iam_access.arn
      ]
      inline_policies = <<POLICY
      {
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "ec2:CreateNetworkInterface",
                "ec2:DetachNetworkInterface",
                "ec2:DescribeNetworkInterfaces",
                "ec2:CreateNetworkInterfacePermission",
                "ec2:DescribeTags",
                "ec2:DescribeNetworkInterfaceAttribute",
                "ec2:CreateTags",
                "ec2:ResetNetworkInterfaceAttribute",
                "ec2:ModifyNetworkInterfaceAttribute",
                "ec2:AttachNetworkInterface",
                "ec2:DescribeNetworkInterfacePermissions"
            ],
            "Resource": "*"
        }
    ]
}
POLICY
    },
    {
      name             = "Developer"
      description      = "Grants read access to every resource, as well as permissions to resources usually accessed by developers. (E.g.: ECS, SNS, SQS, S3, DynamoDB, etc.)"
      session_duration = "PT12H"
      aws_policies = [
        "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess",
        "arn:aws:iam::aws:policy/AmazonECS_FullAccess",
        "arn:aws:iam::aws:policy/AmazonS3FullAccess",
        "arn:aws:iam::aws:policy/AmazonSNSFullAccess",
        "arn:aws:iam::aws:policy/AmazonSQSFullAccess",
        "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess",
        "arn:aws:iam::aws:policy/CloudSearchFullAccess",
        "arn:aws:iam::aws:policy/ReadOnlyAccess"
      ]
      customer_policies = [
        "arn:aws:iam::681574592108:policy/Force_MFA",
        aws_iam_policy.restrict_iam_access.arn
      ]
      inline_policies = <<POLICY
      {
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "ssm:PutParameter",
                "ssm:LabelParameterVersion",
                "ssm:DeleteParameter",
                "ssm:UnlabelParameterVersion",
                "ssm:GetParameterHistory",
                "ssm:GetParametersByPath",
                "ssm:GetParameters",
                "ssm:GetParameter",
                "ssm:DeleteParameters"
            ],
            "Resource": "arn:aws:ssm:*:681574592108:parameter/*"
        },
        {
            "Sid": "VisualEditor1",
            "Effect": "Allow",
            "Action": "ssm:DescribeParameters",
            "Resource": "*"
        }
    ]
}
POLICY
    },
    {
      name        = "Frontend",
      description = "Access required to handle CDN and its origin's S3 bucket"
      aws_policies = [
        "arn:aws:iam::aws:policy/AmazonECS_FullAccess",
        "arn:aws:iam::aws:policy/AmazonS3FullAccess",
        "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess",
        "arn:aws:iam::aws:policy/AWSLambda_FullAccess",
        "arn:aws:iam::aws:policy/CloudWatchFullAccess",
        "arn:aws:iam::aws:policy/CloudWatchFullAccessV2",
        "arn:aws:iam::aws:policy/ReadOnlyAccess",
      ]
      customer_policies = [
        "arn:aws:iam::681574592108:policy/Force_MFA",
        aws_iam_policy.restrict_iam_access.arn
      ]
    },
    {
      name        = "Bedrock"
      description = "Full access to Bedrock, Marketplace and Cloudwatch"
      aws_policies = [
        "arn:aws:iam::aws:policy/AmazonBedrockFullAccess",
        "arn:aws:iam::aws:policy/AWSMarketplaceFullAccess",
        "arn:aws:iam::aws:policy/CloudWatchFullAccessV2"
      ]
    },
    {
      name        = "Leader"
      description = "Full access to API Gateway, Lambda, IAM, and resources required for application and development operations"
      aws_policies = [
        "arn:aws:iam::aws:policy/ReadOnlyAccess",
        "arn:aws:iam::aws:policy/PowerUserAccess"
      ]
      customer_policies = [
        aws_iam_policy.restrict_iam_access.arn
      ]
    }
  ]
}
