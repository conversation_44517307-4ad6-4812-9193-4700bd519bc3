module "lambda" {
  source          = "../../../modules/lambda-function-v2/"
  name            = local.app_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  description     = "Redirects users from our own aws.whykeyway.com domain over to our AWS SSO Start page"

  function_source = {
    source = "ECR"
  }

  timeout = 60

  environment_variables = {
    "REDIRECT_TARGET" = "https://d-9067fb146b.awsapps.com/start/#/"
  }

  tags = merge(local.default_tags, {})
}
