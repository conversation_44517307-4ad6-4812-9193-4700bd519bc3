resource "aws_ecr_repository" "repo" {
  name                 = "${var.environment.cluster.name}-${local.app_name}"
  image_tag_mutability = "MUTABLE"
  force_delete         = true

  image_scanning_configuration {
    scan_on_push = false
  }

  tags = local.default_tags
}

# Only one aws_ecr_lifecycle_policy resource can be used with the same ECR repository
resource "aws_ecr_lifecycle_policy" "policy" {
  repository = aws_ecr_repository.repo.name

  policy = <<EOF
{
  "rules":[
    {
      "rulePriority": 1,
      "description": "Retain only the 5 most recent images",
      "selection": {
        "tagStatus": "any",
        "countType": "imageCountMoreThan",
        "countNumber": 5
      },
      "action": {
        "type": "expire"
      }
    }
  ]
}
EOF
}

resource "aws_ecr_repository_policy" "repo_policy" {
  repository = aws_ecr_repository.repo.name
  policy     = data.aws_iam_policy_document.repo_policy_document.json
}

data "aws_iam_policy_document" "repo_policy_document" {
  statement {
    sid    = "AllowEC2InstancesToPullImages"
    effect = "Allow"

    actions = [
      "ecr:ListImages",
      "ecr:GetDownloadUrlForLayer",
      "ecr:GetAuthorizationToken",
      "ecr:DescribeRepositories",
      "ecr:DescribeImages",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
    ]

    principals {
      type        = "AWS"
      identifiers = [module.metabase_proxy.iam_role]
    }
  }

  statement {
    sid    = "FullAccessForIamUsers"
    effect = "Allow"

    actions = [
      "ecr:*",
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
  }
}
