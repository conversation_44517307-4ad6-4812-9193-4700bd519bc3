module "metabase_proxy" {
  source          = "../../../modules/ec2-with-eip"
  ami_id          = "ami-04b4f1a9cf54c11d0" # ubuntu 24.04 x86
  instance_type   = "t3.small"
  key_name        = "unlock"
  subnet_id       = var.environment.public_network.subnets[1]
  security_groups = concat(var.environment.private_network.security_groups, [aws_security_group.metabase-proxy.id, aws_security_group.gcp-addresses.id])
  volume_size     = 50
  instance_name   = local.ecs_name
  user_data       = local.metabase_init_script
  extra_policies  = [aws_iam_policy.docker_secrets_policy.arn]
  tags            = local.default_tags
}
