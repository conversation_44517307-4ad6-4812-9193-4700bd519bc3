resource "aws_security_group" "metabase-proxy" {
  name        = local.app_name
  description = "Security group for ${local.app_name}"
  vpc_id      = var.environment.public_network.vpc

  // Allow inbound traffic from Metabase IPs
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = local.metabase_ips
  }

  // Allow all outbound traffic to Metabase IPs
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = local.metabase_ips
  }

  tags = merge(local.default_tags, { "Name" = local.app_name })
}

resource "aws_security_group" "gcp-addresses" {
  name        = "gcp-addresses"
  description = "Security group including Google Cloud Platform IP addresses"
  vpc_id      = var.environment.public_network.vpc

  ingress {
    from_port = 10000
    to_port   = 10100
    protocol  = "tcp"
    cidr_blocks = [
      "************/23"
    ]
  }
  egress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    cidr_blocks = [
      "************/23"
    ]
  }

  tags = merge(local.default_tags, { "Name" = local.app_name })
}
