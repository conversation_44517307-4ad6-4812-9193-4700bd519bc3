resource "aws_iam_policy" "docker_secrets_policy" {
  name        = "sre-metabase-docker-secrets"
  description = "Policy for SRE Metabase"
  policy      = data.aws_iam_policy_document.docker_secrets_document.json
}

data "aws_iam_policy_document" "docker_secrets_document" {
  statement {
    sid    = "ReadDockerSecrets"
    effect = "Allow"

    actions = [
      "secretsmanager:GetSecretValue",
    ]

    resources = [
      "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.id}:secret:/sre/docker/*",
    ]
  }
}
