resource "aws_security_group" "openvpn" {
  name        = "openvpn"
  description = "OpenVPN access"
  vpc_id      = var.environment.public_network.vpc

  ingress {
    description = "openvpn (udp)"
    from_port   = 1194
    to_port     = 1194
    protocol    = "udp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "openvpn"
  }
  tags_all = {
    Name = "openvpn"
  }
}

resource "aws_security_group" "openvpn-efs" {
  name        = "openvpn-efs"
  description = "Security group for OpenVPN EFS"
  vpc_id      = var.environment.public_network.vpc

  ingress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.openvpn.id]
  }

  egress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    security_groups = [aws_security_group.openvpn.id]
  }
}
