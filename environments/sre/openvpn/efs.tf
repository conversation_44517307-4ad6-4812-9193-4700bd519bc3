module "sre_openvpn_efs" {
  source          = "../../../modules/efs-mount"
  disk_name       = "${local.app_name}-efs"
  subnet_ids      = [var.environment.public_network.subnets[0], var.environment.public_network.subnets[1]]
  security_groups = concat(var.environment.private_network.security_groups, [aws_security_group.openvpn.id])
  tags            = local.default_tags
}

module "sre_openvpn_efs_backup" {
  source = "../../../modules/aws-backup"
  vault  = "sre-${local.app_name}"
  resources_arn = [
    module.sre_openvpn_efs.arn
  ]
  tags = local.default_tags
}
