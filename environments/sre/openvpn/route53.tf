resource "aws_route53_record" "openvpn_dns" {
  zone_id = local.zone_id
  name    = local.service_url
  type    = "A"
  ttl     = "300"
  records = [module.ec2_2024_a.public_ip, module.ec2_2024_b.public_ip]
}

resource "aws_route53_record" "openvpn_dns_a" {
  zone_id = local.zone_id
  name    = "a.${local.service_url}"
  type    = "A"
  ttl     = "300"
  records = [module.ec2_2024_a.private_ip]
}

resource "aws_route53_record" "openvpn_dns_b" {
  zone_id = local.zone_id
  name    = "b.${local.service_url}"
  type    = "A"
  ttl     = "300"
  records = [module.ec2_2024_b.private_ip]
}

resource "aws_route53_record" "openvpn_dns_a_ext" {
  zone_id = local.zone_id
  name    = "a-ext.${local.service_url}"
  type    = "A"
  ttl     = "300"
  records = [module.ec2_2024_a.public_ip]
}

resource "aws_route53_record" "openvpn_dns_b_ext" {
  zone_id = local.zone_id
  name    = "b-ext.${local.service_url}"
  type    = "A"
  ttl     = "300"
  records = [module.ec2_2024_b.public_ip]
}
