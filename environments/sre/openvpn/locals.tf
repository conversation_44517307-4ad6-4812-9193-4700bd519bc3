data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

locals {
  app_name    = "openvpn"
  zone_id     = "Z05531162DGNWUXR3CVOO"
  service_url = "vpn.whykeyway.com"

  default_tags = merge(aws_servicecatalogappregistry_application.application.application_tag, {
    "service" = local.app_name
    "env"     = var.environment.private_network.vpc_name
    "cluster" = var.environment.cluster.name
  })

  sre_openvpn_init_script = templatefile("${path.module}/init_script.sh", {
    efs_id   = module.sre_openvpn_efs.efs_fs_id,
    ssl_host = "*.${local.service_url}"
  })
}
