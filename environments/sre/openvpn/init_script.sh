#!/bin/bash

# --- Install Docker ---
cd ~
apt-get remove -y docker docker-engine docker.io containerd runc
apt update
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo \
"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
$(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt update
apt install -y docker-ce docker-ce-cli containerd.io
usermod -aG docker ubuntu
systemctl enable docker
systemctl start docker

# --- Install AWS CLI ---
cd ~
apt install -y curl unzip
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws

# --- Setup EFS Utils ---
cd ~
apt-get -y install git binutils nfs-common cargo pkg-config openssl libssl-dev && \
git clone https://github.com/aws/efs-utils && \
cd efs-utils && \
./build-deb.sh && \
apt-get -y install ./build/amazon-efs-utils*deb

# --- Mount EFS ---
cd ~
mkdir -p /root/etc-openvpn/
mount -t efs -o tls ${efs_id}:/ /root/etc-openvpn/
echo "${efs_id}:/ /root/etc-openvpn/ efs _netdev,noresvport,tls,iam 0 0" >> /etc/fstab

# --- Secure Docker with TLS ---
cd ~
DOCKER_CERT_PATH="/etc/docker/certs"
mkdir -p "$DOCKER_CERT_PATH"

# --- Step 1: Download the CA Certificate ---
SECRET_NAME_PEM="/sre/docker/ca-pem"
SECRET_NAME_KEY="/sre/docker/ca-key-pem"
SECRET_NAME_PASS="/sre/docker/ca-passphrase"
aws secretsmanager get-secret-value --region "us-east-1" --secret-id "$SECRET_NAME_PEM" --query SecretString --output text > "$DOCKER_CERT_PATH/ca.pem"
aws secretsmanager get-secret-value --region "us-east-1" --secret-id "$SECRET_NAME_KEY" --query SecretString --output text > "$DOCKER_CERT_PATH/ca-key.pem"
aws secretsmanager get-secret-value --region "us-east-1" --secret-id "$SECRET_NAME_PASS" --query SecretString --output text > "$DOCKER_CERT_PATH/ca-passphrase.txt"

# --- Step 2: Generate the Server Private Key ---
openssl genrsa -out "$DOCKER_CERT_PATH/server-key.pem" 4096

# --- Step 3: Generate the Certificate Signing Request (CSR) ---
openssl req -new -key "$DOCKER_CERT_PATH/server-key.pem" -out "$DOCKER_CERT_PATH/server.csr" -subj "/CN=$(hostname)"

# --- Step 4: Sign the Server Certificate using CA ---
TOKEN=$(curl -sX PUT "http://***************/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
VPC_IP=$(curl -sH "X-aws-ec2-metadata-token: $TOKEN" "http://***************/latest/meta-data/local-ipv4")
echo "subjectAltName = DNS:${ssl_host},IP:$VPC_IP,IP:$(curl -s ifconfig.me)" > "$DOCKER_CERT_PATH/extfile.cnf"
openssl x509 -req -days 365 -sha256 -in "$DOCKER_CERT_PATH/server.csr" \
  -CA "$DOCKER_CERT_PATH/ca.pem" -CAcreateserial -CAkey "$DOCKER_CERT_PATH/ca-key.pem" \
  -out "$DOCKER_CERT_PATH/server-cert.pem" -extfile "$DOCKER_CERT_PATH/extfile.cnf" \
  -passin file:"$DOCKER_CERT_PATH/ca-passphrase.txt"

# --- Step 5: Configure Docker to Use TLS ---
cat <<EOF > /etc/docker/daemon.json
{
  "tls": true,
  "tlsverify": true,
  "tlscacert": "$DOCKER_CERT_PATH/ca.pem",
  "tlscert": "$DOCKER_CERT_PATH/server-cert.pem",
  "tlskey": "$DOCKER_CERT_PATH/server-key.pem",
  "hosts": ["tcp://0.0.0.0:2376", "unix:///var/run/docker.sock"]
}
EOF

# --- Step 6: Cleanup ---
rm -f "$DOCKER_CERT_PATH/ca-key.pem" "$DOCKER_CERT_PATH/ca-passphrase.txt" "$DOCKER_CERT_PATH/server.csr" "$DOCKER_CERT_PATH/extfile.cnf"

# https://stackoverflow.com/questions/44052054/unable-to-start-docker-after-configuring-hosts-in-daemon-json
cp /lib/systemd/system/docker.service /etc/systemd/system/
sed -i 's/\ -H\ fd:\/\///g' /etc/systemd/system/docker.service

# Add ubuntu to the docker group
usermod -aG docker ubuntu

# Restart Docker to apply changes
systemctl daemon-reload
systemctl restart docker
echo "Docker is now secured with TLS on port 2376!"
