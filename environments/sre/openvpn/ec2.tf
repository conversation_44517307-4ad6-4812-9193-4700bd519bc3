module "ec2_2024_a" {
  source          = "../../../modules/ec2-with-eip"
  ami_id          = "ami-04b4f1a9cf54c11d0" # ubuntu 24.04 x86
  instance_type   = "t3.small"
  key_name        = "unlock"
  subnet_id       = var.environment.public_network.subnets[0]
  security_groups = concat(var.environment.private_network.security_groups, [aws_security_group.openvpn.id])
  volume_size     = 20
  instance_name   = "${local.app_name}-a-24"
  user_data       = local.sre_openvpn_init_script
  extra_policies  = [aws_iam_policy.docker_secrets_policy.arn, "arn:aws:iam::aws:policy/AmazonElasticFileSystemsUtils"]
  tags            = local.default_tags
}

module "ec2_2024_b" {
  source          = "../../../modules/ec2-with-eip"
  ami_id          = "ami-04b4f1a9cf54c11d0" # ubuntu 24.04 x86
  instance_type   = "t3.small"
  key_name        = "unlock"
  subnet_id       = var.environment.public_network.subnets[1]
  security_groups = concat(var.environment.private_network.security_groups, [aws_security_group.openvpn.id])
  volume_size     = 20
  instance_name   = "${local.app_name}-b-24"
  user_data       = local.sre_openvpn_init_script
  extra_policies  = [aws_iam_policy.docker_secrets_policy.arn, "arn:aws:iam::aws:policy/AmazonElasticFileSystemsUtils"]
  tags            = local.default_tags
}
