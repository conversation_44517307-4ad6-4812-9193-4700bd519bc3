module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = false

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/ecs-on-off-bot"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * FRI *)" # Every Friday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # U02FU5EFGCT: <EMAIL>
    # U02U590NPEW: <EMAIL>
    # U07Q6TR9WJH: <EMAIL>
    SLACK_ADMIN_IDS = "U02FU5EFGCT,U02U590NPEW,U07Q6TR9WJH"
    # C0793NB9Q07: #sre-ecs-bot
    TARGET_CHANNEL     = "C0793NB9Q07"
    PROTECTED_SERVICES = "nginx-aws-gateway,ecs-on-off-bot,mostacho-protector,datadog-synthetics"
  }

  ecs_secrets = {
    SLACK_APP_TOKEN      = "${local.ecs_parameter_prefix}/slack_app_token",
    SLACK_BOT_TOKEN      = "${local.ecs_parameter_prefix}/slack_bot_token",
    SLACK_SIGNING_SECRET = "${local.ecs_parameter_prefix}/slack_signing_secret",
  }

  iam_extra_policies = [
    "arn:aws:iam::aws:policy/AmazonECS_FullAccess"
  ]

  tags = local.default_tags
}
