module "chatbot_deployments_sre" {
  source           = "../../modules/aws-chatbot"
  name             = "deployments-sre"
  slack_channel_id = "C03SKE7D4LT" # notify-deploy-sre
  publishing_aws_services = {
    "codepipeline" = "codestar-notifications.amazonaws.com"
  }
}

module "chatbot_backups" {
  source           = "../../modules/aws-chatbot"
  name             = "notify-backups"
  slack_channel_id = "C03RGRYSJUF" # notify-backups
  publishing_aws_services = {
    "backup" = "backup.amazonaws.com"
  }
}

module "chatbot_budgets" {
  source           = "../../modules/aws-chatbot"
  name             = "budget-alerts"
  slack_channel_id = "C03EHL9D25U" # budget-alerts
  publishing_aws_services = {
    "budgets" = "budgets.amazonaws.com"
  }
}

module "chatbot_guardduty" {
  source           = "../../modules/aws-chatbot"
  name             = "guardduty-alerts"
  slack_channel_id = "C05B1SN5A9Z" # sre-alerts
  publishing_aws_services = {
    "events" = "events.amazonaws.com"
  }
}
