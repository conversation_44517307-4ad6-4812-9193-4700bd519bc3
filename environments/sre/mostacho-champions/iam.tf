resource "aws_iam_policy" "dynamodb_champions" {
  name        = "mostacho-champions-dynamodb"
  description = "Full access to the DynamoDB tables for Mostacho Champions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem",
          "dynamodb:DescribeTable",
          "dynamodb:UpdateTable"
        ]
        Resource = [
          "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/mostacho-protector-champions",
          "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/mostacho-protector-vanta-vulnerabilities"
        ]
      }
    ]
  })
}

resource "aws_iam_policy" "sqs_queue" {
  name        = "mostacho-champions-sqs-queue"
  description = "Allows mostacho-champions to push messages to its SQS queue"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "sqs:sendMessage"
        Resource = "arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:sre-mostacho-champions-issues-to-reassign.fifo"
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
