locals {
  app_name = "mostacho-champions" # <<< CHANGE ME

  # important variables:
  # ecs_variables on api.tf
  # database_users on postgres.tf
  # route_auth_map on gateway.tf
  # modules on queues.tf

  # stop editing here

  lambda_name = local.app_name

  lambda_parameter_prefix = "/${var.environment.private_network.vpc_name}/${local.app_name}-api"

  gateway_name = "${local.app_name}-${var.environment.private_network.vpc_name}"
  gateway_url  = join(".", compact(["${local.app_name}-gw", var.environment.cluster.dns_prefix, "whykeyway.com"]))
  service_url  = join(".", compact(["${local.lambda_name}", var.environment.cluster.dns_prefix, "whykeyway.com"]))

  default_tags = merge(aws_servicecatalogappregistry_application.application.application_tag, {
    "service" = local.lambda_name
    "env"     = var.environment.private_network.vpc_name
    "cluster" = var.environment.cluster.name
    "Datadog" = "true"
  })
}
