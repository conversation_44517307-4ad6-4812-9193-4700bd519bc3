module "lambda" {
  source = "../../../modules/lambda-docker-v2/"

  name            = local.lambda_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  public_endpoint = false

  memory_size = 512
  timeout     = 30 # 1-900 seconds

  extra_iam_policy_arns = [
    aws_iam_policy.dynamodb_champions.arn,
    aws_iam_policy.sqs_queue.arn
  ]

  environment_variables = {
    VULNS_TABLE     = "mostacho-protector-vanta-vulnerabilities"
    DYNAMODB_TABLE  = "mostacho-protector-champions"
    GITHUB_ORG      = "unlockre"
  }

  # these are not updated on runtime, terraform needs to be applied after changes
  environment_secrets = {
    GITHUB_TOKEN = "/sre/mostacho-protector/github_token"
  }

  tags = local.default_tags
}
