resource "aws_iam_policy" "ses_email" {
  name        = "send_email_whykeyway_com"
  description = "Allows Principal to send emails using whykeywy.com identity"

  policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Effect = "Allow"
          Action = "ses:SendRawEmail"
          Resource = "arn:aws:ses:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:identity/whykeyway.com"
          Resource = "arn:aws:ses:us-east-1:************:identity/whykeyway.com"
        }
      ]
    })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
