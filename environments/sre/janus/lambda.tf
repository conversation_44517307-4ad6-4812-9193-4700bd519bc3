module "lambda" {
  source = "../../../modules/lambda-docker-v2/"

  name            = local.lambda_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  public_endpoint = false

  memory_size = 512
  timeout     = 30 # 1-900 seconds

  # extra_iam_policy_arns = []

  environment_variables = {
    AUTHORIZED_USERS = join(",", [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "emilian<PERSON>@whykeyway.com",
      "<EMAIL>"
    ])
    OPENVPN_API_URL  = "http://a.vpn.whykeyway.com:8080/api/openvpn"
    GOOGLE_CLIENT_ID = "949019209344-8ercbdecfkkj9c17qa802gl2uf8clkb7.apps.googleusercontent.com"
    GITHUB_ORG       = "unlockre"
    SES_SOURCE_EMAIL = "<EMAIL>"
    SLACK_CHANNEL_ID = "C047QQNPTSS"

    GOOGLE_DIRECTORY_ADMIN = "<EMAIL>"
  }

  # these are not updated on runtime, terraform needs to be applied after changes
  environment_secrets = {
    CREDENTIALS_JSON = "/sre/janus/credentials_json",
    GITHUB_TOKEN     = "/sre/janus/github_token",
    SLACK_TOKEN      = "/sre/janus/slack_oauth_token",
  }

  extra_iam_policy_arns = [
    aws_iam_policy.ses_email.arn
  ]

  tags = local.default_tags
}
