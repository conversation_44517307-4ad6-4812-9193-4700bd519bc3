module "sre_vpc" {
  source = "../../modules/networking"
  vpc = {
    name = "sre",
    cidr = "10.4.0.0/16"
  }
  nat_gateways = [
    { zone = "us-east-1a" },
    { zone = "us-east-1b" },
  ]
  public_subnets = [
    { zone = "us-east-1a", cidr = "10.4.0.0/22" },
    { zone = "us-east-1b", cidr = "10.4.4.0/22" }
  ]
  private_subnets = [
    { zone = "us-east-1a", cidr = "10.4.100.0/22" },
    { zone = "us-east-1b", cidr = "10.4.104.0/22" }
  ]
  sg_extra_cidrs = [
    "10.0.0.0/8", # internal network
  ]
}

module "sre_transit_gateway" {
  source = "../../modules/transit-gateway"

  name            = "sre-transit-gateway"
  amazon_side_asn = 65001
}

module "sre_tg_attachments" {
  source = "../../modules/transit-gateway-attachments"

  transit_gateway_id = module.sre_transit_gateway.tg.id # tgw-07dbeb395284ee143
  destination_cidr   = "10.0.0.0/8"
  vpc                = module.sre_vpc.private_network.vpc
  subnets            = module.sre_vpc.private_network.subnets # must have one (and only one) subnet per AZ
  route_tables       = concat(module.sre_vpc.public_network.route_tables, module.sre_vpc.private_network.route_tables)
  tags               = { Name = "sre-private-tgw" }
}
