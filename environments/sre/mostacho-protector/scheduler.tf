module "github_protection_cron" {
  event_name = "sre-mostacho-github-protection"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(5 21 * * ? *)"

  url    = "https://${local.service_url}/run/github-protection"
  method = "POST"
}

module "github_cleanup_terraform_cron" {
  event_name = "sre-mostacho-github-cleanup-terraform"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 3 * * ? *)"

  url    = "https://${local.service_url}/run/github-cleanup-terraform-apply"
  method = "POST"
}

module "github_backups_cron" {
  event_name = "sre-mostacho-github-backups"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(5 21 ? * 5 *)"

  url    = "https://${local.service_url}/run/github-backups"
  method = "POST"
}

module "auth0_scope_cron" {
  event_name = "sre-mostacho-auth0-scope"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(1 */2 ? * * *)"

  url    = "https://${local.service_url}/run/vanta-auth0-scope"
  method = "POST"
}

module "aws_old_keys_cron" {
  event_name = "sre-mostacho-aws-old-keys"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(5 14 ? * 1 *)"

  url    = "https://${local.service_url}/run/aws-disable-old-keys"
  method = "POST"
}

module "aws_dynamodb_cron" {
  event_name = "sre-mostacho-aws-dynamodb-protect"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(14 3 ? * * *)"

  url    = "https://${local.service_url}/run/aws-protect-dynamodb"
  method = "POST"
}

module "vanta_scraper_cron" {
  event_name = "sre-mostacho-vanta-scraper"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 2,15 * * ? *)"

  url    = "https://${local.service_url}/run/vanta-scraper"
  method = "POST"
}

module "issues_solver_cron" {
  event_name = "sre-mostacho-issues-solver"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 9,11,13,15,17 ? * 2-6 *)"

  url    = "https://${local.service_url}/run/issues-solver"
  method = "POST"
}

module "vuln_notifier_cron" {
  event_name = "sre-mostacho-vuln-notifier"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 12 ? * 2 *)"
  url                 = "https://${local.service_url}/run/vuln-notifier"
  method              = "POST"
}

module "big_query_backups_cron" {
  event_name = "sre-mostacho-big-query-backup"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 1 * * ? *)"
  url                 = "https://${local.service_url}/run/big-query-backups"
  method              = "POST"
}

module "weekly_report_cron" {
  event_name = "sre-mostacho-weekly-report"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(5 12 ? * 2 *)"
  url                 = "https://${local.service_url}/run/weekly-report"
  method              = "POST"
}

module "github_sync_issues" {
  event_name = "sre-mostacho-github-sync-issues"
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "cron(0 12 * * ? *)"
  url                 = "https://${local.service_url}/run/github-sync-issues"
  method              = "POST"
}
