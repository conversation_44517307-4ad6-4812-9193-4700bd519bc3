resource "aws_iam_policy" "scraping" {
  name        = "mostacho-protector-scraping"
  description = "Permissions required by Mostacho Protector"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters"
        ],
        Resource = [
          "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "ecr:ListTagsForResource"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "inspector2:ListFindings"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy" "dynamodb" {
  name        = "mostacho-protector-dynamodb"
  description = "Full access to the DynamoDB tables for Mostacho Protector"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "dynamodb:*"
        Resource = "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/*"
      },
      {
        Effect   = "Allow"
        Action   = "dynamodb:DeleteTable"
        Resource = "arn:aws:dynamodb:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:table/*"
      }
    ]
  })
}

resource "aws_iam_policy" "iam" {
  name        = "mostacho-protector-iam"
  description = "IAM access for Mostacho Protector"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "iam:UpdateAccessKey",
          "iam:ListUsers",
          "iam:ListUserTags",
          "iam:ListAccessKeys"
        ]
        Resource = "*"
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
