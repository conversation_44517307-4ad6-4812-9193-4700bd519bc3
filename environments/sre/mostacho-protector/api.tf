module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 256
    memory = 512
  }
  port = 8080

  private_network = var.environment.private_network
  public_endpoint = false


  github_repository  = "unlockre/mostacho-protector"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 19 ? * TUE *)" # Every Tuesday at 19:00 UTC

  codebuild_migration_stage = false

  chatbot_arn = var.environment.chatbot_arn
  iam_extra_policies = [
    aws_iam_policy.scraping.arn,
    aws_iam_policy.dynamodb.arn,
    aws_iam_policy.iam.arn,
    "arn:aws:iam::aws:policy/AmazonS3FullAccess"
  ]

  ecs_variables = {
    GITHUB_ORGANIZATION = "unlockre"
  }
  ecs_secrets = {
    GITHUB_TOKEN        = "/sre/mostacho-protector/github_token",
    SLACK_TOKEN         = "/sre/mostacho-protector/slack_token",
    VANTA_CLIENT_ID     = "/sre/mostacho-protector/vanta_id",
    VANTA_CLIENT_SECRET = "/sre/mostacho-protector/vanta_secret",
    GOOGLE_CREDS        = "/sre/mostacho-protector/google_json"
  }

  tags = local.default_tags
}
