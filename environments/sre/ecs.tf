module "sre_cluster" {
  source       = "../../modules/ecs-fargate-cluster"
  cluster_name = "sre"

  fargate_spot_mode = "hybrid"

  auth0_settings = {
    OPENID_CONFIGURATION = "https://auth.whykeyway.com/.well-known/openid-configuration",
    AUDIENCE             = "https://keyway-api.whykeyway.com",
    TOKEN_ISSUER         = "https://auth.whykeyway.com/"
  }

  private_network = module.sre_vpc.private_network
  public_network  = module.sre_vpc.public_network
}
