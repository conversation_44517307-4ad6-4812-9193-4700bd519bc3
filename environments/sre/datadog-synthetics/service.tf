resource "aws_ecs_service" "service" {
  depends_on = [
    aws_ecs_task_definition.task
  ]

  name            = "${local.app_name}-service"
  cluster         = var.environment.cluster.arn
  task_definition = aws_ecs_task_definition.task.arn
  desired_count   = local.replica_count
  propagate_tags  = "SERVICE"
  tags            = local.default_tags

  enable_execute_command = true

  deployment_controller {
    type = "ECS"
  }

  network_configuration {
    subnets          = var.environment.private_network.subnets
    security_groups  = var.environment.private_network.security_groups
    assign_public_ip = false
  }

  lifecycle {
    ignore_changes = [capacity_provider_strategy, deployment_circuit_breaker]
  }
}
