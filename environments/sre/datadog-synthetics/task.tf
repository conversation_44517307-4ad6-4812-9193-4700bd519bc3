resource "aws_ecs_task_definition" "task" {
  depends_on = [aws_iam_role.ecs_task_execution_role, aws_iam_role.ecs_container_role]
  tags       = local.default_tags

  family       = "${local.app_name}-task-${var.environment.cluster.name}"
  network_mode = "awsvpc"

  cpu    = local.cpu
  memory = local.memory

  task_role_arn            = aws_iam_role.ecs_container_role.arn
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn
  requires_compatibilities = ["FARGATE"]

  container_definitions = jsonencode(
    [
      {
        name  = "datadog-synthetics",
        image = "public.ecr.aws/datadog/synthetics-private-location-worker:latest",
        command = [
          "--site='datadoghq.com'",
          "--locationID='pl:sre-cluster-35e8f5be2580197b01032c535240594e'",
          "--publicKey.fingerprint='sha256$base64$GA8WCOyWvWP6GJAGhywibAf0NuEeFgqJKZVjrCogbqU='"
        ],
        environment = [
        ],
        mountPoints = [],
        volumesFrom = [],
        cpu         = local.cpu,
        memory      = local.memory,
        essential   = true,
        secrets = [
          { name = "DATADOG_API_KEY", valueFrom = "arn:aws:ssm:us-east-1:681574592108:parameter/sre/datadog-synthetics/datadog_api_key" },
          { name = "DATADOG_ACCESS_KEY", valueFrom = "arn:aws:ssm:us-east-1:681574592108:parameter/sre/datadog-synthetics/datadog_access_key" },
          { name = "DATADOG_SECRET_ACCESS_KEY", valueFrom = "arn:aws:ssm:us-east-1:681574592108:parameter/sre/datadog-synthetics/datadog_secret_access_key" },
          { name = "DATADOG_PUBLIC_KEY_PEM", valueFrom = "arn:aws:ssm:us-east-1:681574592108:parameter/sre/datadog-synthetics/datadog_public_key_pem" },
          { name = "DATADOG_PRIVATE_KEY", valueFrom = "arn:aws:ssm:us-east-1:681574592108:parameter/sre/datadog-synthetics/datadog_private_key" },
        ],
        healthCheck = {
          "retries" : 3,
          "command" : [
            "CMD-SHELL", "/bin/sh -c '[ $(expr $(cat /tmp/liveness.date) + 300000) -gt $(date +%s%3N) ]'"
          ],
          "timeout" : 2,
          "interval" : 10,
          "startPeriod" : 30
        }
      }
    ]
  )

  skip_destroy = true
}
