# This is Step 2, start on the GCP portion
module "aws_gcp_vpn" {
  depends_on = [
    module.sre_transit_gateway.tg
    # GCP Cloud VPN must be up!
  ]

  source = "../../modules/vpn-with-gcp"
  gateway = {
    # Note: The IPs and ASN here come from the Step 1 (Cloud VPN Gateways on GCP)
    external_ips = ["************", "*************"]
    asn          = 64514

    name = "sre-prod-gcp"
    type = "ipsec.1"
  }

  transit_gateway_id = module.sre_transit_gateway.tg.id

  tunnels = [
    # The inside CIDR should be /30 and not overlap with other clients, check the Keyway Network Spreadsheet
    # Some IPs are reserved, the list is here: https://docs.aws.amazon.com/vpn/latest/s2svpn/VPNTunnels.html
    # There MUST be 4 tunnels set (2 connections, 2 tunnels each)
    { inside_cidr = "***********/30", preshared_key = "keyway_aws_gcp_***********" },  # conn0 - tunn1
    { inside_cidr = "***********/30", preshared_key = "keyway_aws_gcp_***********" },  # conn0 - tunn2
    { inside_cidr = "***********/30", preshared_key = "keyway_aws_gcp_***********" },  # conn1 - tunn1
    { inside_cidr = "************/30", preshared_key = "keyway_aws_gcp_************" } # conn1 - tunn2
  ]

  # Once you reach this part, apply and proceed to Step 3 on the GCP folder
}
