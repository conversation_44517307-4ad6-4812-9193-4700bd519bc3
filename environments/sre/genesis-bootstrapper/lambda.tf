module "lambda" {
  source = "../../../modules/lambda-docker-v2/"

  name            = local.lambda_name
  cluster         = var.environment.cluster
  private_network = var.environment.private_network
  public_endpoint = false

  memory_size = 512
  timeout     = 120 # 1-900 seconds

  environment_variables = {
  }

  # these are not updated on runtime, terraform needs to be applied after changes
  environment_secrets = {
    GITHUB_TOKEN = "/sre/genesis-bootstrapper/github_token"
  }

  tags = local.default_tags
}
