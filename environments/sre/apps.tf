locals {
  environment = {
    cluster         = module.sre_cluster.cluster
    public_network  = module.sre_vpc.public_network
    private_network = module.sre_vpc.private_network
    chatbot_arn     = module.chatbot_deployments_sre.chatbot_arn
  }
}

module "lambda_docker_hello_world" {
  source      = "./lambda-docker-hello-world/"
  environment = local.environment
}

module "datadog_synthetics" {
  source      = "./datadog-synthetics/"
  environment = local.environment
}

module "ecs_on_off_bot" {
  source      = "./ecs-on-off-bot/"
  environment = local.environment
}

module "mostacho_champions" {
  source      = "./mostacho-champions/"
  environment = local.environment
}

module "mostacho-protector" {
  source      = "./mostacho-protector/"
  environment = local.environment
}

module "janus" {
  source      = "./janus/"
  environment = local.environment
}

module "metabase_proxy" {
  source      = "./metabase-proxy/"
  environment = local.environment
}

module "openvpn" {
  source      = "./openvpn/"
  environment = local.environment
}

module "spot_reclaim_alerts" {
  source = "./spot-reclaim-alerts/"
  # environment = local.environment
}

module "reserva_cocheras_dot" {
  source      = "./reserva-cocheras-dot/"
  environment = local.environment
}

module "identity_center_users" {
  source = "./identity-center-users/"
}

module "aws_start_redirect" {
  source      = "./aws-start-redirect/"
  environment = local.environment
}

module "mostacho_reassigner" {
  source      = "./mostacho-reassigner/"
  environment = local.environment
}

module "genesis_bootstrapper" {
  source      = "./genesis-bootstrapper/"
  environment = local.environment
}
