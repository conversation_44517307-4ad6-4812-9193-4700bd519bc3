module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 256
    memory = 512
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  codebuild_image = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  chatbot_arn     = var.environment.chatbot_arn

  github_repository  = "unlockre/reserva-cocheras-dot"
  github_branch_name = "develop"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    API_URL     = local.service_url
    GATEWAY_URL = local.gateway_url

    PORT = "8080"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"

    SLACK_TOKEN          = "/sre/reserva-cocheras-dot/slack-token"
    SLACK_SIGNING_SECRET = "/sre/reserva-cocheras-dot/slack-signing-secret"
    SLACK_APP_TOKEN      = "/sre/reserva-cocheras-dot/slack-app-token"

    POSTGRES_DB_URL      = "/sre/reserva-cocheras-dot-db/db-connection-string",
    POSTGRES_DB_USER     = "/sre/reserva-cocheras-dot-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/sre/reserva-cocheras-dot-db/db-api-password",
  }

  tags = local.default_tags
}
