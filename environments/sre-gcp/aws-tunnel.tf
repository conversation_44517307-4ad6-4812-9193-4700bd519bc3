# More info: https://cloud.google.com/architecture/build-ha-vpn-connections-google-cloud-aws
module "gcp_aws_vpn" {
  source = "../../modules-gcp/vpn-with-aws"

  # Step 1: Fill these variables first
  router = {
    name         = "gcp-aws-vpn"
    network_name = module.sre_vpc.network.name
    region       = "us-central1"

    # The ASN can be any private ASN in the range 64512-65534 or 4200000000-4294967294
    # that you aren't already using as a peer ASN in the same region and network.
    asn = 64514

    # Step 2: Apply and then copy the gateway IPs from the GCP Console
    # https://console.cloud.google.com/hybrid/vpn/list?project=keyway-data&tab=gateways
    # The Step 3 array must be empty
    # Go to the AWS folder and generate the tunnels there before advancing

    aws_gateway_ips = [
      # Step 3: Download the configuration for both VPN connections
      # gw_ip:            Outside IP > Virtual Private Gateway
      # aws_inside_ip:    Inside IP > Virtual Private Gateway (without /30)
      # gcp_inside_range: Inside IP > Customer Gateway
      # aws_asn:          ASN used for the Transit Gateway (must not collide)
      { gw_ip = "*************", ike_version = 2, shared_secret = "keyway_aws_gcp_169.254.6.0", aws_inside_ip = "***********", gcp_inside_range = "***********/30", aws_asn = 65001 },   # conn0 tunn1
      { gw_ip = "*************", ike_version = 2, shared_secret = "keyway_aws_gcp_169.254.6.4", aws_inside_ip = "***********", gcp_inside_range = "***********/30", aws_asn = 65001 },   # conn0 tunn2
      { gw_ip = "**************", ike_version = 2, shared_secret = "keyway_aws_gcp_169.254.6.8", aws_inside_ip = "***********", gcp_inside_range = "************/30", aws_asn = 65001 }, # conn1 tunn1
      { gw_ip = "***********", ike_version = 2, shared_secret = "keyway_aws_gcp_***********2", aws_inside_ip = "************", gcp_inside_range = "************/30", aws_asn = 65001 },  # conn1 tunn2
    ]
  }

  advertised_ip_ranges = [
    "********/16", # composer-vpc
    "********/16", # gke (composer-vpc)
    "********/16", # gke (composer-dev-vpc)
    "********/16", # private service-network (composer-vpc)
  ]
}
