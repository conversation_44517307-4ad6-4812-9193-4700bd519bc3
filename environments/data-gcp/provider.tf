terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~>5.2"
    }
    datadog = {
      source = "DataDog/datadog"
    }
  }

  required_version = ">=1.1.5"

  backend "s3" {
    bucket = "keyway-terraform-states"
    key    = "data-gcp/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "google" {
  project = "keyway-data"
  region  = "us-central1"
  zone    = "us-central1-c"
}

provider "datadog" {
  # api_key: env.DD_API_KEY
  # app_key: env.DD_APP_KEY
}
