module "composer_data" {
  source = "../../modules-gcp/composer-gke"

  image_version = "composer-2.8.7-airflow-2.9.1"

  composer = {
    name   = "composer-data"
    region = "us-central1"
  }

  workers = {
    cpu              = 8
    memory           = 16
    storage          = 10
    max_workers      = 8
    environment_size = "ENVIRONMENT_SIZE_SMALL"
  }

  network = {
    network    = module.data_vpc.network.name
    subnetwork = module.data_vpc.network.subnets[0]
    cidrs = {
      cluster_cidr_block        = "10.7.0.0/17"   # /17 or bigger
      services_cidr_block       = "10.7.128.0/22" # /22 or bigger
      master_cidr_block         = "10.7.132.0/23" # /23 or smaller
      cloud_sql_cidr_block      = "10.7.136.0/22" # /24 or bigger
      cloud_composer_cidr_block = "10.7.144.0/24" # /24 or smaller
    }
  }
}
