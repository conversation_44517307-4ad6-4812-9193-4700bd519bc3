module "vm" {
  source = "../../../modules-gcp/compute-engine-vm-v2"

  app_name     = "keyget"
  source_image = "cos-stable-113-18244-85-39"  # TODO: Update source image; this has been deprecated in favor of "cos-stable-117-18613-164-121" at the time of writing (2024-04-27)
  machine_type = "c3d-highcpu-8"

  network_id  = var.environment.network.name
  subnetwork  = var.environment.network.subnets[0]
  health_check = {
    type                = "HTTP"
    port                = "8080"
    request_path        = "/health"
    timeout_sec         = 30
    check_interval_sec  = 30
    healthy_threshold   = 1
    unhealthy_threshold = 5 
  }

  locations = {
    instance_group    = "us-central1-c"
    backend_service   = "global"
    instance_template = "us-central1"
    health_check      = "global"
    forwarding_rule   = "global"
    ip_address       = "us-central1"
  }

  load_balancer     = "BOTH"

  force_names = {
    instance_group_manager = "keyget-prod-instance-group"
    instance_template     = "keyget-prod-instance-v4-dd-updated"
    health_check          = "healthcheck"
    backend_service = {
      tcp = "keyget-be"
      udp = null
    }
    forwarding_rule = {
      tcp = "keyget-endpoint"
      udp = null
    }
    ip_addresses = {
      tcp = "keyget-global-ip"
      udp = null
    }
  }
}
