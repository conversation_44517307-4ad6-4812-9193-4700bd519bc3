resource "google_compute_forwarding_rule" "ids" {
  name = "${local.app_name}-ilb"

  is_mirroring_collector = true
  ip_protocol            = "TCP"
  load_balancing_scheme  = "INTERNAL"
  backend_service        = module.vm.tcp_backend_service_id
  all_ports              = true
  network                = var.environment.network.name
  subnetwork             = var.environment.network.subnets[0]
  network_tier           = "PREMIUM"
}

resource "google_compute_packet_mirroring" "packet_mirroring" {
  name        = "suricata-packet-mirroring"
  description = "mirrors composer-vpc for suricata (IDS)"

  network {
    url = var.environment.network.name
  }

  mirrored_resources {
    dynamic "subnetworks" {
      for_each = var.environment.network.subnets
      content {
        url = subnetworks.value
      }
    }
  }

  filter {
    ip_protocols = ["tcp", "udp"]
    cidr_ranges  = ["0.0.0.0/0"]
    direction    = "BOTH"
  }

  collector_ilb {
    url = google_compute_forwarding_rule.ids.id
  }
}
