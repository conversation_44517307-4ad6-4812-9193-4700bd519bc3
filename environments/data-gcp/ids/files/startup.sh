#!/bin/bash

# Install cloud logging agent
curl -sSO https://dl.google.com/cloudagents/add-google-cloud-ops-agent-repo.sh
sudo bash add-google-cloud-ops-agent-repo.sh --also-install
cat <<"EOF" > /etc/google-cloud-ops-agent/config.yaml
${gc_ops_config}
EOF
systemctl restart google-cloud-ops-agent

# Install suricata
add-apt-repository ppa:oisf/suricata-stable -y
apt update -y && apt -y install suricata apache2

# Suricata Conf
mv /etc/suricata/suricata.yaml /etc/suricata/suricata.yaml.bak
cat <<"EOF" > /etc/suricata/suricata.yaml
${suricata_config}
EOF
cat <<"EOF" > /etc/suricata/disable.conf
${suricata_disable}
EOF
systemctl restart suricata

mkdir -p /var/lib/suricata/{rules,update}
suricata-update -v
systemctl restart suricata

# Needs a simple HTTP server for health checks
echo "Suricata IDS - Packet Mirror" > /var/www/html/index.html
systemctl restart apache2

# Log rotate
touch /etc/logrotate.d/suricata
cat <<"EOF" > /etc/logrotate.d/suricata
${suricata_logrotate}
EOF
