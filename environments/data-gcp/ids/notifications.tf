resource "google_logging_metric" "ids_threat_detection" {
  name   = "ids_threat_detection_metric"
  filter = "resource.type=\"gce_instance\" AND log_name=\"projects/keyway-data/logs/suricata_logs\""
  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
  }
}

resource "google_monitoring_alert_policy" "ids_threat_alert" {
  display_name = "IDS Threat Detection Alert"
  combiner     = "OR"
  conditions {
    display_name = "IDS Threat Condition"
    condition_threshold {
      filter          = "metric.type=\"logging.googleapis.com/user/ids_threat_detection_metric\" AND resource.type=\"gce_instance\""
      comparison      = "COMPARISON_GT"
      threshold_value = 0
      duration        = "0s"
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_NONE"
      }
    }
  }
  # slack: sre-warnings
  notification_channels = ["projects/keyway-data/notificationChannels/16111147561892531541"]
}
