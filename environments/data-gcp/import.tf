### KEYGET
import {
  id = "projects/keyway-data/regions/us-central1/instanceTemplates/keyget-prod-instance-v4-dd-updated"
  to = module.keyget.module.vm.google_compute_region_instance_template.template[0]
}
import {
  id = "projects/keyway-data/global/healthChecks/healthcheck"
  to = module.keyget.module.vm.google_compute_health_check.health_check[0]
}
import {
  id = "projects/keyway-data/global/forwardingRules/keyget-endpoint"
  to = module.keyget.module.vm.google_compute_global_forwarding_rule.tcp_internal[0]
}
import {
  id = "projects/keyway-data/regions/us-central1/addresses/keyget-global-ip"
  to = module.keyget.module.vm.google_compute_address.tcp_ip[0]
}
import {
  id = "projects/keyway-data/zones/us-central1-c/instanceGroupManagers/keyget-prod-instance-group"
  to = module.keyget.module.vm.google_compute_instance_group_manager.igm[0]
}
import {
  id = "projects/keyway-data/global/backendServices/keyget-be"
  to = module.keyget.module.vm.google_compute_backend_service.tcp_backend[0]
}
