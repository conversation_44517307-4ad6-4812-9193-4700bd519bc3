module "playground_instance" {
  source = "../../modules-gcp/compute-engine-vm"

  instance_name     = "data-playground"
  availability_zone = "us-central1-c"
  subnetwork        = module.data_vpc.network.subnets[0]

  instance_type = "e2-highmem-16" # 16 vCPU + 128G
  # instance_type = "e2-highmem-8" # 8 vCPU + 64G
  #               "e2-medium"   # 1-2 vCPU (shared) + 4G
  volume_size = 50            # GB
  volume_type = "pd-balanced" # cheap ssds

  image_version = "debian-11-bullseye-v20231010"
}
