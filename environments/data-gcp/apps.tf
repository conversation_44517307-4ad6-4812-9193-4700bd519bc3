locals {
  environment = {
    project = "keyway-data"
    network = module.data_vpc.network
    vpc_connectors = [
      module.data_vpc_conn_0.vpc-conn,
      module.data_vpc_conn_1.vpc-conn
    ]
  }
}

module "ids" {
  source      = "./ids"
  environment = local.environment
}

module "datadog_agent" {
  source      = "./datadog-agent"
  environment = local.environment
}

module "keyget" {
  source      = "./keyget"
  environment = local.environment
}
