#!/bin/bash

# Install docker
cd ~
apt-get remove -y docker docker-engine docker.io containerd runc
apt update
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo \
"deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
$(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt update
apt install -y docker-ce docker-ce-cli containerd.io
usermod -aG docker ubuntu
systemctl enable docker
systemctl start docker

# Create .datadog_env file
cat <<"EOF" > ~/.datadog_env
DD_API_KEY=${datadog_api_key}
DD_APM_ENABLED=true
DD_USE_DOGSTATSD=true
DD_DOGSTATSD_NON_LOCAL_TRAFFIC=true
DD_AC_EXCLUDE=name:.*
DD_TRACE_ENABLED=true
DD_APM_NON_LOCAL_TRAFFIC=true
DD_CONTAINER_EXCLUDE=name:.*
EOF

# Deploy Datadog agent
docker run -d --cgroupns host --restart unless-stopped --name dd-agent --env-file ~/.datadog_env \
            -v /var/run/docker.sock:/var/run/docker.sock:ro -v /proc/:/host/proc/:ro -v /sys/fs/cgroup/:/host/sys/fs/cgroup:ro \
            -p 0.0.0.0:8125:8125/udp -p 0.0.0.0:8126:8126/tcp \
            gcr.io/datadoghq/agent:7
