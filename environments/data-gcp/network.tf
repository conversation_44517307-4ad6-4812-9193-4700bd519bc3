module "data_vpc" {
  source = "../../modules-gcp/networking"

  vpc = {
    name    = "composer"
    region  = "us-central1"
    cidr    = "********/16"
    subnets = 2
  }

  private_ip_google_access = true
}

module "data_vpc_conn_0" {
  source = "../../modules-gcp/vpc-connector"
  connector = {
    name    = "data-vpc-conn-0"
    cidr    = "**********/28"
    network = module.data_vpc.network.name
  }
}

module "data_vpc_conn_1" {
  source = "../../modules-gcp/vpc-connector"
  connector = {
    name    = "data-vpc-conn-1"
    cidr    = "***********/28"
    network = module.data_vpc.network.name
  }
}

module "data_vpc_nat" {
  source  = "../../modules-gcp/cloud-nat"
  name    = "data-vpc-nat"
  network = module.data_vpc.network.name
}

module "data_vpc_conn_dev_0" {
  source = "../../modules-gcp/vpc-connector"
  connector = {
    name    = "data-vpc-conn-dev-0"
    cidr    = "**********/28"
    network = module.data_vpc.network.name
  }
}

module "data_vpc_conn_dev_1" {
  source = "../../modules-gcp/vpc-connector"
  connector = {
    name    = "data-vpc-conn-dev-1"
    cidr    = "***********/28"
    network = module.data_vpc.network.name
  }
}

resource "google_compute_global_address" "service_range" {
  name          = "service-networking"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 16
  network       = module.data_vpc.network.name
}

resource "google_service_networking_connection" "private_service_connection" {
  network                 = module.data_vpc.network.name
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.service_range.name]
}
