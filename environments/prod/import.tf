### DynamoDB tables
import {
  id = "demographics-prod"
  to = module.demographics.module.demographics_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "demographics-test"
  to = module.demographics.module.test_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "first-pass-analyses_prod"
  to = module.property_analyzer.module.first_pass_analyses_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "property-analyzer_files_prod"
  to = module.property_analyzer.module.files_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "key-assist_api_sessions_prod"
  to = module.key_assist.module.api_sessions_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "key-assist_sessions_prod"
  to = module.key_assist.module.sessions_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "keygen-numbers_generator"
  to = module.keygen.module.numbers_generator_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "keygen-property_identifications"
  to = module.keygen.module.property_identifications_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "keygen-queries_cache"
  to = module.keygen.module.queries_cache_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "kfile-staging_files"
  to = module.kfile.module.staging_files_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "kfile-staging_files-prod"
  to = module.kfile.module.staging_files_prod_dynamo.aws_dynamodb_table.basic-dynamodb-table
}
import {
  id = "user-activity_prod"
  to = module.user_activity.module.user_activity_dynamo.aws_dynamodb_table.basic-dynamodb-table
}

### DynamoDB autoscaling policies
import {
  id = "dynamodb/table/demographics-test/dynamodb:table:ReadCapacityUnits"
  to = module.demographics.module.test_dynamo.aws_appautoscaling_target.dynamodb_table_read_target[0]
}
import {
  id = "dynamodb/table/demographics-test/dynamodb:table:ReadCapacityUnits/$demographics-test-scaling-policy"
  to = module.demographics.module.test_dynamo.aws_appautoscaling_policy.dynamodb_table_read_policy[0]
}
import {
  id = "dynamodb/table/demographics-test/dynamodb:table:WriteCapacityUnits"
  to = module.demographics.module.test_dynamo.aws_appautoscaling_target.dynamodb_table_write_target[0]
}
import {
  id = "dynamodb/table/demographics-test/dynamodb:table:WriteCapacityUnits/$demographics-test-scaling-policy"
  to = module.demographics.module.test_dynamo.aws_appautoscaling_policy.dynamodb_table_write_policy[0]
}

import {
  id = "dynamodb/table/kfile-staging_files/dynamodb:table:ReadCapacityUnits"
  to = module.kfile.module.staging_files_dynamo.aws_appautoscaling_target.dynamodb_table_read_target[0]
}
import {
  id = "dynamodb/table/kfile-staging_files/dynamodb:table:ReadCapacityUnits/$kfile-staging_files-scaling-policy"
  to = module.kfile.module.staging_files_dynamo.aws_appautoscaling_policy.dynamodb_table_read_policy[0]
}
import {
  id = "dynamodb/table/kfile-staging_files/dynamodb:table:WriteCapacityUnits"
  to = module.kfile.module.staging_files_dynamo.aws_appautoscaling_target.dynamodb_table_write_target[0]
}
import {
  id = "dynamodb/table/kfile-staging_files/dynamodb:table:WriteCapacityUnits/$kfile-staging_files-scaling-policy"
  to = module.kfile.module.staging_files_dynamo.aws_appautoscaling_policy.dynamodb_table_write_policy[0]
}
