module "staging_files_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-staging_files"
  capacity_mode       = "PROVISIONED"
  deletion_protection = false
  hash_key            = "id"

  attributes = {
    id = "S"
  }

  write_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }
  read_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }

  tags = merge(local.default_tags, {})
}

module "staging_files_prod_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "${local.app_name}-staging_files-prod"
  capacity_mode = "on_demand"
  hash_key      = "k"
  range_key     = "sk"

  attributes = {
    k    = "S"
    sk   = "S"
    path = "S"
  }
  ttl_attribute = "expirationDate"

  global_indexes = [{
    name            = "path-index"
    hash_key        = "path"
    read_capacity   = 0
    write_capacity  = 0
    projection_type = "ALL"
  }]

  tags = merge(local.default_tags, {})
}
