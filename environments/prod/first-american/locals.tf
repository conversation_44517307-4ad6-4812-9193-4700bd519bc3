locals {
  app_name      = "first-american-lambda"
  rds_name      = "first-american-db"
  database_name = "first_american"

  ecs_parameter_prefix = "/prod/${local.app_name}"
  rds_parameter_prefix = "/prod/${local.rds_name}"

  default_tags = merge(aws_servicecatalogappregistry_application.application.application_tag, {
    "service" = local.app_name
    "env"     = var.environment.private_network.vpc_name
    "cluster" = var.environment.cluster.name
  })
}
