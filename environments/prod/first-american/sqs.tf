module "notification_queue" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-first_american-s3_notification"
  tags       = local.default_tags
  extra_statements = [
    {
      Action    = "sqs:*",
      Effect    = "Allow",
      Principal = { Service = "s3.amazonaws.com" },
      Resource  = "arn:aws:sqs:us-east-1:681574592108:prod-first_american-s3_notification",
      Condition = { ArnLike = { "aws:SourceArn" = "arn:aws:s3:::first-american-keyway" } },
      Sid       = "s3-to-sqs"
    }
  ]
}
