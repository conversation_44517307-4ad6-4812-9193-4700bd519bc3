locals {
  environment = {
    cluster         = module.prod_cluster.cluster
    public_network  = module.prod_vpc.public_network
    private_network = module.prod_vpc.private_network
    auth0_settings = {
      JWKS_URI     = "https://auth.whykeyway.com/.well-known/jwks.json",
      AUDIENCE     = "https://keyway-api.whykeyway.com",
      TOKEN_ISSUER = "https://auth.whykeyway.com/"
    }
    chatbot_arn = "arn:aws:chatbot::681574592108:chat-configuration/slack-channel/deployments-prod"
  }
}

module "keyreader" {
  source      = "./keyreader"
  environment = local.environment
}

module "keydocs_cdn" {
  source      = "./keydocs-cdn/"
  environment = local.environment
}

module "document_parser" {
  source      = "./document-parser/"
  environment = local.environment
}

module "asset_manager" {
  source      = "./asset-manager/"
  environment = local.environment
}

module "buy_box" {
  source      = "./buy-box/"
  environment = local.environment
}

module "comp_selector" {
  source      = "./comp-selector/"
  environment = local.environment
}

module "dealroom" {
  source      = "./dealroom/"
  environment = local.environment
}

module "demographics" {
  source      = "./demographics/"
  environment = local.environment
}

module "enricher" {
  source      = "./enricher/"
  environment = local.environment
}

module "first_american" {
  source      = "./first-american/"
  environment = local.environment
}

module "geographics" {
  source      = "./geographics/"
  environment = local.environment
}

module "key_assist" {
  source      = "./key-assist/"
  environment = local.environment
}

module "keygen" {
  source      = "./keygen/"
  environment = local.environment
}

module "kfile" {
  source      = "./kfile/"
  environment = local.environment
}

module "narnia" {
  source      = "./narnia/"
  environment = local.environment
}

module "organizations" {
  source      = "./organizations/"
  environment = local.environment
}

module "property_analyzer" {
  source      = "./property-analyzer/"
  environment = local.environment
}

module "property_assets" {
  source      = "./property-assets/"
  environment = local.environment
}


module "property_sage" {
  source      = "./property-sage/"
  environment = local.environment
}

module "property_sage_synchropy" {
  source      = "./property-sage-synchropy/"
  environment = local.environment
}

module "property_search" {
  source      = "./property-search/"
  environment = local.environment
}

module "rent_api" {
  source      = "./rent-api/"
  environment = local.environment
}

module "user_activity" {
  source      = "./user-activity/"
  environment = local.environment
}

module "data_gpt" {
  source      = "./data-gpt/"
  environment = local.environment
}

module "keyreview" {
  source      = "./keyreview/"
  environment = local.environment
}

module "keyreview_scraper" {
  source      = "./keyreview-scraper/"
  environment = local.environment
}

module "keyreview_manager" {
  source      = "./keyreview-manager/"
  environment = local.environment
}

module "rclone" {
  source      = "./rclone/"
  environment = local.environment
}

module "dms_manager" {
  source      = "./dms-manager/"
  environment = local.environment
}

module "whats_new" {
  source      = "./whats-new/"
  environment = local.environment
}

module "keydocs_ai" {
  source      = "./keydocs-ai/"
  environment = local.environment
}
