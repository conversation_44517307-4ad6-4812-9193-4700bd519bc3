module "demographics_dynamo" {
  source        = "../../../modules/dynamodb-table"
  capacity_mode = "PAY_PER_REQUEST"
  table_name    = "${local.app_name}-prod"
  hash_key      = "id"

  attributes = {
    id = "S"
  }

  tags = merge(local.default_tags, {
    ENV = "prod"
  })
}

module "test_dynamo" {
  source              = "../../../modules/dynamodb-table"
  capacity_mode       = "PROVISIONED"
  table_name          = "${local.app_name}-test"
  hash_key            = "id"
  deletion_protection = false

  attributes = {
    id = "S"
  }

  read_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }
  write_autoscaling = {
    enabled = true
    min     = 1
    max     = 10
  }

  tags = merge(local.default_tags, {})
}
