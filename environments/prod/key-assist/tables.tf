module "api_sessions_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "${local.app_name}_api_sessions_prod"
  capacity_mode = "pay_per_request"
  hash_key      = "PK"
  range_key     = "SK"

  attributes = {
    PK        = "S"
    SK        = "S"
    UPDATE_AT = "S"
  }
  local_indexes = [
    {
      name            = "UPDATE_AT-index"
      projection_type = "ALL"
      range_key       = "UPDATE_AT"
    }
  ]

  tags = merge(local.default_tags, {})
}

module "sessions_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "${local.app_name}_sessions_prod"
  capacity_mode = "provisioned"
  hash_key      = "PK"
  range_key     = "SK"

  attributes = {
    PK = "S"
    SK = "S"
  }

  tags = merge(local.default_tags, {})
}
