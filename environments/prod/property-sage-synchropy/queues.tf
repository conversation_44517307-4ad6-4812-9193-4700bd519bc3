module "sns-prod-synchropy-new_developments_stats" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "prod-synchropy-new_developments_stats"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:prod-demographics-new_developments_stats"
  ]
  tags = local.default_tags
}

module "sns-prod-synchropy-comp_record" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "prod-synchropy-comp_record"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:prod-comps-calculate_record_priority"
  ]
  tags = local.default_tags
}
