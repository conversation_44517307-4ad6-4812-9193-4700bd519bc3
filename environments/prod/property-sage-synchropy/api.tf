module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = false

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-sage-synchropy"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    PROPERTY_SAGE_DB_USER     = "/prod/property-sage-db/db-api-username",
    PROPERTY_SAGE_DB_PASSWORD = "/prod/property-sage-db/db-api-password",
    PROPERTY_SAGE_DB_HOST     = "/prod/property-sage-synchropy/sage-database-host",
    PROPERTY_SAGE_DB_PORT     = "/prod/property-sage-synchropy/sage-database-port",
    PROPERTY_SAGE_DB_DATABASE = "/prod/property-sage-db/db-api-username",
    PROPERTY_SAGE_API_URL     = "/prod/property-sage-synchropy/sage-api-url",

    PROPERTY_SEARCH_DB_USER     = "/prod/property-search-db/db-api-username",
    PROPERTY_SEARCH_DB_PASSWORD = "/prod/property-search-db/db-api-password",
    PROPERTY_SEARCH_DB_HOST     = "/prod/property-sage-synchropy/search-database-host",
    PROPERTY_SEARCH_DB_PORT     = "/prod/property-sage-synchropy/search-database-port",
    PROPERTY_SEARCH_DB_DATABASE = "/prod/property-search-db/db-api-username",

    GEOGRAPHICS_DB_USER     = "/prod/geographics-db/db-api-username",
    GEOGRAPHICS_DB_PASSWORD = "/prod/geographics-db/db-api-password",
    GEOGRAPHICS_DB_HOST     = "/prod/property-sage-synchropy/geographics-database-host",
    GEOGRAPHICS_DB_PORT     = "/prod/property-sage-synchropy/geographics-database-port",
    GEOGRAPHICS_DB_DATABASE = "/prod/geographics-db/db-api-username",

    BIGQUERY_SCHEMA = "/prod/property-sage-synchropy/bigquery-schema",

    API_URL = "/prod/global/property-sage-synchropy-api-url",

    GOOGLE_KEY_PATH    = "/any/property-sage-synchropy/google-key-path",
    GOOGLE_KEY         = "/prod/property-sage-synchropy/google-key",
    DATADOG_API_KEY    = "/any/datadog/api-key",
    SCOPE              = "/any/global/server-scope",
    GEOGRAPHIC_API_URL = "/prod/global/geographics-api-url",

    AWS_ACCESS_KEY_ID            = "/any/aws/access-key-id",
    AWS_SECRET_ACCESS_KEY        = "/any/aws/secret-access-key",
    AWS_ACCOUNT_ID               = "/any/aws/account-id",
    AWS_REGION                   = "/any/aws/region",
    DEMOGRAPHICS_STATS_TOPIC     = "/prod/property-sage-synchropy/demographics-stats-topic",
    MARKET_STATS_TOPIC           = "/prod/property-sage-synchropy/market-stats-topic",
    NEW_DEVELOPMENTS_STATS_TOPIC = "/prod/property-sage-synchropy/new-developments-stats-topic",
    COMP_RECORD_TOPIC            = "/prod/property-sage-synchropy/comp-record-topic",
  }

  tags = local.default_tags
}
