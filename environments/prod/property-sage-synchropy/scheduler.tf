module "property_sage_synchropy_schedule_continuous_sync_prod" {
  event_name = "prod-synchropy-cont-sync" # make it short
  source     = "../../../modules/request-scheduler"
  network    = var.environment.private_network

  schedule_expression = "rate(180 minutes)"

  url    = "https://${local.service_url}/sync"
  method = "POST"

  body = jsonencode({
    entities      = [
      "new_developments",
      "new_developments_stats",
      "notify_market_stats",
      "demographic_data_and_percentiles",
      "flood_score"
    ],
    update_scores = true,
    time_delta = {
      time_delta_unit  = "hours",
      time_delta_value = 4
    }
  })
}
