module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2 * 1024
    memory = 8 * 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  iam_extra_policies = ["arn:aws:iam::aws:policy/AmazonBedrockFullAccess"]

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/keydocs-ai"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
    ENV     = "prod"
    SCOPE   = "server"
    PORT    = "8080"
    WORKERS = "4"

    LANGCHAIN_TRACING_V2 = "true"
    LANGCHAIN_ENDPOINT   = "https://api.smith.langchain.com"
    LANGCHAIN_PROJECT    = "keydocs-ai-prod"

    PROPERTY_ANALYZER_BASE_PATH = "https://property-analyzer-api.whykeyway.com"
    PROPERTY_ANALYZER_TIMEOUT   = "60"
    OPENAI_API_ENDPOINT         = "https://api.openai.com"
    KEYASSIST_API_BASE_PATH     = "https://key-assist-api.whykeyway.com"
    KEYASSIST_API_TIMEOUT       = "60"
    BUCKETS                     = jsonencode({ "embeddings" : "keydocs-embeddings-prod" })
    TOPICS                      = jsonencode({ "extractors_from_file" : "prod-data_gpt-extractors_news" }) # create a new topic?
    QUEUES                      = jsonencode({ "extractors_from_file" : { "queue_name" : "prod-keydocs_ai-datatable_extractors", "workers" : 10, "visibility_timeout" : 120, "max_messages" : 10, "wait_time_seconds" : 10 } })

    DD_PROFILING_ENABLED          = "true"
    DATADOG_ENABLED               = "true"
    DD_RUNTIME_METRICS_ENABLED    = "true"
    DD_PROFILING_TIMELINE_ENABLED = "true"

    # Number of pages to retrieve from the documents
    SEARCH_K = "25"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION                = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"
    GH_SSH_PRIVATE_KEY    = "/any/github/private-ssh-key"

    DD_API_KEY = "/any/datadog/api-key"

    OPENAI_API_KEY               = "${local.ecs_parameter_prefix}/OPENAI_API_KEY"
    LANGCHAIN_API_KEY            = "${local.ecs_parameter_prefix}/LANGCHAIN_API_KEY"
    PROPERTY_ANALYZER_SECRET_KEY = "/any/property-analyzer-api/keyway-key"

  }

  tags = local.default_tags
}
