module "rds" {
  source = "../../../modules/rds-postgres"

  server_name      = local.rds_name
  root_username    = "${local.database_name}_root" # only az and underscores (_)
  parameter_prefix = "/prod/${local.rds_name}/"     # root user and password

  instance_class = "db.t4g.micro"
  rds_network    = var.environment.private_network

  tags = local.default_tags
}

module "rds_backup" {
  source = "../../../modules/aws-backup"
  vault  = local.rds_name
  # schedule = "cron(0 3 * * ? *)"
  resources_arn = [
    module.rds.arn
  ]
}
