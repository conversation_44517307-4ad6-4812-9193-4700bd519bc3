module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 8192
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  codebuild_image = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  chatbot_arn     = var.environment.chatbot_arn

  github_repository  = "unlockre/keyreader-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    ENV   = "prod"
    SCOPE = "server"
    PORT  = "8080"

    API_URL     = local.gateway_tls_url
    GATEWAY_URL = local.gateway_url

    DD_PROFILING_ENABLED    = "true"
    DATADOG_ENABLED         = "true"
    DISABLE_TQDM            = "true"
    LAYOUT_BATCH_SIZE       = 32
    LANGCHAIN_PROJECT       = "keyreader"
    LANGCHAIN_ENDPOINT      = "https://api.smith.langchain.com"
    LANGCHAIN_TRACING_V2    = "false"
    MODAL_BASE_URL          = "https://keyway--keyway-modal-ocr-fastapi-app.modal.run"
    KEYREADER_MODEL         = "google;gemini-2.0-flash-lite-preview-02-05"
    KEYREADER_MODEL_TIMEOUT = 10
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"
    GH_SSH_PRIVATE_KEY    = "/any/github/private-ssh-key" # note: used for codebuild, remove later

    SLACK_API_KEY     = "/prod/keyreader-api/SLACK_API_KEY"
    GOOGLE_API_KEY    = "/prod/keyreader-api/GOOGLE_API_KEY"
    LANGCHAIN_API_KEY = "/prod/keyreader-api/LANGCHAIN_API_KEY"

  }

  tags = local.default_tags
}
