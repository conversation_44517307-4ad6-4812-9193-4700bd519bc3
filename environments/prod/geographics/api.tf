module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/geographics-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    DATADOG_ENABLED      = "true"
    DD_PROFILING_ENABLED = "true"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    POSTGRES_DB_URL      = "/prod/geographics-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/geographics-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/geographics-db/db-api-password",

    API_URL         = "/prod/global/geographics-api-url",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",
  }

  tags = local.default_tags
}
