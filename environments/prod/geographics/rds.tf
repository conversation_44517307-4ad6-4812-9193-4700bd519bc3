module "rds" {
  source = "../../../modules/rds-postgres"

  server_name      = local.rds_name
  root_username    = "${local.database_name}_root" # only az and underscores (_)
  parameter_prefix = "/sre/${local.rds_name}/"     # root user and password

  instance_class = "db.t4g.small"
  rds_network    = var.environment.private_network

  # custom_parameters = {
  #   "max_connections" = "100"
  # }

  tags = local.default_tags
}

module "rds_backup" {
  source = "../../../modules/aws-backup"
  vault  = local.rds_name
  # schedule = "cron(0 3 * * ? *)"
  resources_arn = [
    module.rds.arn
  ]
}
