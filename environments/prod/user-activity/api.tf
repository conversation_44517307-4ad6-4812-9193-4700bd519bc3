module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/user-activity-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    API_URL             = "/prod/global/user-activity-api-url",
    AWS_ACCESS_KEY      = "/any/aws/access-key-id",
    AWS_SECRET_KEY      = "/any/aws/secret-access-key",
    USER_ACTIVITY_TABLE = "/prod/user-activity-api/user-activity-table",

    SCOPE                = "/any/global/server-scope",
    DATADOG_ENABLED      = "/prod/user-activity-api/datadog-enabled",
    DATADOG_API_KEY      = "/any/datadog/api-key",
    DD_PROFILING_ENABLED = "/prod/user-activity-api/profiling-enabled",
    DYNAMODB_TIMEOUT     = "/prod/user-activity-api/dynamodb-timeout",

    SECURITY_ENABLED = "/prod/user-activity-api/security_enabled",
    AUTH0_DOMAIN     = "/prod/user-activity-api/auth0_domain",
    AUTH0_AUDIENCES  = "/prod/user-activity-api/auth0_audiences",
    AUTH0_ISSUER     = "/prod/user-activity-api/auth0_token_issuer"
  }

  tags = local.default_tags
}
