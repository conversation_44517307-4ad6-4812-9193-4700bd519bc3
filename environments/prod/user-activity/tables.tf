module "user_activity_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}_prod"
  capacity_mode       = "PROVISIONED"
  hash_key  = "PK"
  range_key = "SK"

  attributes = {
    PK      = "S"
    SK      = "S"
    LSI1-SK = "S"
  }
  local_indexes = [
    {
      name            = "LSI1"
      range_key       = "LSI1-SK"
      projection_type = "ALL"
    }
  ]

  tags = merge(local.default_tags, {})
}
