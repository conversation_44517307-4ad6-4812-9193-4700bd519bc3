module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 4 * 1024
    memory = 8 * 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-search-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  health_check_interval            = 120
  health_check_unhealthy_threshold = 10
  health_check_healthy_threshold   = 2

  ecs_variables = {
    # VAR_NAME = "string"
    DEFAULT_SQS_GROUP_DELAY   = 0,
    AWS_DATABASE_SERVICE_NAME = "property-search-db",
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    POSTGRES_DB_URL      = "/prod/property-search-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/property-search-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/property-search-db/db-api-password",

    API_URL         = "/prod/global/property-search-api-url",
    DATADOG_ENABLED = "/prod/property-search-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AWS_SQS_PROPERTY_NEWS = "/prod/property-search-api/aws-sqs-property-news",

    GEOGRAPHIC_API_URL = "/prod/global/geographics-api-url",

    SPLIT_IO_TOKEN   = "/prod/property-search-api/split-io-token",
    SPLIT_IO_TIMEOUT = "/prod/property-search-api/split-io-timeout",

    LUCENE_REFRESH_RATE_IN_MINUTES = "/prod/property-search-api/lucene-refresh-rate-in-minutes",

    CLOUD_SEARCH_SEARCH_ENDPOINT    = "/prod/property-search-api/cloudsearch-search-endpoint",
    CLOUD_SEARCH_DOCUMENTS_ENDPOINT = "/prod/property-search-api/cloudsearch-documents-endpoint",
    CLOUD_SEARCH_DOMAIN_NAME        = "/prod/property-search-api/cloudsearch-domain-name",

    SECURITY_ENABLED       = "/prod/property-search-api/security_enabled",
    AUTH0_DOMAIN           = "/prod/property-search-api/auth0_domain",
    AUTH0_AUDIENCES        = "/prod/property-search-api/auth0_audiences",
    AUTH0_ISSUER           = "/prod/property-search-api/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID = "/prod/global/keyway-organization-id"
    KEYWAY_KEY             = "/any/property-search-api/keyway-key"
  }

  tags = local.default_tags
}
