module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/rent-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    DATADOG_ENABLED                                      = "true",
    DD_PROFILING_ENABLED                                 = "true",
    DD_INTEGRATION_KOTLIN_COROUTINE_EXPERIMENTAL_ENABLED = "true"


    AWS_SQS_UNIT_RENT_DATA                    = "prod-rent-unit_rent_data",
    AWS_SQS_PROPERTY_CONCESSIONS              = "prod-rent-property_concessions.fifo",
    AWS_SQS_DELETE_UNIT_RENT_DATA             = "prod-rent-background_tasks",
    AWS_SQS_BACKGROUND_TASKS                  = "prod-rent-background_tasks",
    AWS_SQS_PROPERTY_UNIT_DATA                = "prod-rent-multifamily_units_data",
    AWS_SQS_UNIT_RENT_DATA_WORKERS            = 0,
    AWS_SQS_PROPERTY_CONCESSIONS_WORKERS      = 0,
    AWS_SQS_DELETE_UNIT_RENT_DATA_WORKERS     = 1,
    AWS_SQS_BACKGROUND_TASKS_WORKERS          = 1,
    AWS_SQS_PROPERTY_UNIT_DATA_WORKERS        = 0,
    AWS_SQS_UNIT_RENT_DATA_MAX_MESSAGES       = 1,
    AWS_SQS_PROPERTY_UNIT_DATA_MAX_MESSAGES   = 1,
    AWS_SQS_PROPERTY_CONCESSIONS_MAX_MESSAGES = 1,
    DEFAULT_SQS_RENT_GROUP_DELAY              = 0,
    ENABLE_SQS_CONSUMERS                      = "true",
    AWS_DATABASE_SERVICE_NAME                 = "rent-prod-db",
    AWS_SQS_LAST_SEEN_QUEUE                   = "prod-rent-property_last_rent_data"

  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    SCOPE   = "/any/global/server-scope",
    API_URL = "/prod/rent-api/api-url",

    DATADOG_API_KEY = "/any/datadog/api-key",

    # AWS
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    # DB
    POSTGRES_DB_URL      = "/prod/rent-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/rent-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/rent-db/db-api-password",

    # Auth0
    AUTH0_DOMAIN   = "/prod/global/auth0_domain",
    AUTH0_AUDIENCE = "/prod/rent-api/auth0_audience",
    AUTH0_ISSUER   = "/prod/global/auth0_token_issuer",
  }

  tags = local.default_tags
}
