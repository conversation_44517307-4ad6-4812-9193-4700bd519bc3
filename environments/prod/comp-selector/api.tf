module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/comp-selector-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    DATADOG_ENABLED                                      = "true",
    DD_PROFILING_ENABLED                                 = "true",
    DD_INTEGRATION_KOTLIN_COROUTINE_EXPERIMENTAL_ENABLED = "true"


    AWS_SQS_PROPERTY_NEWS                      = "prod-comps-selector_properties-news",
    AWS_SQS_PROPERTY_QUALITY_NEWS              = "prod-comps-selector_properties-quality-news",
    AWS_SNS_COMP_SETS_EVENTS                   = "prod-comp_sets-events",
    AWS_SQS_RENT_LAST_SEEN_QUEUE               = "prod-rent-property_last_rent_data",
    USER_COMP_SET_ONLY_ORG_IDS                 = "[\"01J8G220924M4Z117G6EN51943\"]",
    AWS_SQS_PROPERTY_NEWS_WORKERS              = 1,
    AWS_SQS_PROPERTY_NEWS_MAX_MESSAGES         = 1,
    AWS_SQS_PROPERTY_QUALITY_NEWS_WORKERS      = 1,
    AWS_SQS_PROPERTY_QUALITY_NEWS_MAX_MESSAGES = 1,
    AWS_SQS_RENT_LAST_SEEN_QUEUE_WORKERS       = 1,
    AWS_SQS_RENT_LAST_SEEN_QUEUE_MAX_MESSAGES  = 1,
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    SCOPE   = "/any/global/server-scope",
    API_URL = "/prod/comp-selector-api/api-url",

    DATADOG_API_KEY       = "/any/datadog/api-key",
    ORGANIZATIONS_API_URL = "/prod/global/organizations-api-url",

    # AWS
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    # DB
    POSTGRES_DB_URL      = "/prod/comp-selector-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/comp-selector-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/comp-selector-db/db-api-password",

    # Auth0
    AUTH0_DOMAIN   = "/prod/global/auth0_domain",
    AUTH0_AUDIENCE = "/prod/comp-selector-api/auth0_audience",
    AUTH0_ISSUER   = "/prod/global/auth0_token_issuer",
  }

  tags = local.default_tags
}
