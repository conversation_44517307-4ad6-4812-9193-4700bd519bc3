resource "aws_iam_policy" "s3_lambda" {
  depends_on  = [module.cloudfront]
  name        = "${local.app_name}-${var.environment.private_network.vpc_name}-s3-lambda"
  description = "Full access to the S3 bucket for ${local.app_name}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:*",
        ]
        Resource = [
          module.cloudfront.s3_bucket_arn,
          "${module.cloudfront.s3_bucket_arn}/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:ListBucket"
        ],
        Resource = [
          "arn:aws:s3:::${local.kfile_bucket_name}",
          "arn:aws:s3:::${local.kfile_bucket_name}/*"
        ]
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
