module "cloudfront" {
  source               = "../../../modules/cloudfront-s3"
  cloudfront_domain    = "keydocs-pdf-img.whykeyway.com"
  s3_bucket_name       = "keydocs-pdf-img-prod-whykeyway"
  cors_allowed_origins = ["*.whykeyway.com", "cdn.whykeyway.com", "*.vercel.app", "localhost:3000", "localhost:3001"]
  auth0_protection     = true
  tags                 = local.default_tags

  response_custom_headers = {
    "Cache-Control" = "public, max-age=2592000" # 30 days
  }

  min_ttl     = 1 * 24 * 60 * 60  # 1 day
  default_ttl = 30 * 24 * 60 * 60 # 30 days
  max_ttl     = 90 * 24 * 60 * 60 # 90 days
}
