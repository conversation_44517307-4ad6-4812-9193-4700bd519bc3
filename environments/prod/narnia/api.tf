module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = false

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/narnia"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    DATADOG_ENABLED = "false",
    SCOPE           = "server",
    ENV             = "prod",
  }

  ecs_secrets = {
    GCP_CREDENTIALS_FILE      = "/any/narnia/gcp-credentials-path",
    GCP_CREDENTIALS_JSON_FILE = "/prod/narnia/gcp-credentials",

    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    API_URL = "/prod/global/narnia-url",

    DATADOG_API_KEY = "/any/datadog/api-key",
  }

  tags = local.default_tags
}
