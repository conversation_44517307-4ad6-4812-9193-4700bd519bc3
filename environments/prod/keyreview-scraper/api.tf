module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 8192
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  codebuild_image = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  chatbot_arn     = var.environment.chatbot_arn

  github_repository  = "unlockre/keyreview-scraper-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    ENV     = "prod"
    SCOPE   = "server"
    PORT    = "8080"
    WORKERS = "2"

    PROXY_SERVICE_HOST              = "https://proxy-service.whykeyway.com"
    DEFAULT_TASK_CACHE_TTL_IN_HOURS = "24"

    API_URL        = local.gateway_tls_url
    GATEWAY_URL    = local.gateway_url
    S3_BUCKET_NAME = module.keyreview_scraper_proxy_responses_s3.bucket_name

    DD_PROFILING_ENABLED = "true"
    DATADOG_ENABLED      = "true"

    DD_TRACE_REMOVE_INTEGRATION_SERVICE_NAMES_ENABLED = "true"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"

    PROXY_SERVICE_API_KEY = "/any/keyway/proxy-service-api-key"

    POSTGRES_DB_URL      = "/prod/keyreview-scraper-db/db-connection-string",
    POSTGRES_DB_HOST     = "/prod/keyreview-scraper-db/db-host",
    POSTGRES_DB_PORT     = "/prod/keyreview-scraper-db/db-port",
    POSTGRES_DB_NAME     = "/prod/keyreview-scraper-db/db-name",
    POSTGRES_DB_USER     = "/prod/keyreview-scraper-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/keyreview-scraper-db/db-api-password",
  }

  tags = local.default_tags
}
