module "numbers_generator_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-numbers_generator"
  capacity_mode       = "pay_per_request"
  deletion_protection = false
  hash_key            = "countryCode"
  range_key           = "stateCode"

  attributes = {
    countryCode = "S"
    stateCode   = "S"
  }
  on_demand_throughput = {
    read  = 0
    write = 30
  }

  tags = merge(local.default_tags, {})
}

module "property_identifications_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-property_identifications"
  capacity_mode       = "pay_per_request"
  deletion_protection = false
  hash_key            = "keywayCode"

  attributes = {
    keywayCode          = "S"
    formattedAddress    = "S"
    placeKeyExternalKey = "S"
  }

  global_indexes = [
    {
      name            = "GSI_formattedAddress"
      projection_type = "ALL"
      hash_key        = "formattedAddress"
      read_capacity   = 0
      write_capacity  = 0
    },
    {
      name            = "GSI_placeKey"
      projection_type = "ALL"
      hash_key        = "placeKeyExternalKey"
      read_capacity   = 0
      write_capacity  = 0
    }
  ]

  tags = merge(local.default_tags, {})
}

module "queries_cache_dynamo" {
  source              = "../../../modules/dynamodb-table"
  table_name          = "${local.app_name}-queries_cache"
  capacity_mode       = "pay_per_request"
  deletion_protection = false
  hash_key            = "key"

  attributes = {
    key         = "S"
    keywayCode  = "S"
  }
  global_indexes = [
    {
      name = "GSI_keywayCode"
      projection_type = "ALL"
      hash_key = "keywayCode"
      read_capacity = 0
      write_capacity = 0
      on_demand_throughput = {
        read_units = 300
        write_units = 100
      }
    }
  ]

  on_demand_throughput = {
    read  = 300
    write = 100
  }

  tags = merge(local.default_tags, {})
}
