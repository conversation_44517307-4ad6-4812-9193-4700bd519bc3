module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/keygen"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    AWS_SNS_IDENTIFICATION_NEWS = "prod-keygen-identification_news"
    GEOGRAPHICS_URL             = "https://geographics-api.whykeyway.com"
  }

  ecs_secrets = {
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",

    GOOGLE_MAPS_API_KEY           = "/prod/keygen/google-maps-api-key",
    DATADOG_API_KEY               = "/prod/keygen/datadog-api-key",
    PLACEKEY_API_KEY              = "/prod/keygen/placekey-api-key",
    ENV                           = "/prod/global/env",
    DATADOG_ENABLED               = "/prod/keygen/datadog-enabled",
    FIRST_AMERICAN_LAMBDA_URL     = "/prod/global/first-american-lambda-url"
    POSTGRES_DB_URL               = "/prod/keygen-db/db-connection-string",
    POSTGRES_DB_USER              = "/prod/keygen-db/db-api-username",
    POSTGRES_DB_PASSWORD          = "/prod/keygen-db/db-api-password",
    ADDRESS_NORMALIZER_LAMBDA_URL = "/prod/global/address-normalizer-lambda-url"
  }

  tags = local.default_tags
}

module "sns-prod-keygen-identification_news" {
  source            = "../../../modules/sns-topic"
  sns_topic_name    = "prod-keygen-identification_news"
  sqs_subscriptions = ["arn:aws:sqs:us-east-1:681574592108:prod-property_assets-identification_news"]

  tags = local.default_tags
}

