module "rds" {
  source = "../../../modules/rds-postgres"

  server_name      = local.rds_name
  root_username    = "${local.database_name}_root" # only az and underscores (_)
  parameter_prefix = "/sre/${local.rds_name}/"     # root user and password

  instance_class = "db.m6g.large"
  rds_network    = var.environment.private_network

  max_allocated_storage = 600

  # custom_parameters = {
  #   "max_connections" = "100"
  # }

  tags = local.default_tags
}
