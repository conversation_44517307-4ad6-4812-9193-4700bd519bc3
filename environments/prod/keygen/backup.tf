module "keygen_backup_prod" {
  source = "../../../modules/aws-backup"
  vault  = "keygen-prod"
  # schedule = "cron(0 3 * * ? *)"
  resources_arn = [
    "arn:aws:dynamodb:us-east-1:681574592108:table/keygen-numbers_generator",
    "arn:aws:dynamodb:us-east-1:681574592108:table/keygen-property_identifications",
    "arn:aws:dynamodb:us-east-1:681574592108:table/keygen-queries_cache",
    # "arn:aws:rds:us-east-1:681574592108:db:deal-room-api-dev"
  ]
  tags = local.default_tags
}
