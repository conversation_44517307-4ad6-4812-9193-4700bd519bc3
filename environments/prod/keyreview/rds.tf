module "rds" {
  source = "../../../modules/rds-postgres"

  server_name       = "${local.app_name}-db"
  root_username     = "${local.database_name}_root"
  parameter_prefix  = "/sre/${local.rds_name}"

  instance_class  = "db.t4g.medium"
  rds_network     = var.environment.private_network

  allocated_storage     = 250
  max_allocated_storage = 300

  tags = local.default_tags
}

module "rds_backup" {
  source  = "../../../modules/aws-backup"
  vault   = local.rds_name
  resources_arn = [
    module.rds.arn
  ]
}
