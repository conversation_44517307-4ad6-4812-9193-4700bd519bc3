module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/keyreview-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    GOOGLE_REVIEWS_API_HOST  = "https://google-maps-scraper.whykeyway.com"
    PROPERTY_ASSETS_API_HOST = "https://property-assets-api.whykeyway.com"
    GPT_API_HOST             = "https://data-gpt-api.whykeyway.com"
    KEY_ASSIST_HOST          = "https://key-assist-api.whykeyway.com"
    SENTIMENT_ANALYSIS_MODEL = "gpt-4o-mini"
    MAX_REVIEWS_TO_SCRAPE    = 1000
    DATADOG_ENABLED          = "true"
    COMP_SETS_EVENTS_QUEUE   = module.sqs-prod-keyreview-comp-sets-events.name
    KEY_SEARCH_HOST          = "https://keysearch-************.us-central1.run.app"
    REVIEWS_SCRAPER_API_HOST = "https://keyreview-scraper-api.whykeyway.com"

    PROPERTY_TASK_LAUNCH_EVENTS_QUEUE = module.sqs-prod-keyreview-property_task_launch.name
  AWS_SNS_PROPERTY_TASK_LAUNCH_EVENTS = module.sqs-prod-keyreview-property_task_launch.name }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    POSTGRES_DB_URL      = "/prod/keyreview-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/keyreview-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/keyreview-db/db-api-password",

    API_URL         = "/dev/global/keyreview-api-url",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",
    GPT_AUTH_TOKEN  = "/any/data-gpt-api/auth-token",
    ENV             = "/prod/global/env",
  }

  tags = local.default_tags
}
