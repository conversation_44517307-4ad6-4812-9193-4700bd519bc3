module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  idle_timeout = 600

  codebuild_image = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
  chatbot_arn     = var.environment.chatbot_arn

  github_repository  = "unlockre/keyreview-manager-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    ENV   = "prod"
    SCOPE = "server"
    PORT  = "8080"

    USE_AZURE = "1"
    DAYS_TTL  = "2"

    KEY_REVIEWS_API = "https://keyreview-api.whykeyway.com"

    DD_PROFILING_ENABLED = "true"
    DATADOG_ENABLED      = "true"

    API_URL     = local.service_url
    GATEWAY_URL = local.gateway_url
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    ACCOUNT_ID            = "/any/aws/account-id"
    REGION_NAME           = "/any/aws/region"
    AWS_ACCESS_KEY_ID     = "/any/aws/access-key-id"
    AWS_ACCESS_KEY        = "/any/aws/access-key-id"
    AWS_SECRET_ACCESS_KEY = "/any/aws/secret-access-key"

    POSTGRES_DB_URL      = "/prod/keyreview-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/keyreview-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/keyreview-db/db-api-password",

    ORGS_POSTGRES_DB_URL      = "/prod/organizations-db/db-connection-string",
    ORGS_POSTGRES_DB_USER     = "/prod/organizations-db/db-api-username",
    ORGS_POSTGRES_DB_PASSWORD = "/prod/organizations-db/db-api-password",

    GOOGLE_CREDENTIALS_JSON = "/prod/keyreview-manager/google-credentials-json"

    SENDGRID_API_KEY = "/any/keyreview-manager/sendgrid-api-key"

    AUTH_TOKEN       = "${local.ecs_parameter_prefix}/AUTH_TOKEN"
    SLACK_TOKEN      = "${local.ecs_parameter_prefix}/SLACK_TOKEN"
    DATA_GPT_API_KEY = "/any/data-gpt-api/auth-token"
    DATA_GPT_API_URL = "/prod/global/data-gpt-api-url"
  }

  tags = local.default_tags
}
