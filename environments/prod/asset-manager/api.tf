module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/asset-manager-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_image           = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    SCOPE   = "/any/global/server-scope",
    API_URL = "/prod/global/asset-manager-api-url",

    # DATADOG
    DATADOG_ENABLED = "/prod/asset-manager-api/datadog-enabled",
    DATADOG_API_KEY = "/any/datadog/api-key",

    # AWS
    AWS_ACCOUNT    = "/any/aws/account-id",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",

    # SQS
    READ_EXCEL_FILE_SQS     = "/prod/asset-manager-api/aws-sqs-read-excel-file-queue-name",
    EXCEL_FILE_RESULT_SQS   = "/prod/asset-manager-api/excel_file_result_sqs",
    REAL_PAGE_EVENTS_SQS    = "/prod/asset-manager-api/real_page_events_sqs",
    YARDI_NOTIFICATIONS_SQS = "/prod/asset-manager-api/yardi-updates-queue-name",

    # DB
    POSTGRES_DB_URL      = "/prod/asset-manager-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/asset-manager-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/asset-manager-db/db-api-password",

    # Services urls
    K_FILE_SERVICE_URL      = "/prod/global/kfile-api-url",
    PROPERTY_ASSETS_API_URL = "/prod/global/property-assets-url",

    # Auth0
    AUTH0_DOMAIN   = "/prod/asset-manager-api/auth0_domain",
    AUTH0_AUDIENCE = "/prod/asset-manager-api/auth0_audience",
    AUTH0_ISSUER   = "/prod/asset-manager-api/auth0_token_issuer",

    # SPLIT IO
    SPLIT_IO_TOKEN = "/prod/asset-manager-api/split_io_token"
  }

  tags = local.default_tags
}
