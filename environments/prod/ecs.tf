module "prod_cluster" {
  source       = "../../modules/ecs-fargate-cluster"
  cluster_name = "production"
  waf_enabled  = true
  # dns_prefix = ""

  fargate_spot_mode = "hybrid"

  auth0_settings = {
    OPENID_CONFIGURATION = "https://auth.whykeyway.com/.well-known/openid-configuration",
    AUDIENCE             = "https://keyway-api.whykeyway.com",
    TOKEN_ISSUER         = "https://auth.whykeyway.com/"
  }

  gateway_container = {
    cpu      = 512
    memory   = 1024
    replicas = 2
  }

  private_network = module.prod_vpc.private_network
  public_network  = module.prod_vpc.public_network
}
