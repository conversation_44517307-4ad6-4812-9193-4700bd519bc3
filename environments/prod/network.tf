module "prod_vpc" {
  source = "../../modules/networking"
  vpc = {
    name = "prod",
    cidr = "10.130.0.0/16"
  }
  nat_gateways = [
    { zone = "us-east-1a" },
    { zone = "us-east-1b" },
  ]
  public_subnets = [
    { zone = "us-east-1a", cidr = "10.130.0.0/22" },
    { zone = "us-east-1b", cidr = "10.130.4.0/22" }
  ]
  private_subnets = [
    { zone = "us-east-1a", cidr = "10.130.100.0/22" },
    { zone = "us-east-1b", cidr = "10.130.104.0/22" }
  ]
  sg_extra_cidrs = [
    "10.4.0.0/16", # sre-vpc
    "10.5.0.0/16", # gcp: data-vpc
    "10.6.0.0/16", # gcp: sre-vpc
    "10.7.0.0/16", # gcp: composer-eks
    "10.8.0.0/16", # gcp: composer-dev-vpc
    "10.9.0.0/16", # gcp: service-network

    "10.110.0.0/16", # stg-vpc (temporarily)
  ]
  enable_flow_logs = true
}

module "prod_tg_attachments" {
  source = "../../modules/transit-gateway-attachments"

  transit_gateway_id = "tgw-07dbeb395284ee143"
  destination_cidr   = "10.0.0.0/8"
  vpc                = module.prod_vpc.private_network.vpc
  subnets            = module.prod_vpc.private_network.subnets
  route_tables       = concat(module.prod_vpc.public_network.route_tables, module.prod_vpc.private_network.route_tables)
  tags               = { Name = "prod-private-tgw" }
}
