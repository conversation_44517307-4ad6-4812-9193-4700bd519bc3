module "api" {
  app_name   = local.ecs_name
  cluster    = var.environment.cluster
  source     = "../../../modules/ecs-service-deployment-v2"
  depends_on = [module.postgres]

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/property-analyzer-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
    AWS_SNS_EXTRACTORS_TO_PROCESS   = "prod-property_analyzer-extractors_to_process"
    AWS_SNS_DATATABLE_EXTRACTORS    = "prod-property_analyzer-datatable_extractors"
    AWS_SQS_METADATA_FROM_FILE_NEWS = "prod-property_analyzer-metadata_news"
    AWS_SQS_EXTRACTORS_TO_PROCESS   = "prod-property_analyzer-extractors_to_process"
    AWS_SQS_EXTRACTORS_NEWS         = "prod-property_analyzer-extractors_news"
    FILE_METADATA_BUCKET_NAME       = "property-analyzer-files-prod"
    DOCUMENT_PARSER_API_URL         = "https://document-parser.whykeyway.com"
    GEOGRAPHIC_API_URL              = "https://geographics-api.whykeyway.com"
    RENT_API_URL                    = "https://rent-api.whykeyway.com"
    COMP_SELECTOR_API_URL           = "https://comp-selector-api.whykeyway.com"

    USE_KEYDOCS_AI = "True"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    API_URL         = "/prod/global/property-analyzer-api-url",
    DATADOG_ENABLED = "/prod/property-analyzer-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AUTH0_DOMAIN           = "/prod/global/auth0_domain",
    AUTH0_AUDIENCES        = "/prod/property-analyzer-api/auth0_audiences",
    AUTH0_ISSUER           = "/prod/global/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID = "/prod/global/keyway-organization-id",
    SECRET_KEY             = "/any/property-analyzer-api/keyway-key"

    DATA_GPT_API_URL               = "/prod/global/data-gpt-api-url",
    DATA_GPT_API_AUTH_TOKEN        = "/any/data-gpt-api/auth-token",
    DEMOGRAPHICS_API_URL           = "/prod/global/demographics-api-url",
    COMPS_API_URL                  = "/prod/global/comps-api-url",
    PROPERTY_ASSETS_API_URL        = "/prod/global/property-assets-url",
    PROPERTY_SEARCH_API_URL        = "/prod/global/property-search-api-url",
    PROPERTY_SEARCH_API_AUTH_TOKEN = "/any/property-search-api/keyway-key",
    DEAL_ROOM_API_URL              = "/prod/global/deal-room-api-url",
    KFILE_URL                      = "/prod/global/kfile-api-url",
    BUY_BOX_API_URL                = "/prod/global/buy-box-api-url",

    FIRST_PASS_ANALYSES_BUCKET_NAME = "/prod/property-analyzer-api/first-pass-bucket",
    FIRST_PASS_ANALYSES_TABLE_NAME  = "/prod/property-analyzer-api/first-pass-table-name",
    FILES_TABLE_NAME                = "/prod/property-analyzer-api/files-table-name",

    AWS_SQS_CLASSIFY_FILE_NEWS           = "/prod/property-analyzer-api/aws-sqs-classify-file-news",
    AWS_SQS_PROPERTY_DATA_FROM_FILE_NEWS = "/prod/property-analyzer-api/aws-sqs-property-data-news",

    POSTGRES_DB_URL      = "/prod/property-analyzer-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/property-analyzer-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/property-analyzer-db/db-api-password",
  }

  tags = local.default_tags
}
