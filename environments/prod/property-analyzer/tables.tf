module "first_pass_analyses_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "first-pass-analyses_prod"
  capacity_mode = "ON_DEMAND"
  hash_key      = "PK"
  range_key     = "SK"

  attributes = {
    PK = "S"
    SK = "S"
  }

  tags = merge(local.default_tags, {})
}

module "files_dynamo" {
  source        = "../../../modules/dynamodb-table"
  table_name    = "property-analyzer_files_prod"
  capacity_mode = "ON_DEMAND"
  hash_key      = "PK"

  attributes = {
    PK = "S"
  }

  tags = merge(local.default_tags, {})
}
