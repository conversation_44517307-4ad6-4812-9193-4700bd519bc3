terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~>5.0"
    }
    datadog = {
      source = "datadog/datadog"
    }
  }

  required_version = ">=1.5"

  backend "s3" {
    bucket = "keyway-terraform-states"
    key    = "prod/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = "us-east-1"
}

provider "datadog" {
  # api_key: env.DD_API_KEY
  # app_key: env.DD_APP_KEY
}
