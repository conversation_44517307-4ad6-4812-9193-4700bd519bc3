module "postgres" {
  source = "../../../modules/postgres-config"

  database_readonly_users = ["fer", "javi", "metabase", "chiri"]
  database_users          = ["ezequiel", "ignacio_boudgouste"]
  database_extensions     = ["postgis", "postgis_raster", "postgis_topology", "fuzzystrmatch", "address_standardizer", "address_standardizer_data_us", "postgis_tiger_geocoder"]

  # don't modify these
  rds_server_config = module.rds.rds_server_config
  database_name     = local.database_name
  database_api_user = local.database_name
  parameter_prefix  = local.rds_parameter_prefix
}
