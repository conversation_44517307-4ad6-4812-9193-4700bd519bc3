module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 2048
    memory = 4096
  }

  private_network = var.environment.private_network
  public_endpoint = true

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/buy-box-api"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = true

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    AWS_ACCESS_KEY = "/any/aws/access-key-id",
    AWS_SECRET_KEY = "/any/aws/secret-access-key",
    AWS_REGION     = "/any/aws/region",
    AWS_ACCOUNT    = "/any/aws/account-id",

    API_URL         = "/prod/global/buy-box-api-url",
    DATADOG_ENABLED = "/prod/buy-box-api/datadog-enabled",
    SCOPE           = "/any/global/server-scope",
    DATADOG_API_KEY = "/any/datadog/api-key",

    AUTH0_DOMAIN            = "/prod/global/auth0_domain",
    AUTH0_AUDIENCES         = "/prod/buy-box-api/auth0_audiences",
    AUTH0_ISSUER            = "/prod/global/auth0_token_issuer",
    KEYWAY_ORGANIZATION_ID  = "/prod/global/keyway-organization-id",
    ORG_WITH_PRIVATE_CONFIG = "/prod/buy-box-api/org-with-private-config"

    POSTGRES_DB_URL      = "/prod/buy-box-db/db-connection-string",
    POSTGRES_DB_USER     = "/prod/buy-box-db/db-api-username",
    POSTGRES_DB_PASSWORD = "/prod/buy-box-db/db-api-password",

    DEMOGRAPHICS_API_URL = "/prod/global/demographics-api-url",
  }

  tags = local.default_tags
}
