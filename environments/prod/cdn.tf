module "cdn_prod" {
  source               = "../../modules/cloudfront-s3"
  cloudfront_domain    = "cdn.whykeyway.com"
  s3_bucket_name       = "cdn-whykeyway"
  cors_allowed_origins = ["keyway-prod.us.auth0.com", "auth.whykeyway.com", "*.whykeyway.com", "cdn.whykeyway.com"]
  # default ttl on prod
}

#module "cdn_stg_backup" {
#  source = "../../modules/aws-backup"
#  vault  = "cdn-prod"
#  resources_arn = [
#    module.cdn_prod.s3_bucket_arn
#  ]
#}
