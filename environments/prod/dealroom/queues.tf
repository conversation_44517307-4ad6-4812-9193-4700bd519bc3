module "deal_room_sqs_cron_jobs_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-cron_jobs"
  tags       = local.default_tags
}

module "deal_room_sqs_loi_signed_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-loi_signed"
  tags       = local.default_tags

  receive_wait_time_seconds = 20
}

module "deal_room_sqs_task_updated_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-task_updated"
  tags       = local.default_tags
}

module "deal_room_sqs_deal_events_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-deal-events"
  tags       = local.default_tags
}

module "deal_room_sqs_read_excel_file_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-read_excel_file"
  tags       = local.default_tags
}

module "deal_room_sqs_airtable_events_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-airtable_events"
  tags       = local.default_tags
}

module "deal_room_sqs_excel_file_result_prod" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-excel_file_result"
  tags       = local.default_tags
}

module "deal_room_prod_sqs_gpt_file_sent" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-gpt-file-sent"
  tags       = local.default_tags

  delay_seconds = 20
}

module "deal_room_prod_sqs_gpt_file_ready" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-gpt-file-ready"
  tags       = local.default_tags
}

module "sns-prod-deal-room-deal_events" {
  source         = "../../../modules/sns-topic"
  sns_topic_name = "prod-deal_room-deal_events"
  sqs_subscriptions = [
    "arn:aws:sqs:us-east-1:681574592108:prod-deal_room-data_integration_deal_events"
  ]
  tags = local.default_tags
}

module "deal_room_prod_data_integration_deal_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-data_integration_deal_events"
  tags       = local.default_tags
}

module "deal_room_prod_user_events" {
  source     = "../../../modules/sqs-queue"
  queue_name = "prod-deal_room-user_events"

  tags = local.default_tags
}
