module "api" {
  app_name = "enricher"
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 512
    memory = 1024
  }

  private_network = var.environment.private_network
  public_endpoint = false

  port = 8080

  chatbot_arn = var.environment.chatbot_arn

  github_repository  = "unlockre/enricher"
  github_branch_name = "master"
  redeploy_schedule  = "cron(0 11 ? * MON *)" # Every Monday at 11:00 UTC

  codebuild_migration_stage = false

  ecs_variables = {
    # VAR_NAME = "string"
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
    GOOGLE_MAPS_API_KEY = "/dev/enricher/google-maps-api-key",
    REONOMY_API_KEY     = "/dev/enricher/reonomy-api-key",
    AWS_ACCESS_KEY      = "/any/aws/access-key-id",
    AWS_SECRET_KEY      = "/any/aws/secret-access-key",
    DATADOG_API_KEY     = "/dev/enricher/datadog-api-key",
    DATADOG_ENABLED     = "/dev/enricher/datadog-enabled",
    ENV                 = "/prod/global/env",
    SCOPE               = "/any/global/server-scope",

    API_URL = "/dev/global/enricher-api-url",
  }

  tags = local.default_tags
}
