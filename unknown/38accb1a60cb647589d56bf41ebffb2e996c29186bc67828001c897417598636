module "valkey_cache" {
  source = "../../../modules/elasticache-valkey"

  cluster_name    = "${local.app_name}-cache"
  node_type       = "cache.t4g.small"
  engine_version  = "7.2"
  num_cache_nodes = 1

  network = var.environment.private_network

  parameter_group_name = "default.valkey7"
  parameter_prefix = "${local.ecs_parameter_prefix}/valkey-cache"
  port                 = 6379

  tags = local.default_tags
}

