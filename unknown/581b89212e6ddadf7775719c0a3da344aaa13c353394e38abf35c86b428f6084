resource "aws_elasticache_replication_group" "valkey" {
  replication_group_id = var.cluster_name
  description          = "Valkey cluster for ${var.cluster_name}"

  engine               = "valkey"
  node_type            = var.node_type
  num_cache_clusters   = var.num_cache_nodes
  parameter_group_name = var.parameter_group_name
  engine_version       = var.engine_version
  port                 = var.port

  subnet_group_name  = aws_elasticache_subnet_group.valkey.name
  security_group_ids = var.network.security_groups

  tags = var.tags
}

resource "aws_elasticache_subnet_group" "valkey" {
  name       = "${var.cluster_name}-subnet-group"
  subnet_ids = var.network.subnets

  tags = var.tags
}
