variable "cluster_name" {
  type        = string
  description = "Name of the ElastiCache cluster"
}

variable "node_type" {
  type        = string
  description = "The compute and memory capacity of the nodes"
  default     = "cache.t4g.small"
}

variable "num_cache_nodes" {
  type        = number
  description = "The number of cache nodes"
  default     = 1
}

variable "parameter_group_name" {
  type        = string
  description = "Name of the parameter group to associate with this cache cluster"
  default     = "default.valkey7"
}

variable "engine_version" {
  type        = string
  description = "Version number of the engine to be used"
  default     = "7.2"
}

variable "port" {
  type        = number
  description = "The port number on which each of the cache nodes will accept connections"
  default     = 6379
}

variable "network" {
  type = object({
    subnets         = list(string)
    security_groups = list(string)
  })
  description = "Network configuration for the ElastiCache cluster"
}

variable "parameter_prefix" {
  type        = string
  description = "Prefix for SSM Parameter Store parameters. If empty, no parameters will be created."
  default     = ""
}

variable "tags" {
  type        = map(string)
  description = "A map of tags to assign to the resource"
  default     = {}
}

