output "cluster_id" {
  value       = aws_elasticache_replication_group.valkey.id
  description = "The ID of the ElastiCache cluster"
}

output "cluster_address" {
  value       = aws_elasticache_replication_group.valkey.primary_endpoint_address
  description = "The address of the ElastiCache cluster"
}

output "cluster_port" {
  value       = aws_elasticache_replication_group.valkey.port
  description = "The port of the ElastiCache cluster"
}

output "configuration_endpoint" {
  value       = aws_elasticache_replication_group.valkey.configuration_endpoint_address
  description = "The configuration endpoint for this cluster"
}

