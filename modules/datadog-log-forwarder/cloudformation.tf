resource "aws_cloudformation_stack" "datadog_forwarder" {
  name         = var.forwarder_name
  capabilities = ["CAPABILITY_IAM", "CAPABILITY_NAMED_IAM", "CAPABILITY_AUTO_EXPAND"]
  parameters = {
    DdApiKeySecretArn = var.api_key_arn,
    DdSite            = var.dd_site,
    FunctionName      = var.forwarder_name
  }
  template_url = "https://datadog-cloudformation-template.s3.amazonaws.com/aws/forwarder/latest.yaml"
}
