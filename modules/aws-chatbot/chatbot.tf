resource "aws_cloudformation_stack" "chatbot_slack_configuration" {
  name = var.name

  depends_on = [aws_sns_topic.chatbot_sns_topic, aws_iam_role.chatbot_role]

  parameters = {
    ConfigurationNameParameter = "${var.name}-config"
    IamRoleArnParameter        = aws_iam_role.chatbot_role.arn
    LoggingLevelParameter      = "ERROR"
    SlackChannelIdParameter    = var.slack_channel_id
    SlackWorkspaceIdParameter  = var.slack_workspace_id
    SnsTopicArnsParameter      = aws_sns_topic.chatbot_sns_topic.arn
  }

  tags = var.tags

  template_body = <<EOF
AWSTemplateFormatVersion: "2010-09-09"
Description: AWS Chatbot Configuration
Parameters:
  ConfigurationNameParameter:
    Type: String
    Description: The name of the configuration.
  IamRoleArnParameter:
    Type: String
    Description: The ARN of the IAM role that defines the permissions for AWS Chatbot. This is a user-defined role that AWS Chatbot will assume. This is not the service-linked role. For more information, see IAM Policies for AWS Chatbot.
  LoggingLevelParameter:
    Type: String
    Description: Specifies the logging level for this configuration. This property affects the log entries pushed to Amazon CloudWatch Logs. Logging levels include ERROR, INFO, or NONE.
    AllowedValues:
      - ERROR
      - INFO
      - NONE
  SlackChannelIdParameter:
    Type: String
    Description: The ID of the Slack channel. To get the ID, open Slack, right click on the channel name in the left pane, then choose Copy Link. The channel ID is the 9-character string at the end of the URL. For example, ABCBBLZZZ.
  SlackWorkspaceIdParameter:
    Type: String
    Description: The ID of the Slack workspace authorized with AWS Chatbot. To get the workspace ID, you must perform the initial authorization flow with Slack in the AWS Chatbot console. Then you can copy and paste the workspace ID from the console. For more details, see steps 1-4 in Setting Up AWS Chatbot with Slack in the AWS Chatbot User Guide.
  SnsTopicArnsParameter:
    Type: CommaDelimitedList
    Description: The ARNs of the SNS topics that deliver notifications to AWS Chatbot.

Resources:
  SlackChannelConfiguration:
    Type: AWS::Chatbot::SlackChannelConfiguration
    Properties:
      ConfigurationName: !Ref ConfigurationNameParameter
      IamRoleArn: !Ref IamRoleArnParameter
      LoggingLevel: !Ref LoggingLevelParameter
      SlackChannelId: !Ref SlackChannelIdParameter
      SlackWorkspaceId: !Ref SlackWorkspaceIdParameter
      SnsTopicArns: !Ref SnsTopicArnsParameter

Outputs:
  ConfigurationArn:
    Description: The ARN of the Chatbot Slack configuration
    Value: !Ref SlackChannelConfiguration
EOF
}
