resource "aws_sns_topic" "chatbot_sns_topic" {
  name = "${var.name}-sns"
}

resource "aws_sns_topic_policy" "chatbot_policy" {
  arn        = aws_sns_topic.chatbot_sns_topic.arn
  depends_on = [aws_sns_topic.chatbot_sns_topic]
  policy     = data.aws_iam_policy_document.chatbot_sns_topic_policy.json
}


data "aws_iam_policy_document" "chatbot_sns_topic_policy" {
  statement {
    actions = [
      "SNS:Publish"
    ]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceOwner"

      values = [
        data.aws_caller_identity.current.account_id
      ]
    }

    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    resources = [
      aws_sns_topic.chatbot_sns_topic.arn
    ]

    sid = "chatbot_same_account"
  }

  dynamic "statement" {
    for_each = var.publishing_aws_services

    content {
      actions = [
        "SNS:Publish"
      ]

      effect = "Allow"

      principals {
        type        = "Service"
        identifiers = [statement.value]
      }

      resources = [
        aws_sns_topic.chatbot_sns_topic.arn
      ]

      sid = "chatbot_service_${statement.key}"
    }
  }
}
