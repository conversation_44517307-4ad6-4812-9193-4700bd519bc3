resource "aws_iam_role" "chatbot_role" {
  name = "${var.name}-chatbot-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "",
        Effect = "Allow",
        Principal = {
          Service = [
            "chatbot.amazonaws.com"
          ]
        },
        "Action" : "sts:AssumeRole"
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "cloudwatch-readonly-policy-attachment" {
  role       = aws_iam_role.chatbot_role.id
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchReadOnlyAccess"
}

resource "aws_iam_role_policy_attachment" "sns-readonly-policy-attachment" {
  role       = aws_iam_role.chatbot_role.id
  policy_arn = "arn:aws:iam::aws:policy/AmazonSNSReadOnlyAccess"
}
