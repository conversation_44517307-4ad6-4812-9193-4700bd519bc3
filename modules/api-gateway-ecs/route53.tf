resource "aws_route53_record" "www" {
  depends_on = [aws_apigatewayv2_domain_name.this]

  zone_id = var.route53_zone_id
  name    = var.custom_domain
  type    = "A"

  alias {
    name    = aws_apigatewayv2_domain_name.this.domain_name_configuration[0].target_domain_name
    zone_id = aws_apigatewayv2_domain_name.this.domain_name_configuration[0].hosted_zone_id

    evaluate_target_health = false
  }
}
