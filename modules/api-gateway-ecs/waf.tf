resource "aws_wafv2_web_acl" "WafWebAcl" {
  count = var.waf_enabled ? 1 : 0
  name  = "${var.app_name}-wafv2-web-acl"
  scope = "REGIONAL"

  default_action {
    allow {
    }
  }

  custom_response_body {
    key          = "blocked_request_custom_response"
    content      = "{\n    \"error\":\"Too Many Requests.\"\n}"
    content_type = "APPLICATION_JSON"
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "WAF_RateLimit"
    sampled_requests_enabled   = true
  }

  rule {
    name     = "RateLimit"
    priority = 1

    action {
      count {
        custom_request_handling {
          insert_header {
            name  = "X-RateLimit-Limit"
            value = "Exceeded"
          }
        }
      }

      # block {
      #   custom_response {
      #     custom_response_body_key = "blocked_request_custom_response"
      #     response_code            = 429
      #   }
      # }
    }

    statement {
      rate_based_statement {
        aggregate_key_type = "CUSTOM_KEYS"
        limit              = var.waf_ratelimit

        custom_key {
          header {
            name = "Authorization"
            text_transformation {
              priority = 0
              type     = "MD5"
            }
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimit"
      sampled_requests_enabled   = true
    }
  }

  tags = merge(
    var.tags, {
      application = var.app_name,
      customer    = "wafv2-web-acl"
    }
  )
}

resource "aws_wafv2_web_acl_logging_configuration" "WafWebAclLogging" {
  count                   = var.waf_enabled ? 1 : 0
  log_destination_configs = [aws_cloudwatch_log_group.WafWebAclLoggroup[0].arn]
  resource_arn            = aws_wafv2_web_acl.WafWebAcl[0].arn

  logging_filter {
    default_behavior = "DROP"

    filter {
      behavior    = "KEEP"
      requirement = "MEETS_ANY"

      condition {
        action_condition { action = "COUNT" }
      }

      condition {
        action_condition { action = "BLOCK" }
      }
    }
  }

  depends_on = [
    aws_wafv2_web_acl.WafWebAcl[0],
    aws_cloudwatch_log_group.WafWebAclLoggroup[0]
  ]
}

resource "aws_wafv2_web_acl_association" "WafWebAclAssociation" {
  count        = var.waf_enabled ? 1 : 0
  resource_arn = var.lb_arn
  web_acl_arn  = aws_wafv2_web_acl.WafWebAcl[0].arn
  depends_on = [
    aws_wafv2_web_acl.WafWebAcl[0],
    aws_cloudwatch_log_group.WafWebAclLoggroup[0]
  ]
}
