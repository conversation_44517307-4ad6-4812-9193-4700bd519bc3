resource "aws_cloudwatch_log_group" "log_group" {
  count             = (var.setup_access_logs) ? 1 : 0
  name              = "/aws/apigateway/${var.custom_domain}"
  retention_in_days = 365
}

resource "aws_cloudwatch_log_group" "WafWebAclLoggroup" {
  count             = var.waf_enabled ? 1 : 0
  name              = "aws-waf-logs-${var.app_name}"
  retention_in_days = 365
}

resource "aws_cloudwatch_log_group" "lambda_loggroup_auth0" {
  count = var.auth0_settings != null ? 1 : 0
  # the name is the same as aws_lambda_function.jwt_authorizer_auth0
  # the reference can't be used since it will cause redundancy
  name              = "/aws/lambda/${var.app_name}-authorizer-auth0"
  retention_in_days = 365
}

resource "aws_cloudwatch_log_group" "lambda_loggroup" {
  count = var.jwt_parameter_path != "" ? 1 : 0

  # the name is the same as aws_lambda_function.jwt_authorizer_auth0
  # the reference can't be used since it will cause redundancy
  name              = "/aws/lambda/${var.app_name}-authorizer-custom"
  retention_in_days = 365
}
