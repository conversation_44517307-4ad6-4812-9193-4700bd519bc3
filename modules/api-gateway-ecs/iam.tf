resource "aws_iam_role" "jwt_authorizer" {
  name = "${var.app_name}-jwt"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Effect = "Allow",
        Sid    = ""
      }
    ]
  })
}

resource "aws_iam_policy" "jwt_authorizer" {
  name        = "${var.app_name}-jwt"
  path        = "/"
  description = "IAM policy for AWS Lambda: ${var.app_name}-authorizer"

  policy = jsonencode(
    {
      Version = "2012-10-17",
      Statement = [
        {
          Action = [
            "logs:CreateLogGroup",
            "logs:CreateLogStream",
            "logs:PutLogEvents"
          ],
          Resource = "arn:aws:logs:*:*:*",
          Effect   = "Allow"
        }
      ]
    }
  )
}

resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.jwt_authorizer.name
  policy_arn = aws_iam_policy.jwt_authorizer.arn
}

resource "aws_iam_role_policy_attachment" "iam_role_policy_attachment_lambda_vpc_access_execution" {
  role       = aws_iam_role.jwt_authorizer.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy_attachment" "iam_role_policy_attachment_lambda_basic_execution" {
  role       = aws_iam_role.jwt_authorizer.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role" "apig_lambda_role" {
  name = "${var.app_name}-apigw"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Principal = {
          Service = "apigateway.amazonaws.com"
        },
        Effect = "Allow",
        Sid    = ""
      }
    ]
  })
}

resource "aws_iam_policy" "apig_lambda" {
  name       = "${var.app_name}-apigw"
  depends_on = [aws_lambda_function.jwt_authorizer[0]]
  count      = length(aws_lambda_function.jwt_authorizer)
  policy = jsonencode(
    {
      Version = "2012-10-17",
      Statement = [
        {
          Action = [
            "lambda:InvokeFunction"
          ],
          Resource = [
            "${aws_lambda_function.jwt_authorizer[count.index].arn}",
            "${aws_lambda_function.jwt_authorizer[count.index].arn}:$LATEST",
            "${aws_lambda_function.jwt_authorizer[count.index].arn}:*",
            "${aws_lambda_function.jwt_authorizer[count.index].arn}:*/*/*",
            "${aws_lambda_alias.custom_alias[0].arn}",
            "${aws_lambda_alias.custom_alias[0].arn}:$LATEST",
            "${aws_lambda_alias.custom_alias[0].arn}:*",
            "${aws_lambda_alias.custom_alias[0].arn}:*/*/*"
          ],
          Effect = "Allow"
        }
      ]
    }
  )
}

resource "aws_iam_role_policy_attachment" "apig_lambda_role_to_policy" {
  count      = length(aws_lambda_function.jwt_authorizer)
  role       = aws_iam_role.apig_lambda_role.name
  policy_arn = aws_iam_policy.apig_lambda[count.index].arn
}

resource "aws_iam_policy" "apig_lambda_auth0" {
  name       = "${var.app_name}-apigw-auth0"
  depends_on = [aws_lambda_function.jwt_authorizer_auth0[0]]
  count      = length(aws_lambda_function.jwt_authorizer_auth0)
  policy = jsonencode(
    {
      Version = "2012-10-17",
      Statement = [
        {
          Action = [
            "lambda:InvokeFunction"
          ],
          Resource = [
            "${aws_lambda_function.jwt_authorizer_auth0[count.index].arn}",
            "${aws_lambda_function.jwt_authorizer_auth0[count.index].arn}:$LATEST",
            "${aws_lambda_function.jwt_authorizer_auth0[count.index].arn}:*",
            "${aws_lambda_function.jwt_authorizer_auth0[count.index].arn}:*/*/*",
            "${aws_lambda_alias.auth0_alias[0].arn}",
            "${aws_lambda_alias.auth0_alias[0].arn}:$LATEST",
            "${aws_lambda_alias.auth0_alias[0].arn}:*",
            "${aws_lambda_alias.auth0_alias[0].arn}:*/*/*"
          ]
          Effect = "Allow"
        }
      ]
    }
  )
}

resource "aws_iam_role_policy_attachment" "apig_lambda_role_to_policy_auth0" {
  count      = length(aws_lambda_function.jwt_authorizer_auth0)
  role       = aws_iam_role.apig_lambda_role.name
  policy_arn = aws_iam_policy.apig_lambda_auth0[count.index].arn
}
