resource "null_resource" "auth_check" {
  lifecycle {
    precondition {
      condition     = !contains(values(var.route_auth_map), "CUSTOM") || length(var.jwt_parameter_path) > 5
      error_message = "At least one route_auth_map value is set to CUSTOM, but no jwt_parameter_path was provided (min length 5)."
    }

    precondition {
      condition     = !contains(values(var.route_auth_map), "AUTH0") || var.auth0_settings != null
      error_message = "At least one route_auth_map value is set to AUTH0, and auth0_settings is not set or invalid (expected: { JWKS_URI=\"https://{domain}/.well-known/jwks.json\", AUDIENCE=\"https://{api-gateway}\", TOKEN_ISSUER=\"https://domain/\" })."
    }
  }
}
