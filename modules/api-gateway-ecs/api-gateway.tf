resource "aws_apigatewayv2_api" "this" {
  name          = "${var.app_name}-api"
  protocol_type = "HTTP"
  description   = "Created by Terraform for ${var.app_name}"

  disable_execute_api_endpoint = true

  cors_configuration {
    allow_origins = var.cors_origins
    allow_methods = var.cors_methods
    allow_headers = var.cors_headers
  }

  tags = var.tags
}

resource "aws_apigatewayv2_domain_name" "this" {
  domain_name = var.custom_domain

  domain_name_configuration {
    certificate_arn = var.acm_ssl_certificate
    endpoint_type   = "REGIONAL"
    security_policy = "TLS_1_2"
  }
}

resource "aws_apigatewayv2_api_mapping" "example" {
  api_id      = aws_apigatewayv2_api.this.id
  domain_name = aws_apigatewayv2_domain_name.this.id
  stage       = aws_apigatewayv2_stage.this.id
}

resource "aws_apigatewayv2_integration" "this" {
  api_id           = aws_apigatewayv2_api.this.id
  integration_type = "HTTP_PROXY"
  integration_uri  = var.skip_vpc_link ? "https://${var.lb_cname}" : var.lb_listener_arn

  integration_method = "ANY"
  connection_type    = var.skip_vpc_link ? "INTERNET" : "VPC_LINK"
  connection_id      = var.skip_vpc_link ? null : var.network.vpc_link

  tls_config {
    server_name_to_verify = var.tls_domain
  }
}


resource "aws_apigatewayv2_route" "route" {
  depends_on = [
    aws_apigatewayv2_integration.this,
    aws_apigatewayv2_authorizer.auth0,
    aws_apigatewayv2_authorizer.custom
  ]

  for_each  = var.route_auth_map
  route_key = each.key

  api_id = aws_apigatewayv2_api.this.id
  target = "integrations/${aws_apigatewayv2_integration.this.id}"

  authorization_type = each.value == "NONE" ? "NONE" : "CUSTOM"
  authorizer_id      = each.value == "NONE" ? null : each.value == "AUTH0" ? aws_apigatewayv2_authorizer.auth0[0].id : aws_apigatewayv2_authorizer.custom[0].id
}

resource "aws_apigatewayv2_stage" "this" {
  api_id      = aws_apigatewayv2_api.this.id
  description = "LIVE (Default stage)"
  name        = "live"
  auto_deploy = true
  tags        = var.tags

  dynamic "access_log_settings" {
    for_each = (var.setup_access_logs) ? ["enabled"] : []
    content {
      destination_arn = aws_cloudwatch_log_group.log_group[0].arn
      format = jsonencode({
        "requestId"      = "$context.requestId",
        "ip"             = "$context.identity.sourceIp",
        "requestTime"    = "$context.requestTime",
        "httpMethod"     = "$context.httpMethod",
        "routeKey"       = "$context.routeKey",
        "status"         = "$context.status",
        "protocol"       = "$context.protocol",
        "responseLength" = "$context.responseLength"
      })

    }
  }
}
