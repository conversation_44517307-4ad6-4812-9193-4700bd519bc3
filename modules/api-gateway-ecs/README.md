<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 app_name  = 
	 custom_domain  = 
	 jwt_parameter_path  = 
	 lb_listener_arn  = 
	 network  = 

	 # Optional variables
	 acm_ssl_certificate  = "arn:aws:acm:us-east-1:681574592108:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
	 cors_headers  = [
  "Content-Type",
  "Authorization",
  "X-Amz-Date",
  "X-Api-Key",
  "X-Amz-Security-Token"
]
	 cors_methods  = [
  "GET",
  "HEAD",
  "PUT",
  "PATCH",
  "POST",
  "DELETE",
  "OPTIONS"
]
	 cors_origins  = [
  "*"
]
	 route53_zone_id  = "Z05531162DGNWUXR3CVOO"
	 route_keys  = [
  "$default"
]
	 tags  = {}
}
```
## Resources

| Name | Type |
|------|------|
| [aws_apigatewayv2_api.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_api) | resource |
| [aws_apigatewayv2_api_mapping.example](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_api_mapping) | resource |
| [aws_apigatewayv2_authorizer.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_authorizer) | resource |
| [aws_apigatewayv2_domain_name.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_domain_name) | resource |
| [aws_apigatewayv2_integration.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_integration) | resource |
| [aws_apigatewayv2_route.route](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_route) | resource |
| [aws_apigatewayv2_stage.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/apigatewayv2_stage) | resource |
| [aws_iam_policy.apig_lambda](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.jwt_authorizer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.apig_lambda_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.jwt_authorizer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.apig_lambda_role_to_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.iam_role_policy_attachment_lambda_basic_execution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.iam_role_policy_attachment_lambda_vpc_access_execution](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.lambda_logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_lambda_function.jwt_authorizer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function) | resource |
| [aws_lambda_layer_version.lambda_layer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_layer_version) | resource |
| [aws_lambda_permission.lambda-permission](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_permission) | resource |
| [aws_route53_record.www](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_ssm_parameter.jwt_parameter_store_path](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ssm_parameter) | resource |
| [random_password.jwt_secret](https://registry.terraform.io/providers/hashicorp/random/latest/docs/resources/password) | resource |
| [archive_file.jwt_authorizer](https://registry.terraform.io/providers/hashicorp/archive/latest/docs/data-sources/file) | data source |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_acm_ssl_certificate"></a> [acm\_ssl\_certificate](#input\_acm\_ssl\_certificate) | The ACM SSL certificate to use for the custom domain. By default is *.whykeyway.com | `string` | `"arn:aws:acm:us-east-1:681574592108:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"` | no |
| <a name="input_app_name"></a> [app\_name](#input\_app\_name) | The application name. Will be used as a prefix for all resources. | `string` | n/a | yes |
| <a name="input_cors_headers"></a> [cors\_headers](#input\_cors\_headers) | The CORS headers to allow. By default it contains a basic list. | `list(string)` | <pre>[<br>  "Content-Type",<br>  "Authorization",<br>  "X-Amz-Date",<br>  "X-Api-Key",<br>  "X-Amz-Security-Token"<br>]</pre> | no |
| <a name="input_cors_methods"></a> [cors\_methods](#input\_cors\_methods) | The CORS methods to allow. By default its a catch-all. | `list(string)` | <pre>[<br>  "GET",<br>  "HEAD",<br>  "PUT",<br>  "PATCH",<br>  "POST",<br>  "DELETE",<br>  "OPTIONS"<br>]</pre> | no |
| <a name="input_cors_origins"></a> [cors\_origins](#input\_cors\_origins) | The CORS origins to allow. By default its a catch-all. | `list(string)` | <pre>[<br>  "*"<br>]</pre> | no |
| <a name="input_custom_domain"></a> [custom\_domain](#input\_custom\_domain) | The custom domain to use for the API Gateway. An example is app-gw.dev.whykeyway.com | `string` | n/a | yes |
| <a name="input_jwt_parameter_path"></a> [jwt\_parameter\_path](#input\_jwt\_parameter\_path) | The Parameter Store path to the secret value where the JWT token will be saved. | `string` | n/a | yes |
| <a name="input_lb_listener_arn"></a> [lb\_listener\_arn](#input\_lb\_listener\_arn) | ARN of the ALB listener to which the API Gateway will be connected | `string` | n/a | yes |
| <a name="input_network"></a> [network](#input\_network) | The network configuration for the VPC | <pre>object({<br>    vpc             = string<br>    cidr_block      = string<br>    subnets         = list(string)<br>    route_tables    = list(string)<br>    security_groups = list(string)<br>    network_acls    = list(string)<br>    vpc_link        = string<br>  })</pre> | n/a | yes |
| <a name="input_route53_zone_id"></a> [route53\_zone\_id](#input\_route53\_zone\_id) | The Route53 zone ID for the custom domain. By default is whykeyway.com | `string` | `"Z05531162DGNWUXR3CVOO"` | no |
| <a name="input_route_keys"></a> [route\_keys](#input\_route\_keys) | The route keys for the API Gateway. By default its a catch-all. Here are some examples: https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-develop-routes.html | `list(string)` | <pre>[<br>  "$default"<br>]</pre> | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Map of tags to apply to all resources. | `map(string)` | `{}` | no |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_api_gateway_invoke"></a> [api\_gateway\_invoke](#output\_api\_gateway\_invoke) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->
## Footer
