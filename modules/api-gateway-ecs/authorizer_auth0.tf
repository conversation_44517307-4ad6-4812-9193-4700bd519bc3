resource "aws_apigatewayv2_authorizer" "auth0" {
  count            = var.auth0_settings != null ? 1 : 0
  api_id           = aws_apigatewayv2_api.this.id
  authorizer_type  = "REQUEST"
  authorizer_uri   = aws_lambda_alias.auth0_alias[0].invoke_arn
  identity_sources = ["$request.header.Authorization"]
  name             = "${var.app_name}-jwt-authorizer-auth0"

  authorizer_payload_format_version = "2.0"
  authorizer_result_ttl_in_seconds  = 0
  authorizer_credentials_arn        = aws_iam_role.apig_lambda_role.arn
}

resource "aws_lambda_function" "jwt_authorizer_auth0" {
  count            = var.auth0_settings != null ? 1 : 0
  depends_on       = [aws_cloudwatch_log_group.lambda_loggroup_auth0[0]]
  function_name    = "${var.app_name}-authorizer-auth0"
  filename         = "${path.module}/auth0/lambda.zip"
  source_code_hash = filebase64sha256("${path.module}/auth0/lambda.zip")
  role             = aws_iam_role.jwt_authorizer.arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  layers           = [aws_lambda_layer_version.lambda_layer_auth0[0].arn]

  environment {
    variables = {
      JWKS_URI     = var.auth0_settings.JWKS_URI
      AUDIENCE     = var.auth0_settings.AUDIENCE
      TOKEN_ISSUER = var.auth0_settings.TOKEN_ISSUER
    }
  }

  tags = var.tags
}

resource "aws_lambda_layer_version" "lambda_layer_auth0" {
  count            = var.auth0_settings != null ? 1 : 0
  layer_name       = "${var.app_name}-authorizer-auth0-layer"
  filename         = "${path.module}/auth0/layer.zip"
  source_code_hash = filebase64sha256("${path.module}/auth0/layer.zip")

  compatible_runtimes = ["nodejs18.x"]
}

resource "aws_lambda_alias" "auth0_alias" {
  count            = var.auth0_settings != null ? 1 : 0
  name             = "${var.app_name}-alias-auth0"
  description      = "Lambda alias for ${var.app_name}-authorizer-auth0"
  function_name    = aws_lambda_function.jwt_authorizer_auth0[0].function_name
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "lambda-permission_auth0" {
  count = var.auth0_settings != null ? 1 : 0
  depends_on = [
    aws_apigatewayv2_api.this,
    aws_lambda_function.jwt_authorizer_auth0[0]
  ]

  statement_id  = "AllowExecutionFromApiGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer_auth0[0].function_name
  principal     = "apigateway.amazonaws.com"

  # The /*/*/* part allows invocation from any stage, method and resource path
  source_arn = "${aws_apigatewayv2_api.this.execution_arn}/*/*"
  qualifier  = aws_lambda_alias.auth0_alias[0].name
}