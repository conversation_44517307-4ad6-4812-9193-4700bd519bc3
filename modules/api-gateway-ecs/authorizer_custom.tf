resource "aws_apigatewayv2_authorizer" "custom" {
  count = var.jwt_parameter_path != "" ? 1 : 0

  api_id           = aws_apigatewayv2_api.this.id
  authorizer_type  = "REQUEST"
  authorizer_uri   = aws_lambda_alias.custom_alias[0].invoke_arn
  identity_sources = ["$request.header.Authorization"]
  name             = "${var.app_name}-jwt-authorizer"

  authorizer_payload_format_version = "2.0"
  authorizer_result_ttl_in_seconds  = 0
  authorizer_credentials_arn        = aws_iam_role.apig_lambda_role.arn
}

resource "aws_lambda_function" "jwt_authorizer" {
  count = var.jwt_parameter_path != "" ? 1 : 0

  depends_on       = [aws_cloudwatch_log_group.lambda_loggroup]
  function_name    = "${var.app_name}-authorizer-custom"
  filename         = "${path.module}/custom/lambda.zip"
  source_code_hash = filebase64sha256("${path.module}/custom/lambda.zip")
  role             = aws_iam_role.jwt_authorizer.arn
  handler          = "authorize.handler"
  runtime          = "nodejs18.x"
  layers           = [aws_lambda_layer_version.lambda_layer[0].arn]

  environment {
    variables = {
      JWT_SECRET = random_password.jwt_secret.result
    }
  }

  lifecycle {
    ignore_changes = [environment[0].variables.JWT_SECRET]
  }

  tags = var.tags
}

resource "aws_lambda_alias" "custom_alias" {
  count            = var.jwt_parameter_path != "" ? 1 : 0
  name             = "${var.app_name}-alias-custom"
  description      = "Lambda alias for ${var.app_name}-authorizer-custom"
  function_name    = aws_lambda_function.jwt_authorizer[0].function_name
  function_version = "$LATEST"
}

resource "aws_lambda_layer_version" "lambda_layer" {
  count = var.jwt_parameter_path != "" ? 1 : 0

  layer_name       = "${var.app_name}-authorizer-custom-layer"
  filename         = "${path.module}/custom/layer.zip"
  source_code_hash = filebase64sha256("${path.module}/custom/layer.zip")

  compatible_runtimes = ["nodejs18.x"]
}

resource "aws_lambda_permission" "lambda-permission" {
  count = var.jwt_parameter_path != "" ? 1 : 0

  depends_on = [
    aws_apigatewayv2_api.this,
    aws_lambda_function.jwt_authorizer[0]
  ]

  statement_id  = "AllowExecutionFromApiGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer[0].function_name
  principal     = "apigateway.amazonaws.com"

  # The /*/*/* part allows invocation from any stage, method and resource path
  source_arn = "${aws_apigatewayv2_api.this.execution_arn}/*/*"
  qualifier  = aws_lambda_alias.custom_alias[0].name
}
