resource "aws_cloudwatch_metric_alarm" "queue_scale" {
  # TODO: Set "scale_on" variable to specify which method to use
  count = local.scale_by_queue == true ? 1 : 0

  alarm_name          = "ECSQueueScaleTasks/${var.app_name}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  treat_missing_data  = "ignore"
  evaluation_periods  = 1
  threshold           = var.threshold

  metric_query {
    id = "total"
    expression = length(var.queues) > 1 ? join(
      " + ",
      [for queue in var.queues : "metric_${replace(queue, "-", "_")}"]
    ) : "m0"
    label       = "TotalMessagesVisible"
    return_data = true
  }

  dynamic "metric_query" {
    for_each = var.queues
    content {
      id = "metric_${replace(metric_query.value, "-", "_")}"

      metric {
        metric_name = "ApproximateNumberOfMessagesVisible"
        namespace   = "AWS/SQS"
        period      = 10
        stat        = "Average"

        dimensions = {
          QueueName = metric_query.value
        }
      }

      return_data = false
    }
  }

  alarm_actions = [aws_appautoscaling_policy.queue_scale_up[0].arn]
  ok_actions    = var.run_on_empty_queue ? [] : [aws_appautoscaling_policy.queue_scale_down[0].arn]
}
