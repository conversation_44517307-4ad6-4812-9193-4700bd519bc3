resource "aws_appautoscaling_target" "ecs" {
  max_capacity       = var.scaling_method == "queue" ? local.max_queue_capacity : local.max_schedule_capacity
  min_capacity       = var.scaling_method == "queue" ? local.min_queue_capacity : local.min_schedule_capacity
  resource_id        = var.service_resource_id
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
  lifecycle {
    ignore_changes = [max_capacity, min_capacity]
  }
}
