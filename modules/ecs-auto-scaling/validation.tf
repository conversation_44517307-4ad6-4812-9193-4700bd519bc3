resource "null_resource" "validate_scaling_method" {
  lifecycle {
    precondition {
      condition = (
        (var.scaling_method == "queue" && length(var.escalation_steps) > 0 && length(var.scheduled_actions) == 0) ||
        (var.scaling_method == "schedule" && length(var.scheduled_actions) > 0 && length(var.escalation_steps) == 0)
      )
      error_message = <<EOT
Invalid configuration:
- If scaling_method is "queue", then 'escalation_steps' must be set and 'scheduled_actions' must be empty.
- If scaling_method is "schedule", then 'scheduled_actions' must be set and 'escalation_steps' must be empty.
EOT
    }
  }

  triggers = {
    validation = "Invalid combination of scaling_method, escalation_steps, and scheduled_actions"
  }
}
