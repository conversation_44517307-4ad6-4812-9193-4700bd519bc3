locals {
  scale_by_schedule     = var.scaling_method == "schedule" && length(var.scheduled_actions) > 0 ? true : false
  min_schedule_capacity = local.scale_by_schedule ? length(var.scheduled_actions) > 0 ? sort(var.scheduled_actions[*].max_capacity)[0] : 0 : 0
  max_schedule_capacity = local.scale_by_schedule ? sort(var.scheduled_actions[*].max_capacity)[length(var.scheduled_actions) - 1] : 0

  scale_by_queue         = var.scaling_method == "queue" && length(var.escalation_steps) > 0 ? true : false
  min_queue_capacity     = local.scale_by_queue ? var.run_on_empty_queue != true ? 0 : sort(var.escalation_steps[*].desired_replicas)[0] : 0
  max_queue_capacity     = local.scale_by_queue ? sort(var.escalation_steps[*].desired_replicas)[length(var.escalation_steps) - 1] : 0
  min_scaling_adjustment = local.scale_by_queue ? var.run_on_empty_queue ? sort(var.escalation_steps[*].desired_replicas)[0] : 0 : 0
}
