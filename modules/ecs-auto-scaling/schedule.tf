resource "aws_appautoscaling_scheduled_action" "scheduled_actions" {
  count = local.scale_by_schedule == true ? length(var.scheduled_actions) : 0

  depends_on         = [aws_appautoscaling_target.ecs]
  name               = "ecs-${var.app_name}-sa-${count.index}"
  service_namespace  = aws_appautoscaling_target.ecs.service_namespace
  resource_id        = var.service_resource_id
  scalable_dimension = aws_appautoscaling_target.ecs.scalable_dimension
  schedule           = "cron(${var.scheduled_actions[count.index].cron})"
  timezone           = "UTC"

  scalable_target_action {
    min_capacity = var.scheduled_actions[count.index].min_capacity
    max_capacity = var.scheduled_actions[count.index].max_capacity
  }
}
