# Define Scaling Policies dynamically based on sorted steps
resource "aws_appautoscaling_policy" "queue_scale_up" {
  count = local.scale_by_queue == true ? 1 : 0

  name               = "${var.app_name}-queue-scale-up"
  service_namespace  = "ecs"
  resource_id        = var.service_resource_id
  scalable_dimension = "ecs:service:DesiredCount"
  policy_type        = "StepScaling"

  step_scaling_policy_configuration {
    adjustment_type         = "ExactCapacity"
    metric_aggregation_type = "Minimum"
    cooldown                = 10

    # Add remaining steps from queue_escalation
    dynamic "step_adjustment" {
      for_each = var.escalation_steps
      iterator = i
      content {
        scaling_adjustment          = i.value.desired_replicas
        metric_interval_lower_bound = try(i.value.lower_bound - var.threshold, null)
        metric_interval_upper_bound = try(i.value.upper_bound - var.threshold, null)
      }
    }
  }
}

# Policy to scale down to 0 tasks when queue is empty
resource "aws_appautoscaling_policy" "queue_scale_down" {
  count = local.scale_by_queue == true ? 1 : 0

  name               = "${var.app_name}-queue-scale-down"
  service_namespace  = "ecs"
  resource_id        = var.service_resource_id
  scalable_dimension = "ecs:service:DesiredCount"
  policy_type        = "StepScaling"

  step_scaling_policy_configuration {
    adjustment_type         = "ExactCapacity"
    metric_aggregation_type = "Minimum"
    cooldown                = 10

    step_adjustment {
      scaling_adjustment          = local.min_scaling_adjustment
      metric_interval_upper_bound = 0
    }
  }
}
