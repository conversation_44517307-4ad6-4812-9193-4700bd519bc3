variable "app_name" {
  type = string
}
variable "scaling_method" {
  type = string

  validation {
    condition     = var.scaling_method == "queue" || var.scaling_method == "schedule"
    error_message = "scaling_method must be either 'queue' or 'schedule'"
  }
}

variable "service_resource_id" {
  type = string
}

variable "scheduled_actions" {
  type = list(object({
    cron         = string
    min_capacity = number
    max_capacity = number
  }))
  default = []
}

variable "queues" {
  type        = list(string)
  default     = []
  description = <<-EOT
  Which queues the metric that triggers the escalation is based on.
  Final metric will be a sum of all "ApproximateNumberOfMessagesVisible" values.
  EOT
}

variable "threshold" {
  type        = number
  default     = 1
  description = <<-EOT
  When number of messages in queues reaches threshold, escalation will be triggered.
  EOT
}

variable "run_on_empty_queue" {
  type        = bool
  default     = true
  description = <<-EOT
  When false, number of tasks will drop to 0 when threshold isn't met.
  Otherwise, number of tasks will lower to lowest specified "desired_replicas"
  in "escalation_steps" variable.
  EOT
}

variable "escalation_steps" {
  type = list(object({
    desired_replicas = number
    lower_bound      = optional(number, null)
    upper_bound      = optional(number, null)
  }))
  default     = []
  description = <<-EOT
  Tasks will begin to scale when the "threshold" variable is reached by the queue;
  from that point on, the policy will measure the threshold of the next step by 
  adding the number of messages in the "upper_bound" attribute of each following step to the base threshold value.
  EOT
}

variable "tags" {
  type    = map(string)
  default = {}
}
