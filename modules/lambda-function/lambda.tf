resource "aws_lambda_function" "function" {
  depends_on = [aws_cloudwatch_log_group.lambda_loggroup_auth0]

  function_name    = var.name
  filename         = var.source_code_zip
  source_code_hash = filebase64sha256("${var.source_code_zip}")

  handler = var.handler
  runtime = var.runtime
  layers  = var.lambda_layers
  role    = aws_iam_role.function_role.arn

  timeout = var.timeout

  environment {
    variables = var.environment_vars
  }

  tags = var.tags
}

resource "aws_lambda_alias" "function_alias" {
  name             = "${var.name}-alias"
  description      = "Lambda alias for ${var.name}"
  function_name    = aws_lambda_function.function.function_name
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "allow_cloudfront" {
  statement_id  = "AllowExecutionFromCloudFront"
  action        = "lambda:InvokeFunctionUrl"
  function_name = aws_lambda_function.function.function_name
  principal     = "edgelambda.amazonaws.com"
  source_arn    = "arn:aws:cloudfront::${data.aws_caller_identity.current.account_id}:distribution/*"
}

resource "aws_lambda_function_url" "function_url" {
  count              = var.create_url ? 1 : 0
  function_name      = aws_lambda_function.function.function_name
  authorization_type = "NONE"

  cors {
    allow_credentials = false
    allow_origins     = ["*"]
    allow_methods     = ["*"]
    allow_headers     = concat(["date", "keep-alive"], var.custom_headers)
    expose_headers    = ["keep-alive", "date"]
    max_age           = 86400
  }
}
