variable "name" {
  type        = string
  description = "The name of the application. Will be used as a prefix for all resources."
}

variable "source_code_zip" {
  type        = string
  description = "The path to the zip file containing the source code for the Lambda function."
}

variable "handler" {
  type        = string
  description = "The name of the function within your code that Lambda calls to begin execution."
  default     = "index.handler"
}

variable "runtime" {
  type        = string
  description = "The runtime to use for the Lambda function."
  default     = "nodejs18.x"
}

variable "lambda_layers" {
  type        = list(string)
  description = "A list of ARNs of Lambda layers to attach to the function."
  default     = []
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "extra_iam_statements" {
  type        = list(map(string))
  description = "A list of IAM policy statements to attach to the Lambda function's role."
  default     = []
}

variable "create_url" {
  type        = bool
  description = "Whether to create a URL for the Lambda function."
  default     = false
}

variable "environment_vars" {
  type        = map(string)
  description = "A map of environment variables to set for the Lambda function."
  default     = {}
}

variable "custom_headers" {
  type    = list(string)
  default = []
}

variable "timeout" {
  type    = number
  default = 3
}
