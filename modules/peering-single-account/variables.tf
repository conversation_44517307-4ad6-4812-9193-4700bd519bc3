# example origin: { name: "some-vpc", vpc_id: vpc-xxxxx, route_table_ids: [rtb-xxxxxxxxx, rtb-yyyyyyyyy], cidr: 0.0.0.0/0 }
variable "origin" {
  type = object({
    name            = string
    vpc_id          = string
    route_table_ids = list(string)
    cidr            = string
  })
}

# example remote: { name: "other-vpc", vpc_id: vpc-xxxxx, route_table_ids: [rtb-xxxxxxxxx, rtb-yyyyyyyyy], cidr: 0.0.0.0/0 }
variable "remote" {
  type = object({
    name            = string
    vpc_id          = string
    route_table_ids = list(string)
    cidr            = string
  })
}
