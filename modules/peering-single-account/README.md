<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 origin  = 
	 remote  = 
}
```
## Resources

| Name | Type |
|------|------|
| [aws_route.peering_route_origin](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
| [aws_route.peering_route_remote](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
| [aws_vpc_peering_connection.peering_connection](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_peering_connection) | resource |
| [aws_vpc_peering_connection_options.peering_options](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc_peering_connection_options) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_origin"></a> [origin](#input\_origin) | example origin: { name: "some-vpc", vpc\_id: vpc-xxxxx, route\_table\_ids: [rtb-xxxxxxxxx, rtb-yyyyyyyyy], cidr: 0.0.0.0/0 } | <pre>object({<br>    name            = string<br>    vpc_id          = string<br>    route_table_ids = list(string)<br>    cidr            = string<br>  })</pre> | n/a | yes |
| <a name="input_remote"></a> [remote](#input\_remote) | example remote: { name: "other-vpc", vpc\_id: vpc-xxxxx, route\_table\_ids: [rtb-xxxxxxxxx, rtb-yyyyyyyyy], cidr: 0.0.0.0/0 } | <pre>object({<br>    name            = string<br>    vpc_id          = string<br>    route_table_ids = list(string)<br>    cidr            = string<br>  })</pre> | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_peering_id"></a> [peering\_id](#output\_peering\_id) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->