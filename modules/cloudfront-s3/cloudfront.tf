resource "aws_cloudfront_origin_access_control" "acl" {
  name                              = "${local.s3_origin_id}-acl"
  description                       = "Connects ${var.s3_bucket_name} bucket with ${var.cloudfront_domain}"
  origin_access_control_origin_type = "s3"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
}

resource "aws_cloudfront_distribution" "s3_distribution" {
  depends_on = [
    aws_s3_bucket.bucket,
    aws_cloudfront_cache_policy.cp,
    aws_cloudfront_response_headers_policy.response_policy
  ]

  dynamic "origin_group" {
    for_each = var.origin_failover != "" ? ["failover"] : []
    content {
      origin_id = "${local.s3_origin_id}-group"

      failover_criteria {
        status_codes = [403, 404, 500, 503, 504]
      }

      member {
        origin_id = local.s3_origin_id
      }

      member {
        origin_id = "${local.s3_origin_id}-failover"
      }
    }
  }

  origin {
    domain_name              = aws_s3_bucket.bucket.bucket_regional_domain_name
    origin_access_control_id = aws_cloudfront_origin_access_control.acl.id
    origin_id                = local.s3_origin_id

    dynamic "origin_shield" {
      for_each = var.origin_shield_regions
      content {
        enabled              = true
        origin_shield_region = origin_shield.value
      }
    }
  }

  dynamic "origin" {
    for_each = var.origin_failover != "" ? ["failover"] : []
    content {
      domain_name = var.origin_failover
      origin_id   = "${local.s3_origin_id}-failover"

      dynamic "custom_origin_config" {
        for_each = strcontains(var.origin_failover, "s3") ? [] : ["custom"]
        content {
          http_port                = 80
          https_port               = 443
          origin_protocol_policy   = "https-only"
          origin_ssl_protocols     = ["TLSv1.2"]
          origin_read_timeout      = 60
          origin_keepalive_timeout = 10
        }
      }

      dynamic "custom_header" {
        for_each = var.origin_failover_headers
        content {
          name  = custom_header.key
          value = custom_header.value
        }
      }

      dynamic "origin_shield" {
        for_each = var.origin_shield_regions
        content {
          enabled              = true
          origin_shield_region = origin_shield.value
        }
      }
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = var.cloudfront_domain
  default_root_object = "index.html"

  #  logging_config {
  #    include_cookies = false
  #    bucket          = "mylogs.s3.amazonaws.com"
  #    prefix          = "myprefix"
  #  }

  aliases = [var.cloudfront_domain]

  default_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS"]
    cached_methods             = ["GET", "HEAD"]
    cache_policy_id            = aws_cloudfront_cache_policy.cp.id
    target_origin_id           = var.origin_failover != "" ? "${local.s3_origin_id}-group" : local.s3_origin_id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.response_policy.id

    dynamic "lambda_function_association" {
      for_each = var.auth0_protection ? [1] : []
      content {
        event_type   = "viewer-request"
        lambda_arn   = aws_lambda_function.jwt_authorizer[0].qualified_arn
        include_body = false
      }
    }

    dynamic "lambda_function_association" {
      for_each = var.lambda_edge_functions
      content {
        event_type   = "viewer-request"
        lambda_arn   = lambda_function_association.value
        include_body = false
      }
    }

    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = var.price_class

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = merge(var.tags, {
    Name = var.cloudfront_domain
  })

  viewer_certificate {
    acm_certificate_arn = var.ssl_certificate_arn
    ssl_support_method  = "sni-only"
  }
}

resource "aws_cloudfront_response_headers_policy" "response_policy" {
  name    = "${local.s3_origin_id}-response-policy"
  comment = "Response Headers Policy for ${var.cloudfront_domain}"

  cors_config {
    access_control_allow_credentials = var.auth0_protection

    access_control_allow_headers {
      items = ["Authorization", "Origin", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Access-Control-Allow-Origin", "Content-Type"]
    }

    access_control_expose_headers {
      items = ["ETag", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Access-Control-Allow-Methods"]
    }

    access_control_allow_methods {
      items = ["GET", "HEAD", "OPTIONS"]
    }

    access_control_allow_origins {
      items = var.cors_allowed_origins
    }

    origin_override = true
  }

  custom_headers_config {
    items {
      header   = "x-source"
      value    = "external"
      override = true
    }

    dynamic "items" {
      for_each = var.response_custom_headers
      content {
        header   = items.key
        value    = items.value
        override = true
      }
    }
  }

  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = var.max_ttl
      include_subdomains         = true
      preload                    = true
      override                   = false
    }

    content_type_options {
      override = false
    }

    xss_protection {
      protection = true
      mode_block = true
      override   = false
    }

    referrer_policy {
      referrer_policy = "same-origin"
      override        = false
    }
  }
}

resource "aws_cloudfront_cache_policy" "cp" {
  name        = "${local.s3_origin_id}-cp"
  comment     = "Cache Policy for ${var.cloudfront_domain}"
  min_ttl     = var.min_ttl
  default_ttl = var.default_ttl
  max_ttl     = var.max_ttl

  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "none"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["Origin", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Access-Control-Allow-Origin"]
      }
    }
    query_strings_config {
      query_string_behavior = length(var.query_string_whitelist) > 0 ? "whitelist" : "none"
      dynamic "query_strings" {
        for_each = length(var.query_string_whitelist) > 0 ? [1] : []
        content {
          items = var.query_string_whitelist
        }
      }
    }
  }
}
