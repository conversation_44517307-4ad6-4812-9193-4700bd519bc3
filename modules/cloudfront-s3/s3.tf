resource "aws_s3_bucket" "bucket" {
  bucket = var.s3_bucket_name

  tags = merge(var.tags, {
    Name       = var.s3_bucket_name
    CloudFront = var.cloudfront_domain
  })
}

resource "aws_s3_bucket_ownership_controls" "ownership_controls" {
  bucket = aws_s3_bucket.bucket.id

  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

data "aws_iam_policy_document" "bucket_policy_document" {
  statement {
    sid    = "AllowFullControlToAccountUsers"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }

    actions = [
      "s3:GetObject",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:ListBucket"
    ]

    resources = [
      aws_s3_bucket.bucket.arn,
      "${aws_s3_bucket.bucket.arn}/*",
    ]
  }

  statement {
    sid    = "AllowCloudFrontAccessWithOAC"
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudfront.amazonaws.com"]
    }
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.bucket.arn}/*"]
    condition {
      test     = "StringEquals"
      variable = "aws:SourceArn"
      values   = ["arn:aws:cloudfront::${data.aws_caller_identity.current.account_id}:distribution/${aws_cloudfront_distribution.s3_distribution.id}"]
    }
  }

  dynamic "statement" {
    for_each = var.lambda_edge_functions
    content {
      effect = "Allow"
      principals {
        type        = "Service"
        identifiers = ["lambda.amazonaws.com"]
      }
      actions = [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ]
      resources = [
        aws_s3_bucket.bucket.arn,
        "${aws_s3_bucket.bucket.arn}/*",
      ]
      condition {
        test     = "StringEquals"
        variable = "aws:SourceArn"
        values   = ["arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:${statement.key}"]
      }
    }
  }
}


resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.bucket.id
  policy = data.aws_iam_policy_document.bucket_policy_document.json
}

resource "aws_s3_bucket_public_access_block" "bucket" {
  bucket = aws_s3_bucket.bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_lifecycle_configuration" "lifecycle" {
  # Only apply the lifecycle rule if s3_cleanup_lifecycle_days is greater than 0
  count  = var.s3_cleanup_lifecycle_days > 0 ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

  rule {
    id     = "LifecycleRule"
    status = "Enabled"

    expiration {
      days = var.s3_cleanup_lifecycle_days
    }

    noncurrent_version_expiration {
      noncurrent_days = var.s3_cleanup_lifecycle_days
    }

    filter { prefix = "" }
  }
}

resource "aws_s3_bucket_versioning" "bucket-versioning" {
  bucket = aws_s3_bucket.bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_cors_configuration" "s3_cors" {
  depends_on = [aws_s3_bucket.bucket]
  bucket     = aws_s3_bucket.bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = concat(var.cors_allowed_origins, [var.cloudfront_domain, aws_cloudfront_distribution.s3_distribution.domain_name])

    expose_headers  = ["ETag", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers"]
    max_age_seconds = var.max_ttl
  }
}
