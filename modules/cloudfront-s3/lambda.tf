resource "aws_lambda_function" "jwt_authorizer" {
  count      = var.auth0_protection ? 1 : 0
  depends_on = [aws_cloudwatch_log_group.lambda_loggroup_auth0[0]]

  function_name    = "${var.s3_bucket_name}-authorizer-auth0"
  filename         = "${path.module}/auth0/lambda.zip"
  source_code_hash = filebase64sha256("${path.module}/auth0/lambda.zip")

  handler = "index.handler"
  runtime = "nodejs18.x"
  role    = aws_iam_role.jwt_authorizer.arn
  publish = true

  tags = var.tags
}

resource "aws_lambda_alias" "auth0_alias" {
  count            = var.auth0_protection ? 1 : 0
  name             = "${var.s3_bucket_name}-alias-auth0"
  description      = "Lambda alias for ${var.s3_bucket_name}-authorizer-auth0"
  function_name    = aws_lambda_function.jwt_authorizer[0].function_name
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "allow_cloudfront" {
  count         = var.auth0_protection ? 1 : 0
  statement_id  = "AllowExecutionFromCloudFront"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jwt_authorizer[0].function_name
  principal     = "edgelambda.amazonaws.com"
  source_arn    = "arn:aws:cloudfront::${data.aws_caller_identity.current.account_id}:distribution/*"
}
