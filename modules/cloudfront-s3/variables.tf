variable "s3_bucket_name" {
  type = string
}

variable "cloudfront_domain" {
  type = string
}

variable "min_ttl" {
  type    = number
  default = 3600
}

variable "default_ttl" {
  type    = number
  default = 28800
}

variable "max_ttl" {
  type    = number
  default = 86400
}

# values: PriceClass_100 / PriceClass_200 / PriceClass_All
# https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/PriceClass.html
variable "price_class" {
  type    = string
  default = "PriceClass_100"
}

variable "ssl_certificate_arn" {
  type    = string
  default = "arn:aws:acm:us-east-1:************:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "cors_allowed_origins" {
  type    = list(string)
  default = ["*.whykeyway.com", "*.dev.whykeyway.com", "*.stg.whykeyway.com", "*.demo.whykeyway.com", "*.vercel.app", "localhost:3000", "localhost"]
}

variable "auth0_protection" {
  type    = bool
  default = false
}

# the default value stands for the zone for whykeyway.com
variable "route53_zone_id" {
  type    = string
  default = "Z05531162DGNWUXR3CVOO"
}

variable "lambda_edge_functions" {
  type    = map(string)
  default = {}
}

variable "origin_shield_regions" {
  type        = list(string)
  description = "A list of AWS regions to enable origin shield for."
  default     = ["us-east-1"]
}

variable "origin_failover" {
  type        = string
  description = "The origin to failover to if the primary origin fails."
  default     = ""
}

variable "origin_failover_headers" {
  type        = map(string)
  description = "A map of headers to send to the failover origin."
  default     = {}
}

variable "response_custom_headers" {
  type        = map(string)
  description = "A map of custom headers to be returned on requests."
  default     = {}
}

variable "query_string_whitelist" {
  type        = list(string)
  description = "A list of query string parameters to whitelist."
  default     = []
}

variable "s3_cleanup_lifecycle_days" {
  type        = number
  description = "The number of days to keep S3 objects before deleting them, set 0 to disable cleanup."
  default     = 0
}
