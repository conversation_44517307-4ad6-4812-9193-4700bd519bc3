variable "app_name" {
  type        = string
  description = "The application name. Will be used as a prefix for all resources."
}

variable "integration_type" {
  type    = string
  default = "HTTP_PROXY"
  validation {
    condition = contains(["HTTP_PROXY", "AWS_PROXY"], var.integration_type)
    error_message = "'integration_type' must be one of 'HTTP_PROXY' or 'AWS_PROXY'"
  }
}

variable "endpoint_uri" {
  type        = string
  description = "The HTTP endpoint to which the API Gateway will be connected"
}

variable "route_auth_map" {
  type    = map(string)
  default = { "$default" : "NONE" }
  # default = {"ANY /something{proxy+}": "AUTH0"}
  # default = {"GET /something{proxy+}": "NONE"}
  # default = {"POST /something{proxy+}": "CUSTOM"}
  description = "This is a string map containing a route and the expected auth method for it. Here are some example routes: https://docs.aws.amazon.com/apigateway/latest/developerguide/http-api-develop-routes.html"

  validation {
    condition     = length(setsubtract(distinct(values(var.route_auth_map)), ["AUTH0", "NONE", "CUSTOM"])) == 0
    error_message = "The authentication method for each route must be one of AUTH0, NONE or CUSTOM."
  }
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "Map of tags to apply to all resources."
}


variable "cors_origins" {
  type        = list(string)
  default     = ["*"]
  description = "The CORS origins to allow. By default its a catch-all."
}

variable "cors_methods" {
  type        = list(string)
  default     = ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE", "OPTIONS"]
  description = "The CORS methods to allow. By default its a catch-all."

  validation {
    condition     = length(setsubtract(distinct(var.cors_methods), ["GET", "HEAD", "PUT", "PATCH", "POST", "DELETE", "OPTIONS"])) == 0
    error_message = "Valid cors_methods include GET, HEAD, PUT, PATCH, POST, DELETE, OPTIONS."
  }
}

variable "cors_headers" {
  type        = list(string)
  default     = ["Content-Type", "Authorization", "X-Amz-Date", "X-Api-Key", "X-Amz-Security-Token"]
  description = "The CORS headers to allow. By default it contains a basic list."
}

variable "custom_domain" {
  type        = string
  description = "The custom domain to use for the API Gateway. An example is app-gw.dev.whykeyway.com"
}

variable "acm_ssl_certificate" {
  type    = string
  default = "arn:aws:acm:us-east-1:************:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
  # this is the *.whykeyway.com ACM cert on the main account
  description = "The ACM SSL certificate to use for the custom domain. By default is *.whykeyway.com"
}

# the default value stands for the zone for whykeyway.com
variable "route53_zone_id" {
  type        = string
  default     = "Z05531162DGNWUXR3CVOO"
  description = "The Route53 zone ID for the custom domain. By default is whykeyway.com"
}

variable "setup_access_logs" {
  type        = bool
  default     = false
  description = "Enable access logs for the API Gateway"
}
