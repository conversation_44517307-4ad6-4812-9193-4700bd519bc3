# TG: PRIVATE
resource "aws_lb_target_group" "target" {
  name        = local.private_tg_name
  port        = var.port
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = var.private_network.vpc

  deregistration_delay = 0

  health_check {
    enabled = true
    path    = var.health_check_path
    matcher = var.health_check_matcher

    healthy_threshold   = var.health_check_healthy_threshold
    unhealthy_threshold = var.health_check_unhealthy_threshold
    timeout             = var.health_check_timeout
    interval            = var.health_check_interval

    # port = "traffic-port"
    # protocol = "HTTP"
  }

  tags = merge({ "Name" = local.private_tg_name }, var.tags)
}

# LB: PRIVATE
resource "aws_lb_listener_rule" "private" {
  depends_on   = [aws_lb_target_group.target]
  listener_arn = var.cluster.private_listener

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.target.arn
  }

  condition {
    host_header {
      values = ["${local.zone_name}.*", "${local.public_zone_name}.*"]
    }
  }

  tags = merge({ "Name" = local.private_tg_name }, var.tags)
}
