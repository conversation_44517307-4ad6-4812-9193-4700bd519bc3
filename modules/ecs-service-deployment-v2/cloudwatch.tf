resource "aws_cloudwatch_log_group" "build_loggroup" {
  name              = "/aws/codebuild/${var.app_name}-ecs-${var.cluster.name}-build"
  retention_in_days = 365
  tags              = var.tags
}

resource "aws_cloudwatch_log_group" "migration_loggroup" {
  count             = var.codebuild_migration_stage == true ? 1 : 0
  name              = "/aws/codebuild/${var.app_name}-ecs-${var.cluster.name}-migration"
  retention_in_days = 365
  tags              = var.tags
}
