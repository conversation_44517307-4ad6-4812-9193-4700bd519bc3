variable "app_name" {
  type = string
}

variable "cluster" {
  type = object({
    name             = string
    arn              = string
    dns_prefix       = string
    public_listener  = string
    public_cname     = string
    private_listener = string
    private_cname    = string
  })
}

# >>> VALID CPU/RAM COMBOS <<<
# 256 (.25 vCPU)  | 512, 1024, 2048
# 512 (.5 vCPU)   | 1024, 2048, 3072, 4096	
# 1024 (1 vCPU)	  | 2048, 3072, 4096, 5120, 6144, 7168, 8192
# 2048 (2 vCPU)   | 4096, 5120, 6144, 7168, 8192 (... up to 16G inc. 1G)
# 4096 (4 vCPU)   | 8192, 9216, 10240 (... up to 30G inc. 1G)
# 8192 (8 vCPU)   | 16384, 20480, 24576 (... up to 60G inc. 4G)
# 16384 (16 vCPU) | 32768, 40960, 49152 (... up to 120G inc. 8G)
# TIP: You can use 16*1024 for 16 vCPU and 32*1024 for 32G of RAM
variable "container_size" {
  type = object({
    cpu    = number
    memory = number
  })
  default = {
    cpu    = 1024
    memory = 2048
  }
  description = "The container size including both CPU and memory."

  validation {
    condition = (
      (var.container_size.cpu == 256 && contains([512, 1024, 2048], var.container_size.memory)) ||
      (var.container_size.cpu == 512 && contains([1024, 2048, 3072, 4096], var.container_size.memory)) ||
      (var.container_size.cpu == 1024 && contains([2048, 3072, 4096, 5120, 6144, 7168, 8192], var.container_size.memory)) ||
      (var.container_size.cpu == 2048 && contains([4096, 5120, 6144, 7168, 8192, 9216, 10240, 11264, 12288, 13312, 14336, 15360], var.container_size.memory)) ||
      (var.container_size.cpu == 4096 && contains([8192, 9216, 10240, 11264, 12288, 13312, 14336, 15360, 16384, 17408, 18432, 19456, 20480], var.container_size.memory)) ||
      (var.container_size.cpu == 8192 && contains([16384, 20480, 24576, 28672, 32768, 36864, 40960, 45056, 49152, 53248, 57344, 61440], var.container_size.memory)) ||
      (var.container_size.cpu == 16384 && contains([32768, 40960, 49152, 57344, 65536, 73728, 81920, 90112, 98304, 106496, 114688, 122880], var.container_size.memory))
    )
    error_message = "CPU and memory combination is invalid. Check: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task-cpu-memory-error.html"
  }
}

variable "port" {
  type    = number
  default = 8080
}

variable "save_access_logs" {
  type    = bool
  default = false
}

variable "private_network" {
  type = object({
    vpc             = string
    vpc_name        = string
    cidr_block      = string
    subnets         = list(string)
    route_tables    = list(string)
    security_groups = list(string)
    network_acls    = list(string)
  })
}

variable "github_connection" {
  type    = string
  default = "arn:aws:codestar-connections:us-east-1:************:connection/d0803f29-eef8-4ec7-95d0-8154f4bd356d"
}

variable "github_repository" {
  type = string
}

variable "github_branch_name" {
  type = string
}

variable "ecs_variables" {
  type    = map(string)
  default = {}
  validation {
    condition = alltrue([
      for key, _ in var.ecs_variables : !(
        key == "DD_ENV" || key == "DD_SERVICE" || key == "DD_LOGS_INJECTION" || key == "DD_VERSION" ||
        key == "DD_TRACE_SAMPLE_RATE" || key == "DD_APM_IGNORE_RESOURCES" || key == "DD_TRACE_HEADER_TAGS"
      )
    ])
    error_message = "The following keys are not allowed: DD_ENV, DD_SERVICE, DD_LOGS_INJECTION, DD_VERSION, DD_TRACE_SAMPLE_RATE, DD_APM_IGNORE_RESOURCES, DD_TRACE_HEADER_TAGS, DD_API_KEY"
  }
}

variable "ecs_secrets" {
  type    = map(string)
  default = {}
  validation {
    condition = alltrue([
      for key, _ in var.ecs_secrets : !(
        key == "DD_ENV" || key == "DD_SERVICE" || key == "DD_LOGS_INJECTION" || key == "DD_VERSION" ||
        key == "DD_TRACE_SAMPLE_RATE" || key == "DD_APM_IGNORE_RESOURCES" || key == "DD_TRACE_HEADER_TAGS"
      )
    ])
    error_message = "The following keys are not allowed: DD_ENV, DD_SERVICE, DD_LOGS_INJECTION, DD_VERSION, DD_TRACE_SAMPLE_RATE, DD_APM_IGNORE_RESOURCES, DD_TRACE_HEADER_TAGS, DD_API_KEY"
  }
}

variable "codebuild_migration_stage" {
  type    = bool
  default = false
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "chatbot_arn" {
  type = string
}

variable "codebuild_image" {
  type    = string
  default = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
}

variable "health_check_path" {
  type    = string
  default = "/health"
}

variable "health_check_matcher" {
  type    = string
  default = "200"
}

variable "mount_points" {
  type = list(object({
    name           = string
    container_path = string
    efs_fs_id      = string
  }))
  default = []
}

variable "iam_extra_policies" {
  default = []
  type    = list(string)
}

# the default value stands for the zone for whykeyway.com
variable "route53_zone_id" {
  type    = string
  default = "Z05531162DGNWUXR3CVOO"
}

variable "replica_count" {
  type    = number
  default = 1
}

variable "idle_timeout" {
  type    = number
  default = 60
}

variable "public_endpoint" {
  type    = bool
  default = false
}

variable "health_check_interval" {
  type    = number
  default = 30
  validation {
    condition     = var.health_check_interval >= 5 && var.health_check_interval <= 300
    error_message = "Health check interval must be between 5 and 300 seconds."
  }
}

variable "health_check_healthy_threshold" {
  type    = number
  default = 3
  validation {
    condition     = var.health_check_healthy_threshold >= 2 && var.health_check_healthy_threshold <= 10
    error_message = "Health check healthy threshold must be between 2 and 10."
  }
}

variable "health_check_unhealthy_threshold" {
  type    = number
  default = 3
  validation {
    condition     = var.health_check_unhealthy_threshold >= 2 && var.health_check_unhealthy_threshold <= 10
    error_message = "Health check unhealthy threshold must be between 2 and 10."
  }
}

variable "health_check_timeout" {
  type    = number
  default = 5
  validation {
    condition     = var.health_check_timeout >= 2 && var.health_check_timeout <= 10
    error_message = "Health check timeout must be between 2 and 10 seconds."
  }
}

variable "redeploy_schedule" {
  type        = string
  default     = ""
  description = "The schedule for the redeploy. Default is every Monday at 11:00 AM UTC."
}

variable "codebuild_compute_type" {
  type    = string
  default = "BUILD_GENERAL1_SMALL"
  validation {
    condition     = contains(["BUILD_GENERAL1_SMALL", "BUILD_GENERAL1_MEDIUM", "BUILD_GENERAL1_LARGE"], var.codebuild_compute_type)
    error_message = "CodeBuild compute type must be one of: BUILD_GENERAL1_SMALL, BUILD_GENERAL1_MEDIUM, BUILD_GENERAL1_LARGE."
  }
}
