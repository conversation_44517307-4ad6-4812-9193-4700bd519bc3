resource "aws_codepipeline" "pipeline" {
  depends_on = [aws_ecs_service.service]
  tags       = var.tags

  artifact_store {
    type     = "S3"
    location = "codepipeline-us-east-1-177039379319"
    # this is the location on current deployments, they all share that bucket
    # oh, you want a reason behind that 1770... number? 
    # well, you and me both buddy ¯\_(ツ)_/¯
  }

  name     = "${var.app_name}-ecs-${var.cluster.name}-pipeline"
  role_arn = aws_iam_role.pipeline_role.arn

  stage {
    action {
      category = "Source"

      configuration = {
        BranchName           = var.github_branch_name
        ConnectionArn        = var.github_connection
        FullRepositoryId     = var.github_repository
        OutputArtifactFormat = "CODE_ZIP"
      }

      name             = "Source"
      namespace        = "SourceVariables"
      output_artifacts = ["SourceArtifact"]
      owner            = "AWS"
      provider         = "CodeStarSourceConnection"
      run_order        = "1"
      version          = "1"
    }

    name = "Source"
  }

  stage {
    action {
      category = "Build"

      configuration = {
        ProjectName = "${var.app_name}-ecs-${var.cluster.name}-build"
      }

      input_artifacts  = ["SourceArtifact"]
      name             = "Build"
      namespace        = "BuildVariables"
      output_artifacts = ["BuildArtifact"]
      owner            = "AWS"
      provider         = "CodeBuild"
      run_order        = "1"
      version          = "1"
    }

    name = "Build"
  }

  dynamic "stage" {
    for_each = var.codebuild_migration_stage == true ? toset([1]) : toset([])
    content {
      action {
        category = "Build"

        configuration = {
          ProjectName = "${var.app_name}-ecs-${var.cluster.name}-migration"
        }

        input_artifacts = ["SourceArtifact"]
        name            = "Migrate"
        owner           = "AWS"
        provider        = "CodeBuild"
        run_order       = "1"
        version         = "1"
      }

      name = "Migrate"
    }
  }

  stage {
    action {
      category = "Deploy"

      configuration = {
        ClusterName = var.cluster.name
        ServiceName = "${var.app_name}-service"
      }

      input_artifacts = ["BuildArtifact"] # expects imagedefinitions.json
      name            = "Deploy"
      namespace       = "DeployVariables"
      owner           = "AWS"
      provider        = "ECS"
      run_order       = "1"
      version         = "1"
    }

    name = "Deploy"
  }
}

resource "aws_codestarnotifications_notification_rule" "pipeline_notification" {
  name        = "${var.app_name}-ecs-${var.cluster.name}-notification"
  detail_type = "FULL"
  resource    = aws_codepipeline.pipeline.arn
  status      = "ENABLED"
  tags        = var.tags

  event_type_ids = [
    "codepipeline-pipeline-pipeline-execution-failed",
    "codepipeline-pipeline-pipeline-execution-started",
    "codepipeline-pipeline-pipeline-execution-succeeded"
  ]

  target {
    address = var.chatbot_arn
    type    = "AWSChatbotSlack"
  }
}
