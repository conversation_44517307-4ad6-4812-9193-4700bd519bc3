<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 app_name  = 
	 cluster  = 
	 codebuild_variables  = 
	 ecs_network  = 
	 ecs_variables  = 
	 github_branch_name  = 
	 github_repository  = 
	 lb_network  = 

	 # Optional variables
	 chatbot_arn  = "arn:aws:chatbot::681574592108:chat-configuration/slack-channel/deployments-dev"
	 codebuild_image  = "aws/codebuild/amazonlinux2-x86_64-standard:3.0"
	 codebuild_migration_stage  = false
	 cpu_units  = 1024
	 github_connection  = "arn:aws:codestar-connections:us-east-1:681574592108:connection/d0803f29-eef8-4ec7-95d0-8154f4bd356d"
	 health_check_matcher  = "200"
	 health_check_path  = "/health"
	 iam_extra_policies  = []
	 mount_points  = []
	 parameter_prefix  = "arn:aws:ssm:us-east-1:681574592108:parameter/"
	 port  = 8080
	 private_endpoint  = false
	 ram_mibs  = 2048
	 replica_count  = 1
	 route53_zone_id  = "Z05531162DGNWUXR3CVOO"
	 save_access_logs  = false
	 ssl_certificate_arn  = "arn:aws:acm:us-east-1:681574592108:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
	 tags  = {}
}
```
## Resources

| Name | Type |
|------|------|
| [aws_codebuild_project.build](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codebuild_project) | resource |
| [aws_codebuild_project.migration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codebuild_project) | resource |
| [aws_codepipeline.pipeline](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codepipeline) | resource |
| [aws_codestarnotifications_notification_rule.pipeline_notification](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/codestarnotifications_notification_rule) | resource |
| [aws_ecr_lifecycle_policy.policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_lifecycle_policy) | resource |
| [aws_ecr_repository.repo](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecr_repository) | resource |
| [aws_ecs_service.service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_service) | resource |
| [aws_ecs_task_definition.task](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ecs_task_definition) | resource |
| [aws_iam_policy.codebuild_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.ecs_container_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.ecs_task_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.pipeline_role_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.codebuild_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.ecs_container_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.ecs_task_execution_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.pipeline_role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.ecs_container_extra_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.ecs_container_policy_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.ecs_task_policy_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_lb.balancer](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb) | resource |
| [aws_lb_listener.http](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_listener.https](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_listener) | resource |
| [aws_lb_target_group.tg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lb_target_group) | resource |
| [aws_route53_record.www](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route53_record) | resource |
| [aws_s3_bucket.lb-logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket) | resource |
| [aws_s3_bucket_acl.acl](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_acl) | resource |
| [aws_s3_bucket_lifecycle_configuration.lifecycle](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_lifecycle_configuration) | resource |
| [aws_s3_bucket_policy.lb-logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |
| [aws_s3_bucket_public_access_block.public-access](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_public_access_block) | resource |
| [aws_s3_bucket_versioning.lb-logs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_versioning) | resource |
| [aws_canonical_user_id.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/canonical_user_id) | data source |
| [aws_ssm_parameter.ecs_secrets_must_exist](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ssm_parameter) | data source |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_app_name"></a> [app\_name](#input\_app\_name) | n/a | `string` | n/a | yes |
| <a name="input_chatbot_arn"></a> [chatbot\_arn](#input\_chatbot\_arn) | n/a | `string` | `"arn:aws:chatbot::681574592108:chat-configuration/slack-channel/deployments-dev"` | no |
| <a name="input_cluster"></a> [cluster](#input\_cluster) | n/a | <pre>object({<br>    name       = string<br>    arn        = string<br>    dns_prefix = string<br>  })</pre> | n/a | yes |
| <a name="input_codebuild_image"></a> [codebuild\_image](#input\_codebuild\_image) | n/a | `string` | `"aws/codebuild/amazonlinux2-x86_64-standard:3.0"` | no |
| <a name="input_codebuild_migration_stage"></a> [codebuild\_migration\_stage](#input\_codebuild\_migration\_stage) | n/a | `bool` | `false` | no |
| <a name="input_codebuild_variables"></a> [codebuild\_variables](#input\_codebuild\_variables) | n/a | <pre>list(object({<br>    name  = string<br>    type  = string<br>    value = string<br>  }))</pre> | n/a | yes |
| <a name="input_cpu_units"></a> [cpu\_units](#input\_cpu\_units) | >>> VALID CPU/RAM COMBOS <<< 256 (.25 vCPU) \| 512, 1024, 2048 512 (.5 vCPU)  \| 1024, 2048, 3072, 4096 1024 (1 vCPU)	 \| 2048, 3072, 4096, 5120, 6144, 7168, 8192 2048 (2 vCPU)  \| 4096, 5120, 6144, 7168, 8192 (... up to 16G) 4096 (4 vCPU)  \| 8192, 9216, 10240 (... up to 30G) | `number` | `1024` | no |
| <a name="input_ecs_network"></a> [ecs\_network](#input\_ecs\_network) | n/a | <pre>object({<br>    vpc             = string<br>    cidr_block      = string<br>    subnets         = list(string)<br>    route_tables    = list(string)<br>    security_groups = list(string)<br>    network_acls    = list(string)<br>  })</pre> | n/a | yes |
| <a name="input_ecs_variables"></a> [ecs\_variables](#input\_ecs\_variables) | n/a | <pre>list(object({<br>    variable_name  = string<br>    parameter_name = string<br>  }))</pre> | n/a | yes |
| <a name="input_github_branch_name"></a> [github\_branch\_name](#input\_github\_branch\_name) | n/a | `string` | n/a | yes |
| <a name="input_github_connection"></a> [github\_connection](#input\_github\_connection) | n/a | `string` | `"arn:aws:codestar-connections:us-east-1:681574592108:connection/d0803f29-eef8-4ec7-95d0-8154f4bd356d"` | no |
| <a name="input_github_repository"></a> [github\_repository](#input\_github\_repository) | n/a | `string` | n/a | yes |
| <a name="input_health_check_matcher"></a> [health\_check\_matcher](#input\_health\_check\_matcher) | n/a | `string` | `"200"` | no |
| <a name="input_health_check_path"></a> [health\_check\_path](#input\_health\_check\_path) | n/a | `string` | `"/health"` | no |
| <a name="input_iam_extra_policies"></a> [iam\_extra\_policies](#input\_iam\_extra\_policies) | n/a | `list(string)` | `[]` | no |
| <a name="input_lb_network"></a> [lb\_network](#input\_lb\_network) | n/a | <pre>object({<br>    vpc             = string<br>    cidr_block      = string<br>    subnets         = list(string)<br>    route_tables    = list(string)<br>    security_groups = list(string)<br>    network_acls    = list(string)<br>  })</pre> | n/a | yes |
| <a name="input_mount_points"></a> [mount\_points](#input\_mount\_points) | n/a | <pre>list(object({<br>    name           = string<br>    container_path = string<br>    efs_fs_id      = string<br>  }))</pre> | `[]` | no |
| <a name="input_parameter_prefix"></a> [parameter\_prefix](#input\_parameter\_prefix) | n/a | `string` | `"arn:aws:ssm:us-east-1:681574592108:parameter/"` | no |
| <a name="input_port"></a> [port](#input\_port) | n/a | `number` | `8080` | no |
| <a name="input_private_endpoint"></a> [private\_endpoint](#input\_private\_endpoint) | n/a | `bool` | `false` | no |
| <a name="input_ram_mibs"></a> [ram\_mibs](#input\_ram\_mibs) | n/a | `number` | `2048` | no |
| <a name="input_replica_count"></a> [replica\_count](#input\_replica\_count) | n/a | `number` | `1` | no |
| <a name="input_route53_zone_id"></a> [route53\_zone\_id](#input\_route53\_zone\_id) | the default value stands for the zone for whykeyway.com | `string` | `"Z05531162DGNWUXR3CVOO"` | no |
| <a name="input_save_access_logs"></a> [save\_access\_logs](#input\_save\_access\_logs) | n/a | `bool` | `false` | no |
| <a name="input_ssl_certificate_arn"></a> [ssl\_certificate\_arn](#input\_ssl\_certificate\_arn) | n/a | `string` | `"arn:aws:acm:us-east-1:681574592108:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | n/a | `map(string)` | `{}` | no |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_access_logs_bucket"></a> [access\_logs\_bucket](#output\_access\_logs\_bucket) | n/a |
| <a name="output_load_balancer_arn"></a> [load\_balancer\_arn](#output\_load\_balancer\_arn) | n/a |
| <a name="output_load_balancer_cname"></a> [load\_balancer\_cname](#output\_load\_balancer\_cname) | n/a |
| <a name="output_load_balancer_listener_arn"></a> [load\_balancer\_listener\_arn](#output\_load\_balancer\_listener\_arn) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->