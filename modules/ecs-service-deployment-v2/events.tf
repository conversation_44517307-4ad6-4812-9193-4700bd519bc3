resource "aws_cloudwatch_event_rule" "deploy_trigger" {
  count               = var.redeploy_schedule != "" ? 1 : 0
  name                = "${var.app_name}-${var.cluster.name}-deploy-trigger"
  schedule_expression = var.redeploy_schedule
}

resource "aws_iam_role" "eventbridge_invoke_pipeline" {
  count = var.redeploy_schedule != "" ? 1 : 0
  name  = "${var.app_name}-${var.cluster.name}-ev-invoke-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "events.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy" "pipeline_invoke_policy" {
  count = var.redeploy_schedule != "" ? 1 : 0
  name  = "${var.app_name}-${var.cluster.name}-ev-invoke-policy"
  role  = aws_iam_role.eventbridge_invoke_pipeline[0].id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action   = "codepipeline:StartPipelineExecution",
      Effect   = "Allow",
      Resource = aws_codepipeline.pipeline.arn
    }]
  })
}

resource "aws_cloudwatch_event_target" "pipeline_trigger_with_role" {
  count     = var.redeploy_schedule != "" ? 1 : 0
  rule      = aws_cloudwatch_event_rule.deploy_trigger[0].name
  target_id = "CodePipelineTrigger"
  arn       = aws_codepipeline.pipeline.arn
  role_arn  = aws_iam_role.eventbridge_invoke_pipeline[0].arn
}
