data "aws_caller_identity" "current" {}

locals {
  app_name     = replace(var.app_name, " ", "-")
  cluster_name = replace(var.cluster.name, " ", "-")

  private_tg_name = substr(join("-", compact(["prv", var.cluster.dns_prefix, local.app_name])), 0, 32)

  ecs_environment = [for k, v in var.ecs_variables : tomap({ name = k, value = v })]

  parameter_prefix = "arn:aws:ssm:us-east-1:${data.aws_caller_identity.current.account_id}:parameter/"
  ecs_secrets      = [for k, v in var.ecs_secrets : tomap({ name = k, valueFrom = "${local.parameter_prefix}${replace(v, "/^//", "")}" })]

  mount_points_container = [for m in var.mount_points : tomap({ containerPath = m.container_path, sourceVolume = m.name })]
  mount_points_volumes   = [for m in var.mount_points : tomap({ name = m.name, efs_fs_id = m.efs_fs_id })]

  zone_name = join(".", compact([var.app_name, var.cluster.dns_prefix]))
  # replace zone_name removing -api by -gw, or adding -gw before the dns_prefix
  public_zone_name = strcontains(var.app_name, "-api") ? replace(local.zone_name, "-api", "-gw") : join(".", compact(["${var.app_name}-gw", var.cluster.dns_prefix]))

  # TODO: These variables are hardcoded, not the best idea
  default_codebuild_variables = {
    AWS_DEFAULT_REGION = "us-east-1"
    AWS_ACCOUNT_ID     = data.aws_caller_identity.current.account_id
    IMAGE_REPO_NAME    = aws_ecr_repository.repo.name
    ECS_CONTAINER_NAME = var.app_name
  }
  default_codebuild_secrets = {
    GH_USERNAME = "github_user"
    GH_TOKEN    = "github_token"
  }
}
