resource "aws_codebuild_project" "migration" {
  count = var.codebuild_migration_stage == true ? 1 : 0
  tags  = var.tags

  artifacts {
    type = "CODEPIPELINE"
  }

  badge_enabled = "false"
  build_timeout = "60"

  cache {
    type = "NO_CACHE"
  }

  concurrent_build_limit = "1"
  description            = "Database migration stage for ${var.app_name}-ecs-${var.cluster.name}"
  # TODO: check if the following is the default key
  encryption_key = "arn:aws:kms:us-east-1:${data.aws_caller_identity.current.account_id}:alias/aws/s3"

  environment {
    compute_type = var.codebuild_compute_type

    dynamic "environment_variable" {
      for_each = { for key, value in merge(local.default_codebuild_variables, var.ecs_variables) : key => {
        name  = key
        type  = "PLAINTEXT"
        value = value
        }
      }

      content {
        name  = environment_variable.value.name
        type  = environment_variable.value.type
        value = environment_variable.value.value
      }
    }

    dynamic "environment_variable" {
      for_each = {
        for key, value in merge(local.default_codebuild_secrets, var.ecs_secrets) :
        key => {
          name  = key
          type  = "PARAMETER_STORE"
          value = value
        } if !contains(keys(local.default_codebuild_variables), key) && !contains(keys(var.ecs_variables), key)
      }

      content {
        name  = environment_variable.value.name
        type  = environment_variable.value.type
        value = environment_variable.value.value
      }
    }

    image                       = var.codebuild_image
    image_pull_credentials_type = "CODEBUILD"
    type                        = "LINUX_CONTAINER"
    privileged_mode             = "true"
  }

  logs_config {
    cloudwatch_logs {
      group_name = aws_cloudwatch_log_group.migration_loggroup[count.index].name
      status     = "ENABLED"
    }

    s3_logs {
      encryption_disabled = "false"
      status              = "DISABLED"
    }
  }

  vpc_config {
    vpc_id             = var.private_network.vpc
    subnets            = var.private_network.subnets
    security_group_ids = var.private_network.security_groups
  }

  name               = "${var.app_name}-ecs-${var.cluster.name}-migration"
  project_visibility = "PRIVATE"
  queued_timeout     = "480"
  service_role       = aws_iam_role.codebuild_role.arn

  source {
    type      = "CODEPIPELINE"
    buildspec = "buildspec-migration.yml"
  }

  source_version = "develop"
}
