locals {
  # this removes a possible / at the end of the prefix to avoid double slashes
  parameter_prefix = replace(var.parameter_prefix, "//$/", "")
}

resource "aws_ssm_parameter" "database_api_user" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  name  = "${local.parameter_prefix}/db-api-username"
  value = var.database_api_user
  type  = "String"
  tags  = var.tags
}

resource "random_password" "db_api_password" {
  length  = 20
  special = false
  lifecycle {
    ignore_changes = [length, lower, special]
  }
}

resource "aws_ssm_parameter" "database_api_password" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  name  = "${local.parameter_prefix}/db-api-password"
  value = random_password.db_api_password.result
  type  = "SecureString"
  tags  = var.tags
}

resource "aws_ssm_parameter" "database_connection_string" {
  depends_on = [
    postgresql_database.database
  ]

  name  = "${local.parameter_prefix}/db-connection-string"
  value = "jdbc:postgresql://${var.rds_server_config.host}:${var.rds_server_config.port}/${var.database_name}"
  type  = "String"
  tags  = var.tags
}
