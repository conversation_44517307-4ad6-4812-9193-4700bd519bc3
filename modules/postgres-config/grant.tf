# GRANT DATABASE ACCESS
resource "postgresql_grant" "grant_api_user_db" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = var.database_api_user
  object_type = "database"
  privileges  = ["CONNECT", "CREATE", "TEMPORARY"]
}

resource "postgresql_grant" "grant_readonly_db" {
  depends_on = [
    postgresql_grant.grant_api_user_db,
    postgresql_role.database_readonly_user
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = local.readonly_user_name
  object_type = "database"
  privileges  = ["CONNECT"]
}

# GRANT SCHEMA ACCESS
resource "postgresql_grant" "grant_api_user_schema" {
  depends_on = [
    postgresql_grant.grant_readonly_db
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = var.database_api_user
  object_type = "schema"
  privileges  = ["CREATE", "USAGE"]
}

resource "postgresql_grant" "grant_readonly_schema" {
  depends_on = [
    postgresql_grant.grant_api_user_schema
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = local.readonly_user_name
  object_type = "schema"
  privileges  = ["USAGE"]
}

# GRANT TABLES ACCESS
resource "postgresql_grant" "grant_api_user_tables" {
  depends_on = [
    postgresql_grant.grant_readonly_schema
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = var.database_api_user
  object_type = "table"
  privileges  = ["DELETE", "INSERT", "REFERENCES", "SELECT", "TRIGGER", "TRUNCATE", "UPDATE"]
}

resource "postgresql_grant" "grant_readonly_tables" {
  depends_on = [
    postgresql_grant.grant_api_user_tables
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = local.readonly_user_name
  object_type = "table"
  privileges  = ["SELECT"]
}

# GRANT SEQUENCES ACCESS
resource "postgresql_grant" "grant_api_user_sequences" {
  depends_on = [
    postgresql_grant.grant_readonly_tables
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = var.database_api_user
  object_type = "sequence"
  privileges  = ["SELECT", "UPDATE", "USAGE"]
}

resource "postgresql_grant" "grant_readonly_sequences" {
  depends_on = [
    postgresql_grant.grant_api_user_sequences
  ]

  count       = length(local.schemas)
  schema      = local.schemas[count.index]
  database    = var.database_name
  role        = local.readonly_user_name
  object_type = "sequence"
  privileges  = ["SELECT"]
}

# GRANT ROLES TO ROOT
resource "postgresql_grant_role" "grant_root" {
  depends_on        = [postgresql_grant.grant_readonly_sequences]
  role              = var.rds_server_config.user
  grant_role        = var.database_api_user
  with_admin_option = true
}

# GRANT ROLES TO USERS
resource "postgresql_grant_role" "grant_users" {
  depends_on        = [postgresql_grant_role.grant_root, postgresql_role.users, postgresql_role.db_api_user]
  for_each          = local.user_password_parameters
  role              = each.key
  grant_role        = var.database_api_user
  with_admin_option = false
}

# GRANT ROLES TO READONLY USERS
resource "postgresql_grant_role" "grant_ro_users" {
  depends_on        = [postgresql_grant_role.grant_users, postgresql_role.users, postgresql_role.database_readonly_user]
  for_each          = local.ro_user_password_parameters
  role              = each.key
  grant_role        = local.readonly_user_name
  with_admin_option = false
}
