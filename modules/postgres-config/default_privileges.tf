# API ROLE
resource "postgresql_default_privileges" "api_user_tables" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  role     = var.database_api_user
  database = var.database_name
  schema   = "public"

  owner       = var.database_api_user
  object_type = "table"
  privileges  = ["DELETE", "INSERT", "REFERENCES", "SELECT", "TRIGGER", "TRUNCATE", "UPDATE"]
}

resource "postgresql_default_privileges" "api_user_sequence" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  role     = var.database_api_user
  database = var.database_name
  schema   = "public"

  owner       = var.database_api_user
  object_type = "sequence"
  privileges  = ["SELECT", "UPDATE", "USAGE"]
}

resource "postgresql_default_privileges" "api_user_schema" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.db_api_user
  ]

  role     = var.database_api_user
  database = var.database_name

  owner       = var.database_api_user
  object_type = "schema"
  privileges  = ["USAGE"]
}

# READONLY ROLE
resource "postgresql_default_privileges" "read_only_tables" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = local.readonly_user_name
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "table"
  privileges  = ["SELECT"]
}

resource "postgresql_default_privileges" "read_only_sequence" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = local.readonly_user_name
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "sequence"
  privileges  = ["SELECT"]
}

resource "postgresql_default_privileges" "read_only_schema" {
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = local.readonly_user_name
  database = var.database_name

  owner       = local.readonly_user_name
  object_type = "schema"
  privileges  = ["USAGE"]
}

# USERS
resource "postgresql_default_privileges" "user_tables" {
  for_each = toset(var.database_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "table"
  privileges  = ["DELETE", "INSERT", "REFERENCES", "SELECT", "TRIGGER", "TRUNCATE", "UPDATE"]
}

resource "postgresql_default_privileges" "user_sequence" {
  for_each = toset(var.database_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "sequence"
  privileges  = ["SELECT", "UPDATE", "USAGE"]
}

resource "postgresql_default_privileges" "user_schema" {
  for_each = toset(var.database_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name

  owner       = local.readonly_user_name
  object_type = "schema"
  privileges  = ["USAGE"]
}

# READONLY USERS
resource "postgresql_default_privileges" "ro_user_tables" {
  for_each = toset(var.database_readonly_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "table"
  privileges  = ["SELECT"]
}

resource "postgresql_default_privileges" "ro_user_sequence" {
  for_each = toset(var.database_readonly_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name
  schema   = "public"

  owner       = local.readonly_user_name
  object_type = "sequence"
  privileges  = ["SELECT"]
}

resource "postgresql_default_privileges" "ro_user_schema" {
  for_each = toset(var.database_readonly_users)
  depends_on = [
    postgresql_database.database,
    postgresql_role.database_readonly_user
  ]

  role     = each.value
  database = var.database_name

  owner       = local.readonly_user_name
  object_type = "schema"
  privileges  = ["USAGE"]
}
