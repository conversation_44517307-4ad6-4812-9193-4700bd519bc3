locals {
  user_password_parameters    = { for user in var.database_users : user => "/sre/postgresql-passwords/${user}" }
  ro_user_password_parameters = { for user in var.database_readonly_users : user => "/sre/postgresql-passwords/${user}" }
}

# Check that all the requested users have their password setup on parameter store
data "aws_ssm_parameter" "user_password_in_parameterstore" {
  for_each        = merge(local.user_password_parameters, local.ro_user_password_parameters)
  name            = each.value
  with_decryption = true
}

resource "postgresql_role" "db_api_user" {
  name     = var.database_api_user
  login    = true
  password = random_password.db_api_password.result
  inherit  = true
}

resource "postgresql_role" "database_readonly_user" {
  depends_on = [postgresql_database.database]
  name       = local.readonly_user_name
  login      = false
  inherit    = true
}

resource "postgresql_role" "users" {
  for_each = var.skip_user_creation ? {} : merge(local.user_password_parameters, local.ro_user_password_parameters)
  name     = each.key
  login    = true
  inherit  = true
  password = data.aws_ssm_parameter.user_password_in_parameterstore[each.key].value

  lifecycle {
    ignore_changes = [roles]
  }
}
