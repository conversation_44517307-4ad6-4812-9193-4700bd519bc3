variable "rds_server_config" {
  type = object({
    host = string
    port = string
    user = string
    pass = string
  })
}

variable "database_name" {
  type = string
}

variable "database_api_user" {
  type = string
}

variable "database_users" {
  type    = list(string)
  default = []
}

variable "database_readonly_users" {
  type    = list(string)
  default = []
}

# Example: "/dev/app_name/"
variable "parameter_prefix" {
  type = string
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "database_extensions" {
  type    = list(string)
  default = []
}

variable "extra_schemas" {
  type    = list(string)
  default = []
}

variable "skip_user_creation" {
  type    = bool
  default = false
}
