resource "aws_cloudfront_distribution" "s3_distribution" {
  depends_on = [
    aws_cloudfront_cache_policy.cp,
    aws_cloudfront_response_headers_policy.response_policy
  ]

  origin {
    domain_name = var.origin_domain_name
    origin_id   = local.origin_id

    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1.2"]
    }

    dynamic "custom_header" {
      for_each = var.custom_headers
      content {
        name  = custom_header.key
        value = custom_header.value
      }
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = var.cloudfront_domain
  default_root_object = "index.html"

  #  logging_config {
  #    include_cookies = false
  #    bucket          = "mylogs.s3.amazonaws.com"
  #    prefix          = "myprefix"
  #  }

  aliases = [var.cloudfront_domain]

  default_cache_behavior {
    allowed_methods            = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    cached_methods             = ["GET", "HEAD"]
    cache_policy_id            = aws_cloudfront_cache_policy.cp.id
    target_origin_id           = local.origin_id
    response_headers_policy_id = aws_cloudfront_response_headers_policy.response_policy.id

    dynamic "lambda_function_association" {
      for_each = var.request_functions
      content {
        event_type   = "viewer-request"
        lambda_arn   = lambda_function_association.value
        include_body = false
      }
    }

    dynamic "lambda_function_association" {
      for_each = var.response_functions
      content {
        event_type   = "viewer-response"
        lambda_arn   = lambda_function_association.value
        include_body = false
      }
    }

    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = var.price_class

  restrictions {
    geo_restriction {
      restriction_type = "none"
      locations        = []
    }
  }

  tags = merge(var.tags, {
    Name = var.cloudfront_domain
  })

  viewer_certificate {
    acm_certificate_arn = var.ssl_certificate_arn
    ssl_support_method  = "sni-only"
  }
}

resource "aws_cloudfront_response_headers_policy" "response_policy" {
  name    = "${local.origin_id}-response-policy"
  comment = "Response Headers Policy for ${var.cloudfront_domain}"

  cors_config {
    access_control_allow_credentials = true

    access_control_allow_headers {
      items = ["Authorization", "Origin", "Referer", "Accept", "Accept-Language", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Access-Control-Allow-Origin", "Content-Type"]
    }

    access_control_expose_headers {
      items = ["ETag", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "Access-Control-Allow-Methods"]
    }

    access_control_allow_methods {
      items = ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
    }

    access_control_allow_origins {
      items = var.cors_allowed_origins
    }

    origin_override = true
  }

  security_headers_config {
    strict_transport_security {
      access_control_max_age_sec = var.max_ttl
      include_subdomains         = true
      preload                    = true
      override                   = false
    }

    content_type_options {
      override = false
    }

    xss_protection {
      protection = true
      mode_block = true
      override   = false
    }

    referrer_policy {
      referrer_policy = "same-origin"
      override        = false
    }
  }
}

resource "aws_cloudfront_cache_policy" "cp" {
  name        = "${local.origin_id}-cp"
  comment     = "Cache Policy for ${var.cloudfront_domain}"
  min_ttl     = var.min_ttl
  default_ttl = var.default_ttl
  max_ttl     = var.max_ttl

  parameters_in_cache_key_and_forwarded_to_origin {
    cookies_config {
      cookie_behavior = "all"
    }
    headers_config {
      header_behavior = "whitelist"
      headers {
        items = ["Authorization", "Origin", "Referer", "Accept", "Accept-Language", "Access-Control-Request-Headers", "Access-Control-Request-Method", "Access-Control-Allow-Origin", "Content-Type"]
      }
    }
    query_strings_config {
      query_string_behavior = "all"
    }
  }
}
