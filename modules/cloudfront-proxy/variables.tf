variable "origin_domain_name" {
  type = string
}

variable "cloudfront_domain" {
  type = string
}

variable "min_ttl" {
  type    = number
  default = 3600
}

variable "default_ttl" {
  type    = number
  default = 28800
}

variable "max_ttl" {
  type    = number
  default = 86400
}

variable "custom_headers" {
  description = "Custom headers to add to the origin request"
  type        = map(string)
  default     = {}
}


# values: PriceClass_100 / PriceClass_200 / PriceClass_All
# https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/PriceClass.html
variable "price_class" {
  type    = string
  default = "PriceClass_100"
}

variable "ssl_certificate_arn" {
  type    = string
  default = "arn:aws:acm:us-east-1:************:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "cors_allowed_origins" {
  type    = list(string)
  default = ["*.whykeyway.com", "*.dev.whykeyway.com", "*.stg.whykeyway.com", "*.demo.whykeyway.com", "*.vercel.app", "localhost:3000", "localhost"]
}

# the default value stands for the zone for whykeyway.com
variable "route53_zone_id" {
  type    = string
  default = "Z05531162DGNWUXR3CVOO"
}

variable "request_functions" {
  type    = list(string)
  default = []
}

variable "response_functions" {
  type    = list(string)
  default = []
}

variable "query_string_whitelist" {
  type        = list(string)
  description = "A list of query string parameters to whitelist."
  default     = []
}
