resource "aws_lambda_layer_version" "layer" {
  for_each = var.function_layers != null ? var.function_layers : {}

  layer_name          = each.value.layer_name
  description         = each.value.description
  compatible_runtimes = each.value.compatible_runtimes
  s3_bucket           = each.value.s3_bucket
  s3_key              = each.value.s3_key

  filename = null

  lifecycle {
    ignore_changes = [
      filename,
      s3_bucket,
      s3_key,
      s3_object_version,
    ]
  }
}
