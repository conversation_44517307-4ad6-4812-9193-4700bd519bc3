locals {
  source           = upper(var.function_source.source)
  lambda_filename  = upper(var.function_source.source) == "FILE" ? var.function_source.file_path : null
  lambda_s3_key    = upper(var.function_source.source) == "S3" ? var.function_source.s3_key : null
  lambda_s3_bucket = upper(var.function_source.source) == "S3" ? var.function_source.s3_bucket : null
  package_type     = upper(var.function_source.source) == "ECR" ? "Image" : "Zip"
}

resource "aws_lambda_function" "function" {
  function_name = local.lambda_name
  role          = aws_iam_role.function_role.arn
  package_type  = local.package_type
  runtime       = var.runtime
  handler       = local.source != "ECR" ? var.handler : null

  image_uri = local.source == "ECR" ? "${data.aws_caller_identity.current.account_id}.dkr.ecr.us-east-1.amazonaws.com/sre-lambda-docker-hello-world:latest" : null
  s3_bucket = local.source == "S3" ? local.lambda_s3_bucket : null
  s3_key    = local.source == "S3" ? local.lambda_s3_key : null
  layers    = var.function_layers != null ? aws_lambda_layer_version.layer[*].arn : null

  publish = true

  memory_size = var.memory_size
  timeout     = var.timeout
  description = var.description

  dynamic "environment" {
    for_each = local.environment_variables != null ? [1] : []
    content {
      variables = local.environment_variables
    }
  }

  vpc_config {
    subnet_ids         = var.vpc_subnet_ids
    security_group_ids = var.vpc_security_group_ids
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [
      function_name,
      image_uri,
      filename,
      source_code_hash,
      s3_bucket,
      s3_key,
      s3_object_version,
      # the following three throw a warning but prevent showing changes on each apply
      version,
      qualified_arn,
      qualified_invoke_arn
    ]
  }
}

resource "aws_lambda_alias" "function_alias" {
  name             = "${local.lambda_name}-alias"
  description      = "Lambda alias for ${local.lambda_name}"
  function_name    = aws_lambda_function.function.function_name
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "allow_alb" {
  depends_on = [aws_lambda_function.function, aws_alb_target_group.tg]
  count      = var.disable_lb != true ? 1 : 0

  statement_id   = "allow-alb-${local.lambda_name}-tg"
  action         = "lambda:InvokeFunction"
  function_name  = aws_lambda_function.function.function_name
  principal      = "elasticloadbalancing.amazonaws.com"
  source_account = data.aws_caller_identity.current.account_id
  source_arn     = aws_alb_target_group.tg[0].arn
}

resource "aws_lambda_event_source_mapping" "sqs_trigger" {
  depends_on = [aws_lambda_function.function]
  count      = var.sqs_trigger != null ? 1 : 0

  event_source_arn                   = var.sqs_trigger.queue_arn
  function_name                      = aws_lambda_function.function.function_name
  enabled                            = var.sqs_trigger.enabled
  batch_size                         = var.sqs_trigger.batch_size
  maximum_batching_window_in_seconds = var.sqs_trigger.maximum_batching_window
  tags                               = var.sqs_trigger.tags
}

resource "aws_lambda_permission" "sqs_trigger" {
  depends_on = [aws_lambda_function.function]
  count      = var.sqs_trigger != null && try(var.sqs_trigger.enabled, false) == true ? 1 : 0

  statement_id  = "AllowExecutionFromSQS"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.function.function_name
  principal     = "sqs.amazonaws.com"
  source_arn    = var.sqs_trigger.queue_arn
}
