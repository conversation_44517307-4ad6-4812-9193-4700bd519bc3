# LB: TARGET GROUPS
resource "aws_alb_target_group" "tg" {
  depends_on  = [aws_lambda_function.function]
  count       = var.disable_lb != true ? 1 : 0

  name        = "${local.lambda_name}-tg"
  vpc_id      = var.private_network.vpc
  target_type = "lambda"

  deregistration_delay = 0

  tags = var.tags
}

resource "aws_lb_target_group_attachment" "function" {
  count       = var.disable_lb != true ? 1 : 0

  depends_on       = [aws_lambda_function.function, aws_alb_target_group.tg, aws_lambda_permission.allow_alb]
  target_group_arn = aws_alb_target_group.tg[0].arn
  target_id        = aws_lambda_function.function.arn
}

resource "aws_lb_listener_rule" "private" {
  count       = var.disable_lb != true ? 1 : 0

  depends_on   = [aws_alb_target_group.tg]
  listener_arn = var.cluster.private_listener

  action {
    type             = "forward"
    target_group_arn = aws_alb_target_group.tg[0].arn
  }

  condition {
    host_header {
      values = ["${local.zone_name}.*"]
    }
  }

  tags = merge({ "Name" = "${local.lambda_name}-tg" }, var.tags)
}
