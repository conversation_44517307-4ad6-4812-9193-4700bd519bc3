output "lambda_arn" {
  value = aws_lambda_function.function.qualified_arn
}

output "lambda_url" {
  depends_on = [ aws_route53_record.api ]
  value = try(aws_route53_record.api[0].fqdn, null)
}

output "lambda_version" {
  description = "Latest published version of the Lambda function"
  value       = aws_lambda_function.function.version
}

output "layer_arn" {
  description = "ARN of the Lambda Layer"
  value       = var.function_layers != null ? aws_lambda_layer_version.layer[0].arn : null
}

output "function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.function.function_name
}
