# variables.tf
variable "name" {
  description = "Name of the existing Lambda function"
  type        = string
}

variable "force_app_name" {
  type        = bool
  description = <<EOT
  Whether you want to force the function's name to be equal to the "name" variable.
  Useful when importing resources to Terraform.
EOT
  default     = false
}

variable "handler" {
  description = "Lambda function handler"
  type        = string
  default     = null
}

variable "runtime" {
  description = "Lambda runtime environment"
  type        = string
  default     = null
}

variable "function_source" {
  type = object({
    source    = string
    s3_key    = optional(string)
    s3_bucket = optional(string)
    image_uri = optional(string)
  })
  validation {
    condition     = contains(["S3", "ECR"], upper(var.function_source.source))
    error_message = "var.function_source 'source' field must be one of 'S3', or 'ECR'"
  }
  validation {
    condition     = (upper(var.function_source.source) == "S3" && var.function_source.s3_key != null) || upper(var.function_source.source) != "S3"
    error_message = "When var.function_source 'source' field is 'S3', 's3_key' field must be specified as well"
  }
  validation {
    condition     = (upper(var.function_source.source) == "S3" && var.function_source.s3_bucket != null) || upper(var.function_source.source) != "S3"
    error_message = "When var.function_source 'source' field is 'S3', 's3_bucket' field must be specified as well"
  }
}

variable "memory_size" {
  description = "Amount of memory in MB allocated to Lambda"
  type        = number
  default     = 128
}

variable "timeout" {
  description = "Function timeout in seconds"
  type        = number
  default     = 3
}

variable "description" {
  description = "Description of the Lambda function"
  type        = string
  default     = ""
}

variable "environment_variables" {
  description = "Map of environment variables"
  type        = map(string)
  default     = {}
}

variable "environment_secrets" {
  type        = map(string)
  description = "Secrets to fetch from AWS SSM and set as environment variables"
  default     = {}
}

variable "vpc_subnet_ids" {
  description = "List of subnet IDs for VPC configuration"
  type        = list(string)
  default     = []
}

variable "vpc_security_group_ids" {
  description = "List of security group IDs for VPC configuration"
  type        = list(string)
  default     = []
}

variable "function_role_path" {
  type    = string
  default = "/service-role/"
}

variable "tags" {
  description = "Map of tags to assign to the resources"
  type        = map(string)
  default     = {}
}

variable "sqs_trigger" {
  type = object({
    queue_arn               = string
    enabled                 = optional(bool, true)
    batch_size              = optional(number)
    maximum_batching_window = optional(number)
    tags                    = optional(map(string), {})
  })
  default = null
}

variable "function_layers" {
  description = "Configuration for Lambda Layer"
  type = map(object({
    layer_name          = string
    description         = string
    compatible_runtimes = list(string)
    s3_key              = string
    s3_bucket           = string
  }))
  default = null
}

variable "route53_zone_id" {
  type    = string
  default = "Z05531162DGNWUXR3CVOO"
}

variable "public_endpoint" {
  type    = bool
  default = false
}

variable "disable_lb" {
  type        = bool
  default     = false
  description = "Whether to disable the Load Balancer integration or not; useful when importing resources."
}

variable "extra_iam_policy_arns" {
  type    = list(string)
  default = []
}

variable "ecr_max_image_count" {
  type        = number
  default     = 5
  description = "The maximum number of images to keep in the ECR repository."
}

variable "cluster" {
  type = object({
    name             = string
    arn              = string
    dns_prefix       = string
    public_listener  = string
    public_cname     = string
    private_listener = string
    private_cname    = string
  })
}

variable "private_network" {
  type = object({
    vpc             = string,
    vpc_name        = string,
    cidr_block      = string,
    subnets         = list(string),
    route_tables    = list(string),
    security_groups = list(string),
    network_acls    = list(string),
    vpc_link        = string,
    rds_subnet      = string
  })
}
