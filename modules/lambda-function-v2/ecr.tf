resource "aws_ecr_repository" "repo" {
  count                = upper(var.function_source.source) == "ECR" ? 1 : 0
  name                 = lower(local.lambda_name)
  image_tag_mutability = "MUTABLE"
  force_delete         = true

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = var.tags
}

# Only one aws_ecr_lifecycle_policy resource can be used with the same ECR repository
resource "aws_ecr_lifecycle_policy" "policy" {
  count       = upper(var.function_source.source) == "ECR" ? 1 : 0
  repository  = aws_ecr_repository.repo[0].name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Retain only the ${var.ecr_max_image_count} most recent images"
        selection = {
          tagStatus   = "any"
          countType   = "imageCountMoreThan"
          countNumber = var.ecr_max_image_count
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

resource "aws_ecr_repository_policy" "repo_policy" {
  count       = upper(var.function_source.source) == "ECR" ? 1 : 0
  repository  = aws_ecr_repository.repo[0].name

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "ECRListReadPolicyForLambda",
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": [
        "ecr:BatchGetImage",
        "ecr:DeleteRepositoryPolicy",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetRepositoryPolicy",
        "ecr:SetRepositoryPolicy"
      ],
      "Condition": {
        "StringLike": {
          "aws:sourceArn": "arn:aws:lambda:us-east-1:${data.aws_caller_identity.current.account_id}:function:*"
        }
      }
    },
    {
      "Sid": "FullAccessForAccountUsers",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "ecr:*"
    }
  ]
}
EOF
}
