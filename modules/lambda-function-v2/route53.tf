resource "aws_route53_record" "api" {
  count = var.disable_lb != true ? 1 : 0

  zone_id = var.route53_zone_id
  name    = local.zone_name
  type    = "CNAME"
  ttl     = "300"
  records = [var.cluster.private_cname]
}

resource "aws_route53_record" "gateway" {
  count   = var.public_endpoint != false ? 1 : 0
  zone_id = var.route53_zone_id
  name    = local.public_zone_name
  type    = "CNAME"
  ttl     = "300"
  records = [var.cluster.public_cname]
}
