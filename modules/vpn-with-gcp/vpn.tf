resource "aws_vpn_connection" "vpn_connection_0" {
  depends_on = [
    aws_customer_gateway.customer_gw
  ]

  transit_gateway_id  = var.transit_gateway_id
  customer_gateway_id = element(aws_customer_gateway.customer_gw, 0).id
  type                = var.gateway.type
  static_routes_only  = false

  tunnel1_inside_cidr   = var.tunnels[0].inside_cidr
  tunnel1_preshared_key = var.tunnels[0].preshared_key

  tunnel2_inside_cidr   = var.tunnels[1].inside_cidr
  tunnel2_preshared_key = var.tunnels[1].preshared_key

  tags = {
    Name = "${var.gateway.name}-vpn-conn-0"
  }
}

resource "aws_vpn_connection" "vpn_connection_1" {
  depends_on = [
    aws_customer_gateway.customer_gw
  ]

  transit_gateway_id  = var.transit_gateway_id
  customer_gateway_id = element(aws_customer_gateway.customer_gw, 1).id
  type                = var.gateway.type
  static_routes_only  = false

  tunnel1_inside_cidr   = var.tunnels[2].inside_cidr
  tunnel1_preshared_key = var.tunnels[2].preshared_key

  tunnel2_inside_cidr   = var.tunnels[3].inside_cidr
  tunnel2_preshared_key = var.tunnels[3].preshared_key

  tags = {
    Name = "${var.gateway.name}-vpn-conn-1"
  }
}

# Customer Gateways (connects to the GCP side)
resource "aws_customer_gateway" "customer_gw" {
  count      = length(var.gateway.external_ips)
  bgp_asn    = var.gateway.asn
  ip_address = element(var.gateway.external_ips, count.index)
  type       = var.gateway.type

  tags = {
    Name = "${var.gateway.name}-customer-gateway-${count.index}"
  }
}
