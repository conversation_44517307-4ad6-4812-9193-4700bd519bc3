<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 gateway  = 
	 transit_gateway_id  = 
	 tunnels  = 
}
```
## Resources

| Name | Type |
|------|------|
| [aws_customer_gateway.customer_gw](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/customer_gateway) | resource |
| [aws_vpn_connection.vpn_connection_0](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpn_connection) | resource |
| [aws_vpn_connection.vpn_connection_1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpn_connection) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_gateway"></a> [gateway](#input\_gateway) | n/a | <pre>object({<br>    name         = string<br>    external_ips = list(string)<br>    asn          = number<br>    type         = string<br>    # vpc_id       = string<br>  })</pre> | n/a | yes |
| <a name="input_transit_gateway_id"></a> [transit\_gateway\_id](#input\_transit\_gateway\_id) | n/a | `string` | n/a | yes |
| <a name="input_tunnels"></a> [tunnels](#input\_tunnels) | n/a | <pre>list(object({<br>    inside_cidr   = string<br>    preshared_key = string<br>  }))</pre> | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_vpn_connection_0"></a> [vpn\_connection\_0](#output\_vpn\_connection\_0) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->