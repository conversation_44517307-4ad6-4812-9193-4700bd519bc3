variable "cluster_name" {
  type = string
}

variable "dns_prefix" {
  type    = string
  default = ""
}

variable "container_insights" {
  type    = bool
  default = false
}

variable "fargate_spot_mode" {
  type    = string
  default = "none"
  validation {
    # can only be "none", "full" or "hybrid"
    condition     = var.fargate_spot_mode == "none" || var.fargate_spot_mode == "full" || var.fargate_spot_mode == "hybrid"
    error_message = "fargate_spot_mode must be one of 'none', 'full' or 'hybrid'"
  }
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "private_network" {
  type = object({
    vpc             = string
    vpc_name        = string
    cidr_block      = string
    subnets         = list(string)
    route_tables    = list(string)
    security_groups = list(string)
    network_acls    = list(string)
  })
}

variable "public_network" {
  type = object({
    vpc             = string
    vpc_name        = string
    cidr_block      = string
    subnets         = list(string)
    route_tables    = list(string)
    security_groups = list(string)
    network_acls    = list(string)
  })
}

variable "auth0_settings" {
  type = object({
    OPENID_CONFIGURATION = string,
    AUDIENCE             = string,
    TOKEN_ISSUER         = string
  })
  description = "This is an object containing JWKS_URI, AUDIENCE and TOKEN_ISSUER for Auth0"
}

variable "ssl_certificate_arn" {
  type    = string
  default = "arn:aws:acm:us-east-1:************:certificate/c5091605-82d5-4ab7-90f7-56c7b92fadd0"
  # this is the *.whykeyway.com ACM cert on the main account
}

variable "idle_timeout" {
  type    = number
  default = 900 # max for lambda
}

variable "gateway_container" {
  type = object({
    cpu      = number
    memory   = number
    replicas = number
  })
  default = {
    cpu      = 512
    memory   = 1024
    replicas = 1
  }
}

variable "waf_enabled" {
  type    = bool
  default = false
}

variable "waf_ratelimit" {
  type    = number
  default = 2000
}
