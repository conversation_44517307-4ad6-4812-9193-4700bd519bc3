# LB: BALANCERS
resource "aws_lb" "private" {
  name               = "${var.cluster_name}-private-lb"
  internal           = true
  load_balancer_type = "application"
  security_groups    = var.private_network.security_groups
  subnets            = var.private_network.subnets

  enable_deletion_protection = false
  idle_timeout               = var.idle_timeout

  access_logs {
    bucket  = aws_s3_bucket.lb-logs.bucket
    prefix  = "private"
    enabled = true
  }

  tags = var.tags
}

resource "aws_lb" "public" {
  name               = "${var.cluster_name}-public-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = var.public_network.security_groups
  subnets            = var.public_network.subnets

  enable_deletion_protection = false
  idle_timeout               = var.idle_timeout

  access_logs {
    bucket  = aws_s3_bucket.lb-logs.bucket
    prefix  = "public"
    enabled = true
  }

  tags = var.tags
}

# LB: LISTENERS
resource "aws_lb_listener" "http" {
  for_each          = tomap({ "private" = aws_lb.private, "public" = aws_lb.public })
  load_balancer_arn = each.value.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_listener" "https" {
  for_each          = tomap({ "private" = aws_lb.private, "public" = aws_lb.public })
  load_balancer_arn = each.value.arn
  port              = "443"
  protocol          = "HTTPS"
  certificate_arn   = var.ssl_certificate_arn

  default_action {
    type = "fixed-response"

    fixed_response {
      content_type = "text/plain"
      message_body = "Invalid request on ${each.value.name}"
      status_code  = "400"
    }
  }
}

# LB: TARGET GROUPS
resource "aws_alb_target_group" "gateway" {
  name        = substr("${local.app_name}-${var.cluster_name}-tg", 0, 32)
  vpc_id      = var.public_network.vpc
  target_type = "ip"
  port        = 8080
  protocol    = "HTTP"

  deregistration_delay = 0

  health_check {
    enabled = true
    path    = "/health"
    matcher = 200

    healthy_threshold = 3
    interval          = 30

    # port = "traffic-port"
    # protocol = "HTTP"
  }

  tags = merge({ "Name" = "${local.app_name}-${var.cluster_name}-tg" }, var.tags)
}

resource "aws_lb_listener_rule" "gateway" {
  listener_arn = aws_lb_listener.https["public"].arn
  depends_on   = [aws_alb_target_group.gateway, aws_lb.public, aws_lb_listener.https]


  condition {
    host_header {
      values = [join(".", compact(["*", var.dns_prefix, "whykeyway.com"]))]
    }
  }

  action {
    type = "forward"
    forward {
      target_group {
        arn = aws_alb_target_group.gateway.arn
      }
    }
  }
}
