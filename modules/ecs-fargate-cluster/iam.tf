resource "aws_iam_role" "ecs_task_execution_role" {
  name = "ecs-task-execution-role-for-${local.app_name}-${var.cluster_name}"
  tags = var.tags

  assume_role_policy = <<EOF
{
 "Version": "2012-10-17",
 "Statement": [
   {
     "Action": "sts:AssumeRole",
     "Principal": {
       "Service": [
         "ecs-tasks.amazonaws.com"
       ]
     },
     "Effect": "Allow",
     "Sid": ""
   }
 ]
}
EOF
}

# TODO: Make the ssm/secrets specific to the app instead of a wildcard
resource "aws_iam_policy" "ecs_task_policies" {
  name        = "ecs-task-execution-policy-for-${local.app_name}-${var.cluster_name}"
  description = "ECS Task Execution Policy for ${local.app_name} (ECS ${var.cluster_name})"
  tags        = var.tags

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "ssm:GetParameters",
          "secretsmanager:GetSecretValue",
          "kms:Decrypt"
        ],
        Effect = "Allow",
        Resource = [
          "arn:aws:secretsmanager:us-east-1:${data.aws_caller_identity.current.account_id}:secret:*",
          "arn:aws:kms:us-east-1:${data.aws_caller_identity.current.account_id}:key/*",
          "arn:aws:ssm:us-east-1:${data.aws_caller_identity.current.account_id}:parameter/*"
        ]
      },
      {
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage"
        ],
        Effect   = "Allow",
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_policy_attachment" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = aws_iam_policy.ecs_task_policies.arn
}

resource "aws_iam_role" "ecs_container_role" {
  name = "ecs_container-role-for-${local.app_name}-${var.cluster_name}"
  tags = var.tags

  assume_role_policy = jsonencode(
    {
      Version = "2012-10-17",
      Statement = [
        {
          Effect = "Allow",
          Sid    = "",
          Principal = {
            Service = [
              "ecs-tasks.amazonaws.com"
            ]
          },
          Action = "sts:AssumeRole",
          Condition = {
            ArnLike = {
              "aws:SourceArn" : "arn:aws:ecs:us-east-1:${data.aws_caller_identity.current.account_id}:*"
            },
            StringEquals = {
              "aws:SourceAccount" : "${data.aws_caller_identity.current.account_id}"
            }
          }
        }
      ]
    }
  )
}

# TODO: Make the ssm/secrets specific to the app instead of a wildcard
resource "aws_iam_policy" "ecs_container_policies" {
  name        = "ecs-container-policy-for-${local.app_name}-${var.cluster_name}"
  description = "ECS Container Policy for ${local.app_name} (ECS ${var.cluster_name})"
  tags        = var.tags

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ssmmessages:CreateControlChannel",
          "ssmmessages:CreateDataChannel",
          "ssmmessages:OpenControlChannel",
          "ssmmessages:OpenDataChannel"
        ],
        Resource = "*"
      }
    ]
    }
  )
}

resource "aws_iam_role_policy_attachment" "ecs_container_policy_attachment" {
  role       = aws_iam_role.ecs_container_role.name
  policy_arn = aws_iam_policy.ecs_container_policies.arn
}
