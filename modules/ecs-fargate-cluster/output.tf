output "cluster" {
  value = tomap({
    "name" : aws_ecs_cluster.cluster.name,
    "arn" : aws_ecs_cluster.cluster.arn,
    "dns_prefix" : var.dns_prefix
    # deprecated
    "lb_logs_bucket" : aws_s3_bucket.lb-logs.id,
    "lb_https_listener" : aws_lb_listener.https["private"].arn,
    # new vars  
    "private_listener" : aws_lb_listener.https["private"].arn,
    "private_cname" : aws_lb.private.dns_name,
    "public_listener" : aws_lb_listener.https["public"].arn,
    "public_cname" : aws_lb.public.dns_name,
  })
}
