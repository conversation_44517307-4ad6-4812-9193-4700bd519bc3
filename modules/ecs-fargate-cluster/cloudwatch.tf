resource "aws_cloudwatch_log_group" "cluster-logs" {
  name = "${var.cluster_name}-ecs-logs"

  retention_in_days = 365
}

resource "aws_cloudwatch_log_group" "container-insights" {

  count = var.container_insights ? 1 : 0
  name  = "/aws/ecs/containerinsights/${var.cluster_name}/performance"

  retention_in_days = 365
}

resource "aws_cloudwatch_log_group" "waf-logs" {
  count             = var.waf_enabled ? 1 : 0
  name              = "aws-waf-logs-${var.cluster_name}"
  retention_in_days = 365
}
