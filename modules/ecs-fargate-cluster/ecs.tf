resource "aws_ecs_cluster" "cluster" {
  name = var.cluster_name

  setting {
    name  = "containerInsights"
    value = var.container_insights ? "enabled" : "disabled"
  }

  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"

      log_configuration {
        cloud_watch_log_group_name = aws_cloudwatch_log_group.cluster-logs.name
      }
    }
  }
}

resource "aws_ecs_cluster_capacity_providers" "providers" {
  cluster_name = aws_ecs_cluster.cluster.name

  # FARGATE can not be removed from the list of capacity providers
  capacity_providers = var.fargate_spot_mode == "none" ? ["FARGATE"] : ["FARGATE", "FARGATE_SPOT"]

  dynamic "default_capacity_provider_strategy" {
    for_each = var.fargate_spot_mode == "hybrid" ? [1] : []
    content {
      capacity_provider = "FARGATE_SPOT"
      weight            = 99
    }
  }

  default_capacity_provider_strategy {
    capacity_provider = var.fargate_spot_mode == "full" ? "FARGATE_SPOT" : "FARGATE"
    weight            = var.fargate_spot_mode == "hybrid" ? 1 : 100
    base              = 1
  }

  lifecycle {
    create_before_destroy = true
  }
}
