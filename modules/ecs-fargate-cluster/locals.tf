data "aws_caller_identity" "current" {}

locals {
  app_name = "nginx-aws-gateway"
  gateway_variables = [
    { name = "REMOTE_HOST", value = aws_lb.private.dns_name },
    { name = "JWT_CACHE_TTL", value = "3600" },
    { name = "OPENID_CONFIGURATION", value = var.auth0_settings.OPENID_CONFIGURATION },
    { name = "AUDIENCE", value = var.auth0_settings.AUDIENCE },
    { name = "TOKEN_ISSUER", value = var.auth0_settings.TOKEN_ISSUER }
  ]
}
