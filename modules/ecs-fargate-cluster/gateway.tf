resource "aws_ecs_task_definition" "task" {
  family                   = "${local.app_name}-task-${var.cluster_name}"
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  cpu                      = var.gateway_container.cpu
  memory                   = var.gateway_container.memory
  skip_destroy             = true

  depends_on = [
    aws_ecr_repository.repo,
    aws_iam_role.ecs_task_execution_role,
    aws_iam_role.ecs_container_role
  ]

  tags = var.tags

  container_definitions = jsonencode(
    [
      {
        name         = "log_router",
        image        = "public.ecr.aws/aws-observability/aws-for-fluent-bit:stable",
        essential    = true,
        user         = "0"
        environment  = []
        mountPoints  = null
        portMappings = []
        volumesFrom  = []
        # Comment this object if you want to switch to Cloudwatch Logs
        firelensConfiguration = {
          type    = "fluentbit",
          options = { "enable-ecs-log-metadata" : "true" }
        }
      },
      {
        name  = "datadog-agent",
        image = "public.ecr.aws/datadog/agent:latest",
        portMappings = [
          {
            hostPort      = 8125,
            protocol      = "udp",
            containerPort = 8125
          },
          {
            hostPort      = 8126,
            protocol      = "tcp",
            containerPort = 8126
          }
        ],
        environment = [
          { name = "ECS_FARGATE", value = "true" },
          { name = "DD_TAGS", value = "env:${var.cluster_name} service:${local.app_name}" },
          { name = "DD_APM_ENABLED", value = "true" },
          { name = "DD_APM_NON_LOCAL_TRAFFIC", value = "true" },
          { name = "DD_USE_DOGSTATSD", value = "true" },
          { name = "DD_DOGSTATSD_NON_LOCAL_TRAFFIC", value = "true" },
          { name = "DD_AC_EXCLUDE", value = "name:.*" },
          { name = "DD_AC_INCLUDE", value = "name:${local.app_name}" },
          { name = "DD_CONTAINER_EXCLUDE", value = "name:.*" },
          { name = "DD_CONTAINER_INCLUDE", value = "name:${local.app_name}" },
          { name = "DD_APM_IGNORE_RESOURCES", value = "GET /health" },
          { name = "DD_LOGS_INJECTION", value = "true" },
          { name = "DD_DBM_PROPAGATION_MODE", value = "full" }
        ],
        mountPoints = [],
        volumesFrom = [],
        essential   = true,
        secrets = [
          { name = "DD_API_KEY", valueFrom = "arn:aws:ssm:us-east-1:${data.aws_caller_identity.current.account_id}:parameter/datadog_api_key" }
        ],
        healthCheck = {
          retries = 3,
          command = [
            "CMD-SHELL",
            "agent health"
          ],
          timeout     = 5,
          interval    = 30,
          startPeriod = 15
        }
      },
      {
        name  = local.app_name,
        image = "${aws_ecr_repository.repo.repository_url}:latest",
        environment = flatten([local.gateway_variables,
          [
            { name = "DD_ENV", value = var.cluster_name },
            { name = "DD_SERVICE", value = local.app_name },
            { name = "DD_VERSION", value = "latest" },
            { name = "DD_TRACE_SAMPLE_RATE", value = "1" },
            { name = "DD_APM_IGNORE_RESOURCES", value = "GET /health" }
        ]]),
        dockerLabels = {
          "com.datadoghq.tags.env" : var.cluster_name,
          "com.datadoghq.tags.service" : local.app_name,
          "com.datadoghq.tags.version" : "latest"
        },
        mountPoints = [],
        volumesFrom = [],
        essential   = true,
        portMappings = [
          {
            containerPort = 8080,
            hostPort      = 8080,
            protocol      = "tcp"
          }
        ],
        dependsOn = [
          { containerName = "datadog-agent", condition = "HEALTHY" }
        ],
        secrets = [],
        # Uncomment this logConfiguration to return to CloudWatch
        # logConfiguration = {
        #   logDriver = "awslogs",
        #   options = {
        #     "awslogs-group" : "${local.cluster_name}-ecs-logs",
        #     "awslogs-region" : "us-east-1",
        #     "awslogs-stream-prefix" : "${local.app_name}"
        #   }
        # }
        # Comment this logConfiguration if you want to disable Datadog Logs
        logConfiguration = {
          logDriver = "awsfirelens",
          options = {
            Name           = "datadog",
            Host           = "http-intake.logs.datadoghq.com",
            dd_service     = local.app_name,
            dd_source      = local.app_name,
            dd_message_key = "log",
            dd_tags        = "env:${var.cluster_name} service:${local.app_name}",
            TLS            = "on",
            provider       = "ecs"
          },
          secretOptions = [
            { name = "apikey", valueFrom = "arn:aws:ssm:us-east-1:${data.aws_caller_identity.current.account_id}:parameter/datadog_api_key" }
          ]
        }
      }
    ]
  )

  task_role_arn      = aws_iam_role.ecs_container_role.arn
  execution_role_arn = aws_iam_role.ecs_task_execution_role.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64" # TODO: switch to ARM64?
  }
}

# ECS: SERVICE
resource "aws_ecs_service" "service" {
  depends_on = [
    aws_iam_role.ecs_task_execution_role,
    aws_iam_policy.ecs_task_policies,
    aws_ecs_task_definition.task,
    aws_alb_target_group.gateway
  ]

  name            = "${local.app_name}-service"
  cluster         = aws_ecs_cluster.cluster.arn
  task_definition = aws_ecs_task_definition.task.arn
  desired_count   = var.gateway_container.replicas
  propagate_tags  = "SERVICE"
  tags            = var.tags

  health_check_grace_period_seconds = 10
  enable_execute_command            = true

  deployment_controller {
    type = "ECS"
  }

  network_configuration {
    subnets          = var.private_network.subnets
    security_groups  = var.private_network.security_groups
    assign_public_ip = false
  }

  load_balancer {
    target_group_arn = aws_alb_target_group.gateway.arn
    container_name   = local.app_name
    container_port   = 8080
  }

  lifecycle {
    ignore_changes = [desired_count, capacity_provider_strategy, deployment_circuit_breaker]
  }
}
