resource "aws_wafv2_web_acl" "waf-web-acl" {
  count = var.waf_enabled ? 1 : 0
  name  = "${var.cluster_name}-wafv2-web-acl"
  scope = "REGIONAL"

  default_action {
    allow {
    }
  }

  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 0
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        vendor_name = "AWS"
        name        = "AWSManagedRulesCommonRuleSet"

        # Exclude the query string size rule (demographics)
        rule_action_override {
          action_to_use {
            allow {}
          }
          name = "SizeRestrictions_QUERYSTRING"
        }

        # Exclude the body size rule (data-gpt)
        rule_action_override {
          action_to_use {
            allow {}
          }
          name = "SizeRestrictions_BODY"
        }
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWSManagedRulesKnownBadInputsRuleSet"
    priority = 1
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        vendor_name = "AWS"
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "KnownBadInputs"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWSManagedRulesAmazonIpReputationList"
    priority = 2
    override_action {
      none {}
    }
    statement {
      managed_rule_group_statement {
        vendor_name = "AWS"
        name        = "AWSManagedRulesAmazonIpReputationList"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "IpReputation"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "RateLimit"
    priority = 3

    action {
      count {}
      # block {}
    }

    statement {
      rate_based_statement {
        aggregate_key_type = "CUSTOM_KEYS"
        limit              = var.waf_ratelimit

        custom_key {
          header {
            name = "Authorization"
            text_transformation {
              priority = 0
              type     = "MD5"
            }
          }
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimitRule"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "WebACL"
    sampled_requests_enabled   = true
  }

  tags = var.tags
}

resource "aws_wafv2_web_acl_logging_configuration" "waf-logs" {
  count                   = var.waf_enabled ? 1 : 0
  log_destination_configs = [aws_cloudwatch_log_group.waf-logs[0].arn]
  resource_arn            = aws_wafv2_web_acl.waf-web-acl[0].arn

  logging_filter {
    default_behavior = "DROP"

    filter {
      behavior    = "KEEP"
      requirement = "MEETS_ANY"

      condition {
        action_condition { action = "COUNT" }
      }

      condition {
        action_condition { action = "BLOCK" }
      }
    }
  }

  depends_on = [
    aws_wafv2_web_acl.waf-web-acl[0],
    aws_cloudwatch_log_group.waf-logs[0]
  ]
}

resource "aws_wafv2_web_acl_association" "waf-association" {
  count        = var.waf_enabled ? 1 : 0
  resource_arn = aws_lb.public.arn
  web_acl_arn  = aws_wafv2_web_acl.waf-web-acl[0].arn
  depends_on = [
    aws_wafv2_web_acl.waf-web-acl[0],
    aws_cloudwatch_log_group.waf-logs[0]
  ]
}
