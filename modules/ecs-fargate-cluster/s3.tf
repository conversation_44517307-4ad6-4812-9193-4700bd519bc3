data "aws_canonical_user_id" "current" {}

resource "aws_s3_bucket" "lb-logs" {
  bucket        = "${var.cluster_name}-lb-logs-keyway"
  force_destroy = true

  tags = merge(var.tags, {
    Name    = "${var.cluster_name}-lb-logs-keyway"
    Cluster = var.cluster_name
  })
}

resource "aws_s3_bucket_public_access_block" "public-access" {
  bucket = aws_s3_bucket.lb-logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_ownership_controls" "ownership_controls" {
  bucket = aws_s3_bucket.lb-logs.id

  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.lb-logs.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowFullControlToAccountUsers",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        },
        Action = "s3:*",
        Resource = [
          "${aws_s3_bucket.lb-logs.arn}",
          "${aws_s3_bucket.lb-logs.arn}/*"
        ]
      },
      {
        Sid    = "AllowLogDelivery",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::************:root"
        },
        Action = [
          "s3:PutObject"
        ],
        Resource = "${aws_s3_bucket.lb-logs.arn}/*",
      }
    ]
  })
}

# Only one aws_s3_bucket_lifecycle_configuration can be associated with a bucket
resource "aws_s3_bucket_lifecycle_configuration" "lifecycle" {
  bucket = aws_s3_bucket.lb-logs.id

  rule {
    id     = "remove_old_files"
    status = "Enabled"

    expiration {
      days = 14
    }

    filter { prefix = "" }
  }
}

resource "aws_s3_bucket_versioning" "lb-logs" {
  bucket = aws_s3_bucket.lb-logs.id
  versioning_configuration {
    status = "Enabled"
  }
}
