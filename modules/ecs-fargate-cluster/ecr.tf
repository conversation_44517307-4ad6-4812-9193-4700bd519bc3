resource "aws_ecr_repository" "repo" {
  name                 = "${local.app_name}-${var.public_network.vpc_name}"
  image_tag_mutability = "MUTABLE"
  force_delete         = true

  image_scanning_configuration {
    scan_on_push = false
  }

  tags = var.tags
}

# Only one aws_ecr_lifecycle_policy resource can be used with the same ECR repository
resource "aws_ecr_lifecycle_policy" "policy" {
  repository = aws_ecr_repository.repo.name

  policy = <<EOF
{
  "rules":[
    {
      "rulePriority": 1,
      "description": "Retain only the 5 most recent images",
      "selection": {
        "tagStatus": "any",
        "countType": "imageCountMoreThan",
        "countNumber": 5
      },
      "action": {
        "type": "expire"
      }
    }
  ]
}
EOF
}

resource "aws_ecr_repository_policy" "repo_policy" {
  repository = aws_ecr_repository.repo.name

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "ECRListReadPolicyForECSTasks",
      "Effect": "Allow",
      "Principal": {
        "AWS": "${aws_iam_role.ecs_task_execution_role.arn}"
      },
      "Action": [
        "ecr:BatchGetImage",
        "ecr:GetDownloadUrlForLayer",
        "ecr:DescribeRepositories",
        "ecr:ListImages",
        "ecr:DescribeImages",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetAuthorizationToken"
      ]
    },
    {
      "Sid": "FullAccessForAccountUsers",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "ecr:*"
    }
  ]
}
EOF
}
