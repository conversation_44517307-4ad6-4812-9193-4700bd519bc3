resource "aws_backup_plan" "plan" {
  name = "${var.vault}-plan"

  rule {
    rule_name         = "${var.vault}-rule"
    target_vault_name = aws_backup_vault.vault.name
    schedule          = var.schedule

    lifecycle {
      delete_after = var.retention_days
    }
  }

  #  advanced_backup_setting {
  #    backup_options = {
  #      WindowsVSS = "enabled"
  #    }
  #    resource_type = "EC2"
  #  }
}
