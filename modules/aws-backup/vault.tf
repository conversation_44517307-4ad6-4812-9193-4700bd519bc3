data "aws_caller_identity" "current" {}

resource "aws_backup_vault" "vault" {
  name = var.vault
  tags = merge(var.tags, {
    Name = "${var.vault}"
  })
}

resource "aws_backup_vault_policy" "policy" {
  backup_vault_name = aws_backup_vault.vault.name

  policy = jsonencode(
    {
      Version = "2012-10-17",
      Id      = "default",
      Statement = [
        {
          Sid    = "default",
          Effect = "Allow",
          Principal = {
            AWS = [
              "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
            ]
          },
          Action = [
            "backup:DescribeBackupVault",
            "backup:DeleteBackupVault",
            "backup:PutBackupVaultAccessPolicy",
            "backup:DeleteBackupVaultAccessPolicy",
            "backup:GetBackupVaultAccessPolicy",
            "backup:StartBackupJob",
            "backup:GetBackupVaultNotifications",
            "backup:PutBackupVaultNotifications"
          ],
          Resource = "${aws_backup_vault.vault.arn}"
        }
      ]
    }
  )
}

# Not supported by <PERSON><PERSON><PERSON> (yet) at Sep 22
# 
#resource "aws_backup_vault_notifications" "notifications" {
#  depends_on = [
#    aws_backup_vault.vault
#  ]
#  backup_vault_name   = aws_backup_vault.vault.name
#  sns_topic_arn       = var.sns_topic_arn
#  backup_vault_events = ["BACKUP_JOB_STARTED", "BACKUP_JOB_COMPLETED", "COPY_JOB_STARTED", "COPY_JOB_SUCCESSFUL", "COPY_JOB_FAILED", "RESTORE_JOB_STARTED", "RESTORE_JOB_COMPLETED"]
#}
