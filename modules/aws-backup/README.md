<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 vault  = 

	 # Optional variables
	 account_id  = "************"
	 resources_arn  = []
	 retention_days  = 7
	 schedule  = "cron(0 6 * * ? *)"
	 slack_email  = "<EMAIL>"
	 sns_topic_arn  = "arn:aws:sns:us-east-1:************:notify-backups-sns"
}
```
## Resources

| Name | Type |
|------|------|
| [aws_backup_plan.plan](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/backup_plan) | resource |
| [aws_backup_selection.selection](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/backup_selection) | resource |
| [aws_backup_vault.vault](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/backup_vault) | resource |
| [aws_backup_vault_policy.policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/backup_vault_policy) | resource |
| [aws_iam_role.role](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.iam_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.iam_attachment_1](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.iam_attachment_2](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.iam_attachment_3](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_account_id"></a> [account\_id](#input\_account\_id) | n/a | `string` | `"************"` | no |
| <a name="input_resources_arn"></a> [resources\_arn](#input\_resources\_arn) | n/a | `list(string)` | `[]` | no |
| <a name="input_retention_days"></a> [retention\_days](#input\_retention\_days) | n/a | `number` | `7` | no |
| <a name="input_schedule"></a> [schedule](#input\_schedule) | n/a | `string` | `"cron(0 6 * * ? *)"` | no |
| <a name="input_slack_email"></a> [slack\_email](#input\_slack\_email) | n/a | `string` | `"<EMAIL>"` | no |
| <a name="input_sns_topic_arn"></a> [sns\_topic\_arn](#input\_sns\_topic\_arn) | n/a | `string` | `"arn:aws:sns:us-east-1:************:notify-backups-sns"` | no |
| <a name="input_vault"></a> [vault](#input\_vault) | n/a | `string` | n/a | yes |
## Outputs

No outputs.
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->