variable "vault" {
  type = string
}

variable "resources_arn" {
  type    = list(string)
  default = []
}

variable "schedule" {
  type    = string
  default = "cron(0 6 * * ? *)"
}

variable "slack_email" {
  type    = string
  default = "<EMAIL>"
}

variable "retention_days" {
  type    = number
  default = 7
}

variable "sns_topic_arn" {
  type    = string
  default = "arn:aws:sns:us-east-1:681574592108:notify-backups-sns"
}

variable "tags" {
  type    = map(string)
  default = {}
}
