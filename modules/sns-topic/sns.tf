resource "aws_sns_topic" "sns" {
  name                        = "${var.sns_topic_name}${var.fifo_topic == true ? ".fifo" : ""}"
  tags                        = var.tags
  fifo_topic                  = var.fifo_topic
  content_based_deduplication = var.fifo_topic

  policy = jsonencode({
    Version : "2008-10-17",
    Id : "__default_policy_ID",
    Statement : [
      {
        Sid : "__default_statement_ID",
        Effect : "Allow",
        Principal : {
          AWS : "*"
        },
        Action : [
          "SNS:GetTopicAttributes",
          "SNS:SetTopicAttributes",
          "SNS:AddPermission",
          "SNS:RemovePermission",
          "SNS:DeleteTopic",
          "SNS:Subscribe",
          "SNS:ListSubscriptionsByTopic",
          "SNS:Publish"
        ],
        Resource : "arn:aws:sns:us-east-1:${data.aws_caller_identity.current.account_id}:${var.sns_topic_name}",
        Condition : {
          StringEquals : {
            "AWS:SourceOwner" : "${data.aws_caller_identity.current.account_id}"
          }
        }
      }
    ]
  })
}

resource "aws_sns_topic_subscription" "sns-to-sqs" {
  count     = length(var.sqs_subscriptions)
  topic_arn = aws_sns_topic.sns.arn
  protocol  = "sqs"
  endpoint  = var.sqs_subscriptions[count.index]
}

resource "aws_sns_topic_subscription" "raw_sns-to-sqs" {
  count                = length(var.raw_sqs_subscriptions)
  topic_arn            = aws_sns_topic.sns.arn
  protocol             = "sqs"
  endpoint             = var.raw_sqs_subscriptions[count.index]
  raw_message_delivery = true
}

resource "aws_sns_topic_subscription" "filtered_sns_to_sqs" {
  count                = length(var.filtered_sqs_subscriptions)
  topic_arn            = aws_sns_topic.sns.arn
  protocol             = "sqs"
  endpoint             = var.filtered_sqs_subscriptions[count.index].sqs_arn
  raw_message_delivery = var.filtered_sqs_subscriptions[count.index].raw_message_delivery
  filter_policy        = jsonencode(var.filtered_sqs_subscriptions[count.index].filter_policy)
  filter_policy_scope  = var.filtered_sqs_subscriptions[count.index].filter_policy_scope
}
