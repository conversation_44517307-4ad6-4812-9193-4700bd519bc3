variable "sns_topic_name" {
  type = string
}

variable "sqs_subscriptions" {
  type    = list(string)
  default = []
}

variable "raw_sqs_subscriptions" {
  type    = list(string)
  default = []
}

variable "fifo_topic" {
  type    = bool
  default = false
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "filtered_sqs_subscriptions" {
  type = list(object({
    sqs_arn              = string
    filter_policy        = optional(map(list(string)))
    filter_policy_scope  = optional(string, "MessageAttributes")
    raw_message_delivery = bool
  }))
  description = "List of SQS ARNs with filter policies to subscribe to the SNS topic"
  default     = []
}
