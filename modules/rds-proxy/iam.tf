resource "aws_iam_policy" "secrets_manager_policy" {
  name        = "${var.name}-policy"
  description = "Policy to allow RDS Proxy to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ],
        Resource = aws_secretsmanager_secret.proxy_login.arn
      }
    ]
  })
}

resource "aws_iam_role" "rds_proxy_role" {
  name = "${var.name}-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "rds.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "secrets_manager_policy_attachment" {
  role       = "${var.name}-role"
  policy_arn = aws_iam_policy.secrets_manager_policy.arn
}
