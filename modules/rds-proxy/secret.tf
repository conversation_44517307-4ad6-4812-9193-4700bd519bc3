resource "aws_secretsmanager_secret" "proxy_login" {
  name        = "${var.parameter_prefix}/${var.name}-login"
  description = "Credentials for the database proxy"
}

resource "aws_secretsmanager_secret_version" "proxy_login" {
  secret_id     = aws_secretsmanager_secret.proxy_login.id
  secret_string = <<EOF
  {
    "username": "${data.aws_ssm_parameter.username.value}",
    "password": "${data.aws_ssm_parameter.password.value}"
  }
  EOF
}
