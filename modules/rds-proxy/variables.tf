variable "name" {
  description = "The name of the proxy"
  type        = string
}

variable "engine_family" {
  description = "The engine family of the proxy"
  type        = string
  default     = "POSTGRESQL"
}

variable "parameter_prefix" {
  description = "The prefix for the SSM parameters"
  type        = string
}

variable "rds_network" {
  type = object({
    subnets         = list(string)
    security_groups = list(string)
  })
}

variable "db_instance_identifier" {
  description = "The RDS instance identifier"
  type        = string
}

variable "idle_client_timeout" {
  description = "The idle client timeout"
  type        = number
  default     = 28800
}

variable "max_connections_percent" {
  description = "The pool size"
  type        = number
  default     = 75
}

variable "max_idle_connections_percent" {
  description = "The pool size"
  type        = number
  default     = 50
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
