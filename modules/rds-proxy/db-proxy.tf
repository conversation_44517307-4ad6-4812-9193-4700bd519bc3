resource "aws_db_proxy" "proxy" {
  name                   = var.name
  debug_logging          = false
  engine_family          = var.engine_family
  idle_client_timeout    = var.idle_client_timeout
  require_tls            = true
  role_arn               = aws_iam_role.rds_proxy_role.arn
  vpc_security_group_ids = var.rds_network.security_groups
  vpc_subnet_ids         = var.rds_network.subnets

  auth {
    auth_scheme = "SECRETS"
    description = "db-root password"
    iam_auth    = "DISABLED"
    secret_arn  = aws_secretsmanager_secret.proxy_login.arn
  }

  tags = var.tags
}

resource "aws_db_proxy_default_target_group" "tg" {
  db_proxy_name = aws_db_proxy.proxy.name

  connection_pool_config {
    connection_borrow_timeout    = 120
    init_query                   = "SELECT NULL"
    max_connections_percent      = var.max_connections_percent
    max_idle_connections_percent = var.max_idle_connections_percent
    session_pinning_filters      = ["EXCLUDE_VARIABLE_SETS"]
  }
}

resource "aws_db_proxy_target" "proxy" {
  db_instance_identifier = var.db_instance_identifier
  db_proxy_name          = aws_db_proxy.proxy.name
  target_group_name      = aws_db_proxy_default_target_group.tg.name
}
