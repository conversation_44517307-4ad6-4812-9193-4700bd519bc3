<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 queue_name  = 

	 # Optional variables
	 delay_seconds  = 0
	 max_message_size  = 262144
	 max_receive_count  = 10
	 message_retention_seconds  = 345600
	 receive_wait_time_seconds  = 0
	 server_side_encryption  = false
	 visibility_timeout_seconds  = 30
}
```
## Resources

| Name | Type |
|------|------|
| [aws_sqs_queue.queue](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sqs_queue) | resource |
| [aws_sqs_queue.queue-dlq](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sqs_queue) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_delay_seconds"></a> [delay\_seconds](#input\_delay\_seconds) | n/a | `number` | `0` | no |
| <a name="input_max_message_size"></a> [max\_message\_size](#input\_max\_message\_size) | From 1024 bytes (1 KiB) up to 262144 bytes (256 KiB). The default is 262144 (256 KiB). | `number` | `262144` | no |
| <a name="input_max_receive_count"></a> [max\_receive\_count](#input\_max\_receive\_count) | n/a | `number` | `10` | no |
| <a name="input_message_retention_seconds"></a> [message\_retention\_seconds](#input\_message\_retention\_seconds) | From 60 (1 minute) to 1209600 (14 days). The default is 345600 (4 days). | `number` | `345600` | no |
| <a name="input_queue_name"></a> [queue\_name](#input\_queue\_name) | n/a | `string` | n/a | yes |
| <a name="input_receive_wait_time_seconds"></a> [receive\_wait\_time\_seconds](#input\_receive\_wait\_time\_seconds) | n/a | `number` | `0` | no |
| <a name="input_server_side_encryption"></a> [server\_side\_encryption](#input\_server\_side\_encryption) | Boolean to enable server-side encryption (SSE) of message content with SQS-owned encryption keys | `bool` | `false` | no |
| <a name="input_visibility_timeout_seconds"></a> [visibility\_timeout\_seconds](#input\_visibility\_timeout\_seconds) | From 0 (seconds) to 43200 (12 hours). The default is 30. | `number` | `30` | no |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_arn"></a> [arn](#output\_arn) | n/a |
| <a name="output_sqs"></a> [sqs](#output\_sqs) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->