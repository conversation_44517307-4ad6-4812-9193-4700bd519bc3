resource "aws_sqs_queue" "queue" {
  depends_on                        = [aws_sqs_queue.queue-dlq]
  content_based_deduplication       = var.fifo_queue
  delay_seconds                     = var.delay_seconds
  fifo_queue                        = var.fifo_queue
  kms_data_key_reuse_period_seconds = "300"
  max_message_size                  = var.max_message_size
  message_retention_seconds         = var.message_retention_seconds
  name                              = "${var.queue_name}${var.fifo_queue == true ? ".fifo" : ""}"
  tags                              = var.tags

  policy = jsonencode({
    Id = "__default_policy_ID",
    Statement = concat([
      {
        Action = "sqs:*",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        },
        Resource = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}${var.fifo_queue == true ? ".fifo" : ""}",
        Sid      = "__owner_statement"
      },
      {
        Action    = "sqs:*",
        Effect    = "Allow",
        Principal = { Service = "sqs.amazonaws.com" },
        Resource  = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}${var.fifo_queue == true ? ".fifo" : ""}",
        Condition = { ArnLike = { "aws:SourceArn" = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:*" } },
        Sid       = "sqs-self-account"
      },
      {
        Action    = "sqs:*", Effect = "Allow",
        Principal = { Service = "sns.amazonaws.com" },
        Resource  = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}${var.fifo_queue == true ? ".fifo" : ""}",
        Condition = { ArnLike = { "aws:SourceArn" = "arn:aws:sns:us-east-1:${data.aws_caller_identity.current.account_id}:*" } },
        Sid       = "sns-self-account"
      }
    ], var.extra_statements),
    Version = "2008-10-17"
  })

  receive_wait_time_seconds  = var.receive_wait_time_seconds
  redrive_policy             = jsonencode({ deadLetterTargetArn = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}-DLQ${var.fifo_queue == true ? ".fifo" : ""}", maxReceiveCount = var.max_receive_count })
  sqs_managed_sse_enabled    = var.server_side_encryption
  visibility_timeout_seconds = var.visibility_timeout_seconds
}

resource "aws_sqs_queue" "queue-dlq" {
  content_based_deduplication       = var.fifo_queue
  delay_seconds                     = "0"
  fifo_queue                        = var.fifo_queue
  kms_data_key_reuse_period_seconds = "300"
  max_message_size                  = var.max_message_size
  message_retention_seconds         = var.message_retention_seconds
  name                              = "${var.queue_name}-DLQ${var.fifo_queue == true ? ".fifo" : ""}"
  tags                              = var.tags

  policy = jsonencode({
    Id = "__default_policy_ID",
    Statement = [
      {
        Action = "sqs:*",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        },
        Resource = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}-DLQ${var.fifo_queue == true ? ".fifo" : ""}",
        Sid      = "__owner_statement"
      },
      {
        Action    = "sqs:SendMessage",
        Effect    = "Allow",
        Principal = { Service = "sqs.amazonaws.com" },
        Resource  = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}-DLQ${var.fifo_queue == true ? ".fifo" : ""}",
        Condition = { ArnLike = { "aws:SourceArn" = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:*" } },
        Sid       = "sqs-self-account"
      },
      {
        Action    = "sqs:SendMessage",
        Effect    = "Allow",
        Principal = { Service = "sns.amazonaws.com" },
        Resource  = "arn:aws:sqs:us-east-1:${data.aws_caller_identity.current.account_id}:${var.queue_name}-DLQ${var.fifo_queue == true ? ".fifo" : ""}",
        Condition = { ArnLike = { "aws:SourceArn" = "arn:aws:sns:us-east-1:${data.aws_caller_identity.current.account_id}:*" } },
        Sid       = "sns-self-account"
      }
    ],
    Version = "2008-10-17"
  })

  receive_wait_time_seconds  = "0"
  sqs_managed_sse_enabled    = var.server_side_encryption
  visibility_timeout_seconds = var.visibility_timeout_seconds
}
