variable "queue_name" {
  type = string
}

# From 1024 bytes (1 KiB) up to 262144 bytes (256 KiB). The default is 262144 (256 KiB).
variable "max_message_size" {
  type    = number
  default = 262144
}

# From 60 (1 minute) to 1209600 (14 days). The default is 345600 (4 days).
variable "message_retention_seconds" {
  type    = number
  default = 345600
}

# Boolean to enable server-side encryption (SSE) of message content with SQS-owned encryption keys
variable "server_side_encryption" {
  type    = bool
  default = false
}

# From 0 (seconds) to 43200 (12 hours). The default is 30.
variable "visibility_timeout_seconds" {
  type    = number
  default = 30
}

variable "fifo_queue" {
  type    = bool
  default = false
}

variable "delay_seconds" {
  type    = number
  default = 0
}

variable "receive_wait_time_seconds" {
  type    = number
  default = 0
}

variable "max_receive_count" {
  type    = number
  default = 10
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "extra_statements" {
  type = list(object({
    Action    = string,
    Effect    = string,
    Principal = map(string),
    Resource  = string,
    Condition = map(map(string)),
    Sid       = string,
  }))
  default = []
}
