data "aws_ssoadmin_instances" "this" {}

resource "aws_ssoadmin_permission_set" "this" {
  for_each = { for ps in var.permission_sets : ps.name => ps }

  name             = each.value.name
  description      = each.value.description
  session_duration = each.value.session_duration
  instance_arn     = tolist(data.aws_ssoadmin_instances.this.arns)[0]
}

resource "aws_ssoadmin_managed_policy_attachment" "aws_policies" {
  for_each = {
    for ps in flatten([
      for permission_set in var.permission_sets : [
        for policy in permission_set.aws_policies :
        {
          permission_set_name = permission_set.name,
          policy_arn          = policy
        }
      ] if length(permission_set.aws_policies) > 0
    ]) : "${ps.permission_set_name}-${ps.policy_arn}" => ps
  }

  instance_arn       = tolist(data.aws_ssoadmin_instances.this.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.this[each.value.permission_set_name].arn
  managed_policy_arn = each.value.policy_arn
}

resource "aws_ssoadmin_customer_managed_policy_attachment" "customer_policies" {
  for_each = {
    for ps in flatten([
      for permission_set in var.permission_sets : [
        for policy in permission_set.customer_policies :
        {
          permission_set_name = permission_set.name,
          policy_arn          = policy
        }
      ] if length(permission_set.customer_policies) > 0
    ]) : "${ps.permission_set_name}-${ps.policy_arn}" => ps
  }

  instance_arn       = tolist(data.aws_ssoadmin_instances.this.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.this[each.value.permission_set_name].arn
  customer_managed_policy_reference {
    name = split("/", each.value.policy_arn)[1]
  }
}

resource "aws_ssoadmin_permission_set_inline_policy" "inline_policies" {
  for_each = {
    for ps in var.permission_sets : ps.name => ps
    if ps.inline_policies != ""
  }

  instance_arn       = tolist(data.aws_ssoadmin_instances.this.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.this[each.value.name].arn
  inline_policy      = each.value.inline_policies
}

resource "aws_ssoadmin_permissions_boundary_attachment" "boundary_policies" {
  for_each = {
    for ps in flatten([
      for permission_set in var.permission_sets : [
        for policy in permission_set.permission_boundaries :
        {
          permission_set_name = permission_set.name,
          policy_arn          = policy
        }
      ] if length(permission_set.permission_boundaries) > 0
    ]) : "${ps.permission_set_name}-${ps.policy_arn}" => ps
  }

  dynamic "permissions_boundary" {
    for_each = [
      for permission_set in var.permission_sets : [
        for policy in permission_set.permission_boundaries : policy
      ] if length(permission_set.permission_boundaries) > 0
        && permission_set.name == each.value.permission_set_name
    ]
    content {
      managed_policy_arn = element(split(",", each.value.policy_arn), length(split(",", each.value.policy_arn)) - 1)
    }
  }

  instance_arn       = tolist(data.aws_ssoadmin_instances.this.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.this[each.value.permission_set_name].arn
}
