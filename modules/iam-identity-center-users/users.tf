locals {
  users = {
    for user in module.list_ssoadmin_users.result.Users : user.UserName => user.UserId
  }
}

module "list_ssoadmin_users" {
  source   = "digitickets/cli/aws"
  aws_cli_commands = [
    "identitystore",
    "list-users",
    "--identity-store-id",
    tolist(data.aws_ssoadmin_instances.this.identity_store_ids)[0]
  ]
}

resource "aws_ssoadmin_account_assignment" "user_assignment" {
  for_each = { for ua in flatten([
    for assignment in var.user_assignments : [
      for ps in assignment.permission_sets : {
        user = assignment.user
        permission_set = ps
      }
    ]
  ]) : "${ua.permission_set}/${ua.user}" => ua }

  instance_arn       = tolist(data.aws_ssoadmin_instances.this.arns)[0]
  permission_set_arn = aws_ssoadmin_permission_set.this[each.value.permission_set].arn

  principal_id       = local.users[each.value.user]
  principal_type     = "USER"

  target_id          = "************"
  target_type        = "AWS_ACCOUNT"
}
