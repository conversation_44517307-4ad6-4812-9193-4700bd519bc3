variable "user_assignments" {
  type = list(object({
    user            = string
    permission_sets  = list(string)
  }))
  description = "List of user and permission set assignments"
}

variable "permission_sets" {
  type = list(object({
    name                  = string
    description           = optional(string, "Permission Set created by Terraform")
    session_duration      = optional(string, "PT8H")
    aws_policies          = optional(list(string), [])
    customer_policies     = optional(list(string), [])
    permission_boundaries = optional(list(string), [])
    inline_policies       = optional(string, "")
  }))
  description = "JSONs for each permission set's policies, be it AWS or customer managed, or just inline policies"
}
