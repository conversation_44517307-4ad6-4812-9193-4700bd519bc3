variable "table_name" {
  type = string
}

variable "capacity_mode" {
  type = string
  default = "PAY_PER_REQUEST"
  validation {
    condition = upper(var.capacity_mode) == "PAY_PER_REQUEST" || upper(var.capacity_mode) == "PROVISIONED" || upper(var.capacity_mode) == "ON_DEMAND"
    error_message = "Variable 'capacity_mode' must be either 'PROVISIONED', or 'PAY_PER_REQUEST/ON_DEMAND'"
  }
}

variable "hash_key" {
  type = string
}

variable "range_key" {
  type    = string
  default = null
}

variable "global_indexes" {
  type = list(object({
    name = string
    projection_type = string
    hash_key = string
    range_key = optional(string, null)
    read_capacity = number
    write_capacity = number
    non_key_attributes = optional(list(string), null)
    on_demand_throughput = optional(object({
      read_units = number
      write_units = number
    }), null)
  }))
  default = []
  description = "List of GLOBAL Secondary Indexes for the table."
}

variable "local_indexes" {
  type = list(object({
    name = string
    projection_type = string
    range_key = string
    non_key_attributes = optional(list(string), null)
  }))
  default = null
  description = "List of LOCAL Secondary Indexes for the table."
}

variable "attributes" {
  type = map(string)
}

variable "ttl_attribute" {
  type        = string
  default     = null
  description = <<-EOT
  When using Time to Live in DynamoDB, this variable will specify the
  name of the table where you'll store the table's TTL expiration
  timestamp in
  EOT
}

variable "on_demand_throughput" {
  type = object({
    read = number
    write = number
  })
  default = null
  description = "Number of max read and write request units when using On-Demand capacity mode."
}

variable "point_in_type_recovery" {
  type    = bool
  default = true
}

variable "deletion_protection" {
  type    = bool
  default = true
}

variable "default_capacity" {
  type = object({
    read  = number
    write = number
  })
  default = null
}

variable "read_autoscaling" {
  type = object({
    enabled = optional(bool, null)  # 'enabled' arg is now deprecated and remains for retrocompatibility
    min     = number
    max     = number
  })
  default = {
    enabled = false
    min     = 1
    max     = 2
  }
  validation {
    condition     = var.read_autoscaling.min < var.read_autoscaling.max
    error_message = "'min' value must be lower than 'max', and they can't be equals"
  }
}

variable "write_autoscaling" {
  type = object({
    enabled = optional(bool, null)  # 'enabled' arg is now deprecated and remains for retrocompatibility
    min     = number
    max     = number
  })
  default = {
    enabled = false
    min     = 1
    max     = 2
  }
  validation {
    condition     = var.write_autoscaling.min < var.write_autoscaling.max
    error_message = "'min' value must be lower than 'max', and they can't be equals"
  }
}

variable "tags" {
  type    = map(string)
  default = {}
}
