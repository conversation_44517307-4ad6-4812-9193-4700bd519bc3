locals {
  read_autoscaling = {
    enabled = var.read_autoscaling.enabled == null && (var.read_autoscaling.min != null && var.read_autoscaling.max != null) || var.read_autoscaling.enabled == true ? true : false
    max = var.read_autoscaling.max
    min = var.read_autoscaling.min
  }
  write_autoscaling = {
    enabled = var.write_autoscaling.enabled == null && (var.write_autoscaling.min != null && var.write_autoscaling.max != null) || var.write_autoscaling.enabled == true ? true : false
    max = var.write_autoscaling.max
    min = var.write_autoscaling.min
  }
}

resource "aws_appautoscaling_target" "dynamodb_table_read_target" {
  count              = local.read_autoscaling.enabled ? 1 : 0
  max_capacity       = local.read_autoscaling.max
  min_capacity       = local.read_autoscaling.min
  resource_id        = "table/${var.table_name}"
  scalable_dimension = "dynamodb:table:ReadCapacityUnits"
  service_namespace  = "dynamodb"
}

resource "aws_appautoscaling_policy" "dynamodb_table_read_policy" {
  count              = local.read_autoscaling.enabled ? 1 : 0
  name               = "DynamoDBReadCapacityUtilization:${aws_appautoscaling_target.dynamodb_table_read_target[0].resource_id}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.dynamodb_table_read_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.dynamodb_table_read_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.dynamodb_table_read_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "DynamoDBReadCapacityUtilization"
    }

    target_value = 70
  }

  lifecycle {
    ignore_changes = [name]
  }
}

resource "aws_appautoscaling_target" "dynamodb_table_write_target" {
  count              = local.write_autoscaling.enabled ? 1 : 0
  max_capacity       = local.write_autoscaling.max
  min_capacity       = local.write_autoscaling.min
  resource_id        = "table/${var.table_name}"
  scalable_dimension = "dynamodb:table:WriteCapacityUnits"
  service_namespace  = "dynamodb"
}

resource "aws_appautoscaling_policy" "dynamodb_table_write_policy" {
  count              = local.write_autoscaling.enabled ? 1 : 0
  name               = "DynamoDBWriteCapacityUtilization:${aws_appautoscaling_target.dynamodb_table_write_target[0].resource_id}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.dynamodb_table_write_target[0].resource_id
  scalable_dimension = aws_appautoscaling_target.dynamodb_table_write_target[0].scalable_dimension
  service_namespace  = aws_appautoscaling_target.dynamodb_table_write_target[0].service_namespace

  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "DynamoDBWriteCapacityUtilization"
    }

    target_value = 70
  }

  lifecycle {
    ignore_changes = [name]
  }
}
