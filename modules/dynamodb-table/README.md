## Requirements

No requirements.

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | n/a |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_appautoscaling_policy.dynamodb_table_read_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/appautoscaling_policy) | resource |
| [aws_appautoscaling_policy.dynamodb_table_write_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/appautoscaling_policy) | resource |
| [aws_appautoscaling_target.dynamodb_table_read_target](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/appautoscaling_target) | resource |
| [aws_appautoscaling_target.dynamodb_table_write_target](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/appautoscaling_target) | resource |
| [aws_dynamodb_table.basic-dynamodb-table](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/dynamodb_table) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_attributes"></a> [attributes](#input\_attributes) | n/a | `map(string)` | n/a | yes |
| <a name="input_capacity_mode"></a> [capacity\_mode](#input\_capacity\_mode) | n/a | `string` | `"PAY_PER_REQUEST"` | no |
| <a name="input_default_capacity"></a> [default\_capacity](#input\_default\_capacity) | n/a | <pre>object({<br>    read  = number<br>    write = number<br>  })</pre> | `null` | no |
| <a name="input_deletion_protection"></a> [deletion\_protection](#input\_deletion\_protection) | n/a | `bool` | `true` | no |
| <a name="input_global_indexes"></a> [global\_indexes](#input\_global\_indexes) | List of GLOBAL Secondary Indexes for the table. | <pre>list(object({<br>    name = string<br>    projection_type = string<br>    hash_key = string<br>    range_key = optional(string, null)<br>    read_capacity = number<br>    write_capacity = number<br>    non_key_attributes = optional(list(string), null)<br>    on_demand_throughput = optional(object({<br>      read_units = number<br>      write_units = number<br>    }), null)<br>  }))</pre> | `[]` | no |
| <a name="input_hash_key"></a> [hash\_key](#input\_hash\_key) | n/a | `string` | n/a | yes |
| <a name="input_local_indexes"></a> [local\_indexes](#input\_local\_indexes) | List of LOCAL Secondary Indexes for the table. | <pre>list(object({<br>    name = string<br>    projection_type = string<br>    range_key = string<br>    non_key_attributes = optional(list(string), null)<br>  }))</pre> | `null` | no |
| <a name="input_on_demand_throughput"></a> [on\_demand\_throughput](#input\_on\_demand\_throughput) | Number of max read and write request units when using On-Demand capacity mode. | <pre>object({<br>    read = number<br>    write = number<br>  })</pre> | `null` | no |
| <a name="input_point_in_type_recovery"></a> [point\_in\_type\_recovery](#input\_point\_in\_type\_recovery) | n/a | `bool` | `true` | no |
| <a name="input_range_key"></a> [range\_key](#input\_range\_key) | n/a | `string` | `null` | no |
| <a name="input_read_autoscaling"></a> [read\_autoscaling](#input\_read\_autoscaling) | n/a | <pre>object({<br>    enabled = optional(bool, null)  # 'enabled' arg is now deprecated and remains for retrocompatibility<br>    min     = number<br>    max     = number<br>  })</pre> | <pre>{<br>  "enabled": false,<br>  "max": 2,<br>  "min": 1<br>}</pre> | no |
| <a name="input_table_name"></a> [table\_name](#input\_table\_name) | n/a | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | n/a | `map(string)` | `{}` | no |
| <a name="input_ttl_attribute"></a> [ttl\_attribute](#input\_ttl\_attribute) | When using Time to Live in DynamoDB, this variable will specify the<br>name of the table where you'll store the table's TTL expiration<br>timestamp in | `string` | `null` | no |
| <a name="input_write_autoscaling"></a> [write\_autoscaling](#input\_write\_autoscaling) | n/a | <pre>object({<br>    enabled = optional(bool, null)  # 'enabled' arg is now deprecated and remains for retrocompatibility<br>    min     = number<br>    max     = number<br>  })</pre> | <pre>{<br>  "enabled": false,<br>  "max": 2,<br>  "min": 1<br>}</pre> | no |

## Outputs

No outputs.
