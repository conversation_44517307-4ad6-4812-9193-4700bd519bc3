locals {
  capacity_mode = upper(var.capacity_mode) == "ON_DEMAND" ? "PAY_PER_REQUEST" : upper(var.capacity_mode)
  default_capacity = upper(var.capacity_mode) == "PROVISIONED" && var.default_capacity == null ? {
    write = 1
    read = 1
  } : var.default_capacity
}

resource "aws_dynamodb_table" "basic-dynamodb-table" {
  name           = var.table_name
  billing_mode   = local.capacity_mode
  read_capacity  = local.default_capacity == null ? null : local.default_capacity.read
  write_capacity = local.default_capacity == null ? null : local.default_capacity.write
  hash_key       = var.hash_key
  range_key      = var.range_key

  point_in_time_recovery {
    enabled = var.point_in_type_recovery
  }
  deletion_protection_enabled = var.deletion_protection

  dynamic "attribute" {
    for_each = var.attributes
    content {
      name = attribute.key
      type = attribute.value
    }
  }

  dynamic "global_secondary_index" {
    for_each = var.global_indexes == null ? [] : var.global_indexes
    content {
      name = global_secondary_index.value.name
      projection_type = global_secondary_index.value.projection_type
      hash_key = global_secondary_index.value.hash_key
      range_key = try(global_secondary_index.value.range_key, null)
      read_capacity = global_secondary_index.value.read_capacity
      write_capacity = global_secondary_index.value.write_capacity
      non_key_attributes = try(global_secondary_index.value.non_key_attributes, null)
      dynamic "on_demand_throughput" {
        for_each = global_secondary_index.value.on_demand_throughput != null ? [global_secondary_index.value.on_demand_throughput] : []
        content {
          max_read_request_units = on_demand_throughput.value.read_units == null ? null : on_demand_throughput.value.read_units
          max_write_request_units = on_demand_throughput.value.write_units == null ? null : on_demand_throughput.value.write_units
        }
      }
    }
  }

  dynamic "local_secondary_index" {
    for_each = var.local_indexes == null ? [] : var.local_indexes
    content {
      name = local_secondary_index.value.name
      projection_type = local_secondary_index.value.projection_type
      range_key = local_secondary_index.value.range_key
      non_key_attributes = try(local_secondary_index.value.non_key_attributes, null)
    }
  }

  ttl {
    attribute_name = var.ttl_attribute
    enabled        = var.ttl_attribute == null? false : true
  }

  dynamic "on_demand_throughput" {
    for_each = var.on_demand_throughput == null ? [] : [var.on_demand_throughput]
    content {
      max_read_request_units = on_demand_throughput.value.read
      max_write_request_units = on_demand_throughput.value.write
    }
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [stream_view_type, stream_enabled]

    precondition {
      condition = (upper(local.capacity_mode) == "PAY_PER_REQUEST" && local.default_capacity == null) || (upper(local.capacity_mode) == "PROVISIONED" && local.default_capacity != null)
      error_message = "If 'capacity_mode' is set to 'ON_DEMAND/PAY_PER_REQUEST', 'default_capacity' must not be set. When set to 'PROVISIONED', 'default_capacity' must be set."
    }
    precondition {
      condition = (var.on_demand_throughput != null && local.capacity_mode == "PAY_PER_REQUEST") || var.on_demand_throughput == null
      error_message = "When 'on_demand_throughput' is set, 'capacity_mode' MUST be 'ON_DEMAND/PAY_PER_REQUEST'."
    }
  }
}
