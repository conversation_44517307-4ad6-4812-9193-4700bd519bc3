resource "aws_cloudwatch_log_group" "lambda_loggroup" {
  name              = "/aws/lambda/${var.event_name}-lambda"
  retention_in_days = 365
}

resource "aws_cloudwatch_event_rule" "cw-event-rule" {
  name                = "${var.event_name}-event"
  description         = "Terraform: request-scheduler cronjob"
  schedule_expression = var.schedule_expression
}

resource "aws_cloudwatch_event_target" "cw-event-target" {
  arn       = aws_lambda_function.lambda.arn
  rule      = aws_cloudwatch_event_rule.cw-event-rule.id
  target_id = "${var.event_name}-lambda"

  depends_on = [
    aws_cloudwatch_event_rule.cw-event-rule
  ]

  input = jsonencode({})
}
