resource "aws_lambda_function" "lambda" {
  depends_on    = [aws_cloudwatch_log_group.lambda_loggroup]
  function_name = "${var.event_name}-lambda"
  role          = aws_iam_role.lambda_iam_role.arn
  handler       = "index.handler"
  runtime       = "nodejs16.x"

  filename         = "${path.module}/lambda.zip"
  source_code_hash = filebase64sha256("${path.module}/lambda.zip")

  environment {
    variables = {
      url     = var.url
      method  = var.method
      headers = var.headers
      body    = var.body
    }
  }

  vpc_config {
    subnet_ids         = var.network.subnets
    security_group_ids = var.network.security_groups
  }
}

resource "aws_lambda_permission" "lambda-permission" {
  statement_id  = "AllowExecutionFromEvents"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.cw-event-rule.arn
}
