variable "event_name" {
  type = string # make it short
}

variable "schedule_expression" {
  # For example, cron(0 20 * * ? *) or rate(5 minutes).
  type = string
  validation {
    condition     = can(regex("^(cron\\(.*\\)|rate\\(.*\\))$", var.schedule_expression))
    error_message = "Schedule expression must be in the format of cron() or rate()."
  }
}

variable "url" {
  type = string
}

variable "method" {
  type    = string # GET | POST | PUT | PATCH | DELETE | OPTIONS
  default = "GET"
  validation {
    condition     = can(regex("^(GET|POST|PUT|PATCH|DELETE|OPTIONS)$", var.method))
    error_message = "Method must be GET, POST, PUT, PATCH, DELETE, or OPTIONS."
  }
}

variable "headers" {
  type    = string
  default = "{}"
}

variable "body" {
  type    = string
  default = "{}"
}

variable "network" {
  type = object({
    vpc             = string
    cidr_block      = string
    subnets         = list(string)
    route_tables    = list(string)
    security_groups = list(string)
    network_acls    = list(string)
  })
}
