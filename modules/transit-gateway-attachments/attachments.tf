resource "aws_ec2_transit_gateway_vpc_attachment" "vpc_attachment" {
  transit_gateway_id = var.transit_gateway_id
  vpc_id             = var.vpc
  subnet_ids         = var.subnets

  dns_support  = "enable"
  ipv6_support = "disable"

  transit_gateway_default_route_table_association = true
  transit_gateway_default_route_table_propagation = true

  tags = var.tags
}

resource "aws_route" "vpc_route" {
  count                  = length(var.route_tables)
  route_table_id         = element(var.route_tables, count.index)
  destination_cidr_block = var.destination_cidr
  transit_gateway_id     = var.transit_gateway_id
}
