<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 route_tables  = 
	 subnets  = 
	 transit_gateway_id  = 
	 vpc  = 

	 # Optional variables
	 destination_cidr  = "10.0.0.0/8"
}
```
## Resources

| Name | Type |
|------|------|
| [aws_ec2_transit_gateway_vpc_attachment.vpc_attachment](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ec2_transit_gateway_vpc_attachment) | resource |
| [aws_route.vpc_route](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_destination_cidr"></a> [destination\_cidr](#input\_destination\_cidr) | n/a | `string` | `"10.0.0.0/8"` | no |
| <a name="input_route_tables"></a> [route\_tables](#input\_route\_tables) | n/a | `list(string)` | n/a | yes |
| <a name="input_subnets"></a> [subnets](#input\_subnets) | n/a | `list(string)` | n/a | yes |
| <a name="input_transit_gateway_id"></a> [transit\_gateway\_id](#input\_transit\_gateway\_id) | n/a | `string` | n/a | yes |
| <a name="input_vpc"></a> [vpc](#input\_vpc) | n/a | `string` | n/a | yes |
## Outputs

No outputs.
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->