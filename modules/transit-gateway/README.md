<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 name  = 

	 # Optional variables
	 amazon_side_asn  = 64512
}
```
## Resources

| Name | Type |
|------|------|
| [aws_ec2_transit_gateway.tg](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/ec2_transit_gateway) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_amazon_side_asn"></a> [amazon\_side\_asn](#input\_amazon\_side\_asn) | n/a | `number` | `64512` | no |
| <a name="input_name"></a> [name](#input\_name) | n/a | `string` | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_tg"></a> [tg](#output\_tg) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->