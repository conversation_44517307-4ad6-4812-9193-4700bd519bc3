locals {
  # this removes a possible / at the end of the prefix to avoid double slashes
  parameter_prefix = replace(var.parameter_prefix, "//$/", "")
}

resource "aws_ssm_parameter" "default_postgres_ssm_parameter_username" {
  name  = "${local.parameter_prefix}/db-root-username"
  value = var.root_username
  type  = "String"
  tags  = var.tags
}

resource "aws_ssm_parameter" "default_postgres_ssm_parameter_password" {
  name  = "${local.parameter_prefix}/db-root-password"
  value = random_password.random_password_postgres.result
  type  = "SecureString"
  tags  = var.tags
}
