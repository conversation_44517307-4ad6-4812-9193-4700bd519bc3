resource "random_password" "random_password_postgres" {
  length  = 20
  special = false
}

resource "aws_db_instance" "rds_postgres" {
  depends_on = [aws_db_parameter_group.custom]

  identifier     = var.server_name
  instance_class = var.instance_class
  db_name        = var.root_username
  port           = 5432

  username             = var.root_username
  password             = random_password.random_password_postgres.result
  parameter_group_name = length(var.custom_parameters) > 0 ? aws_db_parameter_group.custom[0].name : null

  # engine
  engine         = "postgres"
  engine_version = var.engine_version

  # storage
  storage_encrypted     = true
  allocated_storage     = var.allocated_storage
  storage_type          = var.storage_type
  max_allocated_storage = var.max_allocated_storage
  storage_throughput    = var.storage_throughput
  iops                  = var.provisioned_iops

  # network
  publicly_accessible    = var.publicly_accessible
  vpc_security_group_ids = var.rds_network.security_groups
  db_subnet_group_name   = var.rds_network.rds_subnet
  multi_az               = var.multi_az

  # backup
  backup_retention_period = var.backup_retention_period
  backup_window           = var.backup_window
  maintenance_window      = var.maintenance_window
  copy_tags_to_snapshot   = var.copy_tags_to_snapshot
  snapshot_identifier     = var.snapshot_identifier

  # monitoring
  auto_minor_version_upgrade            = var.auto_minor_version_upgrade
  apply_immediately                     = var.apply_immediately
  performance_insights_enabled          = var.performance_insights_enabled
  performance_insights_kms_key_id       = var.performance_insights_kms_key_id
  performance_insights_retention_period = var.performance_insights_retention_period
  enabled_cloudwatch_logs_exports       = var.enabled_cloudwatch_logs_exports
  monitoring_interval                   = var.monitoring_interval > 0 ? var.monitoring_interval : null
  monitoring_role_arn                   = var.monitoring_interval > 0 ? aws_iam_role.enhanced_monitoring[0].arn : null

  # deletion
  deletion_protection = var.deletion_protection
  skip_final_snapshot = var.skip_final_snapshot

  tags = var.tags

  lifecycle {
    ignore_changes = [db_name, password, engine_version, storage_encrypted, db_subnet_group_name, allocated_storage]
    # storage_encrypted can't be modified for an existing RDS instance
  }
}
