variable "server_name" {
  type = string
  validation {
    condition     = length(var.server_name) < 64
    error_message = "The server name must be less than 64 characters"
  }
}

variable "root_username" {
  type = string
}

# Example: "/dev/app_name/"
variable "parameter_prefix" {
  type = string
}

variable "rds_network" {
  type = object({
    vpc             = string
    cidr_block      = string
    subnets         = list(string)
    route_tables    = list(string)
    security_groups = list(string)
    network_acls    = list(string)
    rds_subnet      = string
  })
}

variable "engine_version" {
  type    = string
  default = "16.6"
}

# remember to change this according to the engine_version
variable "parameter_group_family" {
  type    = string
  default = "postgres16"
}

# https://aws.amazon.com/rds/instance-types/
variable "instance_class" {
  type    = string
  default = "db.t4g.micro"
}

variable "allocated_storage" {
  type    = number
  default = 20 # GiB
}

variable "max_allocated_storage" { # autoscaling
  type    = number
  default = 100
}

variable "storage_type" {
  type    = string
  default = "gp3"
}

variable "backup_retention_period" {
  type    = number
  default = 3
}

variable "backup_window" {
  type    = string
  default = "03:00-05:00"
}

variable "maintenance_window" {
  type    = string
  default = "mon:05:00-mon:05:30"
}

variable "copy_tags_to_snapshot" {
  type    = bool
  default = true
}

variable "deletion_protection" {
  type    = bool
  default = true
}

variable "skip_final_snapshot" {
  type    = bool
  default = true
}

variable "auto_minor_version_upgrade" {
  type    = bool
  default = true
}

variable "multi_az" {
  type    = bool
  default = false
}

variable "apply_immediately" {
  type    = bool
  default = true
}

variable "snapshot_identifier" {
  type    = string
  default = ""
}

variable "enabled_cloudwatch_logs_exports" {
  type    = list(string)
  default = ["postgresql", "upgrade"]
}

variable "monitoring_interval" {
  type    = number
  default = 0
}

variable "publicly_accessible" {
  type    = bool
  default = false
}

variable "performance_insights_enabled" {
  type    = bool
  default = true
}

variable "performance_insights_kms_key_id" {
  type    = string
  default = ""
}

variable "performance_insights_retention_period" {
  type    = number
  default = 7
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "custom_parameters" {
  type    = map(string)
  default = {}
}

variable "provisioned_iops" {
  type    = number
  default = null
}
variable "storage_throughput" {
  type    = number
  default = null
}
