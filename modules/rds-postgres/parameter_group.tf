# Create a custom DB parameter group
resource "aws_db_parameter_group" "custom" {
  count = length(var.custom_parameters) > 0 ? 1 : 0
  name  = "${var.server_name}-pg"

  family = var.parameter_group_family

  dynamic "parameter" {
    for_each = var.custom_parameters
    content {
      name  = parameter.key
      value = parameter.value
      # Add more keys here to fix "cannot use immediate apply method for static parameter"
      apply_method = contains(["max_connections"], parameter.key) ? "pending-reboot" : "immediate"
    }
  }
}
