resource "aws_lambda_function" "function" {
  depends_on = [aws_cloudwatch_log_group.lambda_loggroup_auth0]

  function_name = local.lambda_name
  image_uri     = "${data.aws_caller_identity.current.account_id}.dkr.ecr.us-east-1.amazonaws.com/sre-lambda-docker-hello-world:latest"
  package_type  = "Image"

  memory_size = var.memory_size
  timeout     = var.timeout

  role    = aws_iam_role.function_role.arn
  publish = true

  environment {
    variables = local.environment_variables
  }

  vpc_config {
    subnet_ids         = var.private_network.subnets
    security_group_ids = var.private_network.security_groups
  }

  tags = var.tags

  lifecycle {
    ignore_changes = [image_uri, version, qualified_arn, qualified_invoke_arn]
  }
}

resource "aws_lambda_alias" "function_alias" {
  name             = "${local.lambda_name}-alias"
  description      = "Lambda alias for ${local.lambda_name}"
  function_name    = aws_lambda_function.function.function_name
  function_version = "$LATEST"
}

resource "aws_lambda_permission" "allow_alb" {
  depends_on     = [aws_lambda_function.function]
  statement_id   = "allow-alb-${local.lambda_name}-tg"
  action         = "lambda:InvokeFunction"
  function_name  = aws_lambda_function.function.function_name
  principal      = "elasticloadbalancing.amazonaws.com"
  source_account = data.aws_caller_identity.current.account_id
  source_arn     = aws_alb_target_group.tg.arn
}
