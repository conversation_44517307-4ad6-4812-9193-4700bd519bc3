data "aws_canonical_user_id" "current" {}
data "aws_caller_identity" "current" {}

data "aws_ssm_parameter" "secrets" {
  for_each = var.environment_secrets
  name     = each.value
}

locals {
  # this variable contains all the environment variables, and the secrets (swapping the value for the secret value)
  environment_variables = merge(var.environment_variables, { for key, value in var.environment_secrets : key => data.aws_ssm_parameter.secrets[key].value })
  zone_name             = var.cluster.dns_prefix == "" ? "${var.name}" : "${var.name}.${var.cluster.dns_prefix}"
  public_zone_name      = strcontains(var.name, "-api") ? replace(local.zone_name, "-api", "-gw") : join(".", compact(["${var.name}-gw", var.cluster.dns_prefix]))
  lambda_name           = "${var.cluster.name}-${var.name}"
}
