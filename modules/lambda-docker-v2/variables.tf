variable "name" {
  type        = string
  description = "The name of the application. Will be used as a prefix for all resources."
}

variable "cluster" {
  type = object({
    name             = string
    arn              = string
    dns_prefix       = string
    public_listener  = string
    public_cname     = string
    private_listener = string
    private_cname    = string
  })
}

variable "private_network" {
  type = object({
    vpc             = string,
    vpc_name        = string,
    cidr_block      = string,
    subnets         = list(string),
    route_tables    = list(string),
    security_groups = list(string),
    network_acls    = list(string),
    vpc_link        = string,
    rds_subnet      = string
  })
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "Map of tags to apply to all resources."
}

variable "public_endpoint" {
  type    = bool
  default = false
}

variable "memory_size" {
  type        = number
  default     = 128
  description = "The amount of memory that your function has access to. Increasing the memory also increases the CPU available to your function."
  validation {
    condition     = var.memory_size % 64 == 0 && var.memory_size <= 10 * 1024
    error_message = "Memory size must be a multiple of 64 and not exceed 10240 (10G)."
  }
}

variable "timeout" {
  type        = number
  default     = 3
  description = "The amount of time that Lambda allows a function to run before stopping it."
  validation {
    condition     = var.timeout >= 1 && var.timeout <= 900
    error_message = "Timeout must be between 1 and 900 seconds"
  }
}

variable "ephemeral_storage" {
  type        = number
  default     = 512
  description = "The amount of ephemeral storage in MiB allocated to the function."
  validation {
    condition     = var.ephemeral_storage >= 512 && var.ephemeral_storage <= 10240
    error_message = "Ephemeral storage must be between 512 and 10240 MiB"
  }
}

variable "environment_variables" {
  type        = map(string)
  default     = {}
  description = "A map that defines environment variables for the Lambda function."

  validation {
    condition = alltrue([
      # Check that the key is not in the restricted keys list
      for key in keys(var.environment_variables) : !contains([
        "AWS_REGION", "AWS_DEFAULT_REGION", "AWS_LAMBDA_LOG_GROUP_NAME", "AWS_LAMBDA_LOG_STREAM_NAME",
        "AWS_LAMBDA_FUNCTION_NAME", "AWS_LAMBDA_FUNCTION_MEMORY_SIZE", "AWS_LAMBDA_FUNCTION_VERSION",
        "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_SESSION_TOKEN"],
      key)]) && alltrue([
      # Check that the value length does not exceed 4KB
      for value in values(var.environment_variables) : length(value) <= 4096
    ])
    error_message = "One of your environment variables is invalid or too long (4KB): https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html#configuration-envvars-runtime"
  }
}

variable "environment_secrets" {
  type        = map(string)
  default     = {}
  description = "A map that defines parameter store paths for the Lambda function."

  validation {
    condition = alltrue([
      # Check that the key is not in the restricted keys list
      for key in keys(var.environment_secrets

        ) : !contains([
          "AWS_REGION", "AWS_DEFAULT_REGION", "AWS_LAMBDA_LOG_GROUP_NAME", "AWS_LAMBDA_LOG_STREAM_NAME",
          "AWS_LAMBDA_FUNCTION_NAME", "AWS_LAMBDA_FUNCTION_MEMORY_SIZE", "AWS_LAMBDA_FUNCTION_VERSION",
        "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_SESSION_TOKEN"],
      key)
    ])
    error_message = "One of your environment secrets is invalid or too long (4KB): https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html#configuration-envvars-runtime"
  }
}

# the default value stands for the zone for whykeyway.com
variable "route53_zone_id" {
  type    = string
  default = "Z05531162DGNWUXR3CVOO"
}

variable "extra_iam_policy_arns" {
  type    = list(string)
  default = []
}

variable "ecr_max_image_count" {
  type        = number
  default     = 5
  description = "The maximum number of images to keep in the ECR repository."
}
