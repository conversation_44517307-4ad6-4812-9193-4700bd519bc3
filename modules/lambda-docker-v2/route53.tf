resource "aws_route53_record" "api" {
  zone_id = var.route53_zone_id
  name    = local.zone_name
  type    = "CNAME"
  ttl     = "300"
  records = [var.cluster.private_cname]
}

resource "aws_route53_record" "gateway" {
  count   = var.public_endpoint ? 1 : 0
  zone_id = var.route53_zone_id
  name    = local.public_zone_name
  type    = "CNAME"
  ttl     = "300"
  records = [var.cluster.public_cname]
}
