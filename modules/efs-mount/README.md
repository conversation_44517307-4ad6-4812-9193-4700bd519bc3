<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 disk_name  = 
	 security_groups  = 
	 subnet_ids  = 
}
```
## Resources

| Name | Type |
|------|------|
| [aws_efs_file_system.efs_drive](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/efs_file_system) | resource |
| [aws_efs_mount_target.efs_mount](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/efs_mount_target) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_disk_name"></a> [disk\_name](#input\_disk\_name) | example: disk\_name = "efs-drive-example" | `string` | n/a | yes |
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | example: security\_groups = ["sg-xxxxxxx", "sg-yyyyyyy"] | `list(string)` | n/a | yes |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | example: subnet\_ids = ["subnet-xxxxxxx", "subnet-yyyyyyy"] | `list(string)` | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_arn"></a> [arn](#output\_arn) | n/a |
| <a name="output_efs_fs_id"></a> [efs\_fs\_id](#output\_efs\_fs\_id) | n/a |
| <a name="output_efs_mount_dns_name"></a> [efs\_mount\_dns\_name](#output\_efs\_mount\_dns\_name) | n/a |
| <a name="output_efs_mount_ids"></a> [efs\_mount\_ids](#output\_efs\_mount\_ids) | n/a |
| <a name="output_name"></a> [name](#output\_name) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->