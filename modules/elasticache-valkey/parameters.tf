resource "aws_ssm_parameter" "valkey_host" {
  count = var.parameter_prefix != "" ? 1 : 0

  name  = "${var.parameter_prefix}/valkey-host"
  type  = "String"
  value = aws_elasticache_replication_group.valkey.primary_endpoint_address
  tags  = var.tags
}

resource "aws_ssm_parameter" "valkey_port" {
  count = var.parameter_prefix != "" ? 1 : 0

  name  = "${var.parameter_prefix}/valkey-port"
  type  = "String"
  value = tostring(aws_elasticache_replication_group.valkey.port)
  tags  = var.tags
}

resource "aws_ssm_parameter" "valkey_connection_string" {
  count = var.parameter_prefix != "" ? 1 : 0

  name  = "${var.parameter_prefix}/valkey-connection-string"
  type  = "String"
  value = "${aws_elasticache_replication_group.valkey.primary_endpoint_address}:${aws_elasticache_replication_group.valkey.port}"
  tags  = var.tags
}

