/* SG for Public services */
resource "aws_security_group" "public" {
  name        = "${var.vpc.name}-public-sg"
  description = "Public access from the internet"
  vpc_id      = aws_vpc.vpc.id
  depends_on  = [aws_vpc.vpc]
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-public-sg"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
resource "aws_security_group_rule" "public_in_tcp_80" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.public.id
  description       = "terraform: public access from port 80"
}
resource "aws_security_group_rule" "public_in_tcp_443" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.public.id
  description       = "terraform: public access from port 443"
}
resource "aws_security_group_rule" "public_in_custom" {
  for_each          = toset(var.public_ports)
  type              = "ingress"
  from_port         = each.value
  to_port           = each.value
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.public.id
  description       = "terraform: public access from port ${each.value}"
}
resource "aws_security_group_rule" "public_out_everything" {
  type              = "egress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.public.id
  description       = "terraform: public egress to the internet"
}
/* SG for Private services */
resource "aws_security_group" "private" {
  name        = "${var.vpc.name}-private-sg"
  description = "Private access from the VPC"
  vpc_id      = aws_vpc.vpc.id
  depends_on  = [aws_vpc.vpc]
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-private-sg"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
resource "aws_security_group_rule" "private_in_local_network" {
  type              = "ingress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = [var.vpc.cidr]
  security_group_id = aws_security_group.private.id
  description       = "terraform: private ingress from within the VPC"
}

/* TODO: This is the SRE VPC CIDR hardcoded, this should be parametrized or moved to the openvpn module */
resource "aws_security_group_rule" "private_in_sre_vpc" {
  count             = length(var.sg_extra_cidrs) > 0 ? 1 : 0
  type              = "ingress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = var.sg_extra_cidrs
  security_group_id = aws_security_group.private.id
  description       = "terraform: private ingress from extra CIDRs"
}

resource "aws_security_group_rule" "private_out_everywhere" {
  type              = "egress"
  from_port         = 0
  to_port           = 65535
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.private.id
  description       = "terraform: private egress to the internet"
}
