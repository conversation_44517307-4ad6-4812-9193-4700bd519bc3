resource "aws_apigatewayv2_vpc_link" "vpc_link_public" {
  name               = "${var.vpc.name}-public"
  security_group_ids = [aws_security_group.public.id]
  subnet_ids         = aws_subnet.public_subnet.*.id

  tags = merge(var.tags, {
    VPC            = "${var.vpc.name}"
    Subnet         = join(" ", aws_subnet.public_subnet.*.id)
    SecurityGroups = aws_security_group.public.id
    "billing:App"  = "network"
    "billing:Env"  = var.vpc.name
  })
}

resource "aws_apigatewayv2_vpc_link" "vpc_link_private" {
  name               = "${var.vpc.name}-private"
  security_group_ids = [aws_security_group.private.id]
  subnet_ids         = aws_subnet.private_subnet.*.id

  tags = merge(var.tags, {
    VPC            = "${var.vpc.name}"
    Subnet         = join(" ", aws_subnet.public_subnet.*.id)
    SecurityGroups = aws_security_group.private.id
    "billing:App"  = "network"
    "billing:Env"  = var.vpc.name
  })
}
