## Expected output:
## xxx_network: {
##   vpc: "vpc-xxxx",
##   subnets: [ "subnet-xxxx", "subnet-yyyy", ...],
##   route_tables: [ "rt-xxxx", "rt-yyyy", ...],
##   security_groups: [ "sg-xxxx", "sg-yyyy", ...],
##   network_acls: [ "nacl-xxxx", "nacl-yyyy", ...]
## }

output "public_network" {
  value = {
    vpc             = aws_vpc.vpc.id,
    vpc_name        = var.vpc.name,
    cidr_block      = aws_vpc.vpc.cidr_block,
    subnets         = aws_subnet.public_subnet.*.id,
    route_tables    = [aws_route_table.public.id],
    security_groups = [aws_security_group.public.id],
    network_acls    = [aws_network_acl.public.id],
    vpc_link        = aws_apigatewayv2_vpc_link.vpc_link_public.id
    rds_subnet      = aws_db_subnet_group.db_subnet.id
  }
}

output "private_network" {
  value = {
    vpc             = aws_vpc.vpc.id,
    vpc_name        = var.vpc.name,
    cidr_block      = aws_vpc.vpc.cidr_block,
    subnets         = aws_subnet.private_subnet.*.id,
    route_tables    = aws_route_table.private.*.id,
    security_groups = [aws_security_group.private.id],
    network_acls    = [aws_network_acl.private.id],
    vpc_link        = aws_apigatewayv2_vpc_link.vpc_link_private.id
    rds_subnet      = aws_db_subnet_group.db_subnet.id
  }
}
