/* Public subnets */
resource "aws_network_acl" "public" {
  vpc_id = aws_vpc.vpc.id
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-public-acl"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
resource "aws_network_acl_rule" "public_ingress_all" {
  network_acl_id = aws_network_acl.public.id
  rule_number    = 100
  egress         = false
  protocol       = "-1"
  rule_action    = "allow"
  cidr_block     = "0.0.0.0/0"
  from_port      = 1
  to_port        = 65535
}
resource "aws_network_acl_rule" "public_egress_all" {
  network_acl_id = aws_network_acl.public.id
  rule_number    = 100
  egress         = true
  protocol       = "-1"
  rule_action    = "allow"
  cidr_block     = "0.0.0.0/0"
  from_port      = 1
  to_port        = 65535
}
resource "aws_network_acl_association" "public" {
  count          = length(var.public_subnets)
  network_acl_id = aws_network_acl.public.id
  subnet_id      = aws_subnet.public_subnet[count.index].id
}
/* Private subnets */
resource "aws_network_acl" "private" {
  vpc_id = aws_vpc.vpc.id
  tags = {
    Name = "${var.vpc.name}-private-acl"
  }
}
resource "aws_network_acl_rule" "private_ingress_all" {
  network_acl_id = aws_network_acl.private.id
  rule_number    = 100
  egress         = false
  protocol       = "-1"
  rule_action    = "allow"
  cidr_block     = "0.0.0.0/0"
  from_port      = 1
  to_port        = 65535
}
resource "aws_network_acl_rule" "private_egress_all" {
  network_acl_id = aws_network_acl.private.id
  rule_number    = 100
  egress         = true
  protocol       = "-1"
  rule_action    = "allow"
  cidr_block     = "0.0.0.0/0"
  from_port      = 1
  to_port        = 65535
}
resource "aws_network_acl_association" "private" {
  count          = length(var.private_subnets)
  network_acl_id = aws_network_acl.private.id
  subnet_id      = aws_subnet.private_subnet[count.index].id
}
