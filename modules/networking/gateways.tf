# Internet gateway (shared) 
resource "aws_internet_gateway" "ig" {
  vpc_id = aws_vpc.vpc.id
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-igw"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}

# Nat gateways' IPs (one per az)
resource "aws_eip" "nat_eip" {
  count      = length(var.nat_gateways)
  domain     = "vpc"
  depends_on = [aws_internet_gateway.ig]
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-nat-${element(var.nat_gateways.*.zone, count.index)}"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}

# Nat gateways (one per az) 
resource "aws_nat_gateway" "nat" {
  count         = length(var.nat_gateways)
  allocation_id = aws_eip.nat_eip[count.index].id
  # gets the id from the first public subnet with the same zone as this gateway
  subnet_id  = aws_subnet.public_subnet[index(var.public_subnets.*.zone, var.nat_gateways[count.index].zone)].id
  depends_on = [aws_internet_gateway.ig]
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-nat-${element(var.nat_gateways.*.zone, count.index)}"
    VPC           = "${var.vpc.name}"
    Subnet        = aws_subnet.public_subnet[index(var.public_subnets.*.zone, var.nat_gateways[count.index].zone)].id
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
