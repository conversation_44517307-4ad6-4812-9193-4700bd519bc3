/* Public routing table (shared) */
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.vpc.id
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-public-route-table"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
resource "aws_route" "public_internet_gateway" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.ig.id
}
resource "aws_route_table_association" "public" {
  count          = length(var.public_subnets)
  subnet_id      = element(aws_subnet.public_subnet.*.id, count.index)
  route_table_id = aws_route_table.public.id
}
/* Private routing tables (one per az) */
resource "aws_route_table" "private" {
  count  = length(var.nat_gateways)
  vpc_id = aws_vpc.vpc.id
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-${element(var.nat_gateways.*.zone, count.index)}-private-route-table"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
resource "aws_route" "private_nat_gateway" {
  count                  = length(var.nat_gateways)
  route_table_id         = aws_route_table.private[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat[count.index].id
}
resource "aws_route_table_association" "private" {
  count          = length(var.private_subnets)
  subnet_id      = element(aws_subnet.private_subnet.*.id, count.index)
  route_table_id = element(aws_route_table.private.*.id, count.index)
}
