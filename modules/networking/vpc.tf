/*==== The VPC ======*/
resource "aws_vpc" "vpc" {
  cidr_block           = var.vpc.cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-vpc"
    VPC           = "${var.vpc.name}"
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}

/*==== Public Subnets ======*/
resource "aws_subnet" "public_subnet" {
  vpc_id                  = aws_vpc.vpc.id
  count                   = length(var.public_subnets)
  cidr_block              = element(var.public_subnets, count.index).cidr
  availability_zone       = element(var.public_subnets, count.index).zone
  map_public_ip_on_launch = true
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-public-${element(var.public_subnets, count.index).zone}"
    VPC           = "${var.vpc.name}"
    Subnet        = element(var.public_subnets, count.index).zone
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}

/*==== Private Subnets ======*/
resource "aws_subnet" "private_subnet" {
  vpc_id                  = aws_vpc.vpc.id
  count                   = length(var.private_subnets)
  cidr_block              = var.private_subnets[count.index].cidr
  availability_zone       = element(var.private_subnets, count.index).zone
  map_public_ip_on_launch = false
  tags = merge(var.tags, {
    Name          = "${var.vpc.name}-private-${element(var.private_subnets, count.index).zone}"
    VPC           = "${var.vpc.name}"
    Subnet        = element(var.private_subnets, count.index).zone
    "billing:App" = "network"
    "billing:Env" = var.vpc.name
  })
}
