variable "vpc" {
  type = object({
    name = string
    cidr = string
  })
  description = "example vpc: { name: \"prod\", cidr: \"10.0.0.0/16\" }"
}

variable "nat_gateways" {
  type = list(object({
    zone = string
  }))
  description = "example nat: [{ zone: \"us-east-1a\" }, { zone: \"us-east-1b\" }]"
}

variable "public_subnets" {
  type = list(object({
    zone = string
    cidr = string
  }))
  description = "example subnet: [{ zone: \"us-east-1a\", cidr: \"10.0.0.0/22\" }, { zone: \"us-east-1b\", cidr: \"********/22\" }]"
}

variable "private_subnets" {
  type = list(object({
    zone = string
    cidr = string
  }))
  description = "example subnet: [{ zone: \"us-east-1a\", cidr: \"**********/22\" }, { zone: \"us-east-1b\", cidr: \"**********/22\" }]"
}

variable "sg_extra_cidrs" {
  type    = list(string)
  default = []
}

variable "public_ports" {
  type    = list(string)
  default = []
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "Map of tags to apply to all resources."
}

variable "enable_flow_logs" {
  type        = bool
  default     = false
  description = "Enable VPC flow logs."
}
