variable "bucket_name" {
  type = string
}

variable "cleanup_lifecycle_days" {
  type        = number
  description = "The number of days to keep S3 objects before deleting them, set 0 to disable cleanup."
  default     = 0
}

variable "cors_allowed_origins" {
  type    = list(string)
  default = ["*.whykeyway.com", "*.dev.whykeyway.com", "*.stg.whykeyway.com", "*.demo.whykeyway.com", "*.vercel.app", "localhost:3000", "localhost"]
}

variable "max_ttl" {
  type    = number
  default = 86400
}

variable "tags" {
  type    = map(string)
  default = {}
}

variable "notification_queues" {
  type = list(object({
    arn    = string
    suffix = string
  }))
  default = []
}

variable "policy_extra_services" {
  type = list(string)
  default = []
}