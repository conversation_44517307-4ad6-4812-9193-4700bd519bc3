data "aws_canonical_user_id" "current" {}
data "aws_caller_identity" "current" {}

resource "aws_s3_bucket" "bucket" {
  bucket = var.bucket_name
  tags   = var.tags
}

resource "aws_s3_bucket_ownership_controls" "example" {
  bucket = aws_s3_bucket.bucket.id

  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

data "aws_iam_policy_document" "bucket_policy_document" {
  statement {
    sid    = "AllowFullControlToAccountUsers"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }

    actions = ["s3:*"]

    resources = [
      aws_s3_bucket.bucket.arn,
      "${aws_s3_bucket.bucket.arn}/*",
    ]
  }
  dynamic "statement" {
    for_each = var.policy_extra_services
    content {
      effect = "Allow"
      principals {
        type        = "Service"
        identifiers = [statement.value]
      }
      actions = ["s3:*"]
      resources = [
        aws_s3_bucket.bucket.arn,
        "${aws_s3_bucket.bucket.arn}/*"
      ]
    }
  }
}


resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.bucket.id
  policy = data.aws_iam_policy_document.bucket_policy_document.json
}

resource "aws_s3_bucket_public_access_block" "bucket" {
  bucket = aws_s3_bucket.bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_lifecycle_configuration" "lifecycle" {
  # Only apply the lifecycle rule if cleanup_lifecycle_days is greater than 0
  count  = var.cleanup_lifecycle_days > 0 ? 1 : 0
  bucket = aws_s3_bucket.bucket.id

  rule {
    id     = "LifecycleRule"
    status = "Enabled"

    expiration {
      days = var.cleanup_lifecycle_days
    }

    noncurrent_version_expiration {
      noncurrent_days = var.cleanup_lifecycle_days
    }

    filter { prefix = "" }
  }
}

resource "aws_s3_bucket_versioning" "bucket-versioning" {
  bucket = aws_s3_bucket.bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_cors_configuration" "s3_cors" {
  depends_on = [aws_s3_bucket.bucket]
  bucket     = aws_s3_bucket.bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = var.cors_allowed_origins

    expose_headers  = ["ETag", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers"]
    max_age_seconds = var.max_ttl
  }
}

resource "aws_s3_bucket_notification" "bucket_notification" {
  count  = length(var.notification_queues)
  bucket = aws_s3_bucket.bucket.id

  queue {
    queue_arn     = var.notification_queues[count.index].arn
    events        = ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"]
    filter_suffix = var.notification_queues[count.index].suffix
  }
}
