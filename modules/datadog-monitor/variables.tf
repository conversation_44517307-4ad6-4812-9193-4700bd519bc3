variable "app_name" {
  description = "Name of the application"
  type        = string
}

variable "type" {
  description = "Type of monitor to create"
  type        = string
  validation {
    condition     = contains(["error_rate", "avg_latency", "p90_latency"], var.type)
    error_message = "Type must be one of: error_rate, avg_latency, p90_latency"
  }
}

variable "notify" {
  description = "Slack channel to notify (must exist on Datadog)"
  type        = string
}

variable "critical" {
  description = "Critical threshold for the monitor"
  type        = string
  validation {
    condition     = can(regex("^[0-9]+(\\.[0-9]+)?$", var.critical))
    error_message = "Critical threshold must be a number"
  }
}

variable "warning" {
  description = "Warning threshold for the monitor"
  type        = string
  validation {
    condition     = can(regex("^[0-9]+(\\.[0-9]+)?$", var.warning))
    error_message = "Warning threshold must be a number"
  }
}

variable "tags" {
  description = "Tags to apply to the monitor"
  type        = map(string)
  default     = {}
}

variable "environment" {
  description = "Environment variables"
  type = object({
    private_network = object({
      vpc_name = string
    })
  })
}
