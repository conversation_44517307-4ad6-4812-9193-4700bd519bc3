locals {
  pretty_type = {
    error_rate  = "Error rate"
    avg_latency = "Average latency"
    p90_latency = "P90 latency"
  }

  queries = {
    error_rate  = "sum(last_10m):(sum:trace.servlet.request.errors{env:${var.environment.private_network.vpc_name},service:${var.app_name}}.as_count() / sum:trace.servlet.request.hits{env:${var.environment.private_network.vpc_name},service:${var.app_name}}.as_count())"
    avg_latency = "sum(last_10m):(avg:trace.servlet.request{env:${var.environment.private_network.vpc_name},service:${var.app_name}}.rollup(sum).fill(zero) / cutoff_min(sum:trace.servlet.request.hits{env:${var.environment.private_network.vpc_name},service:${var.app_name}}.as_count(), 5))"
    p90_latency = "percentile(last_10m):p90:trace.servlet.request{env:${var.environment.private_network.vpc_name},service:${var.app_name}}"
  }

  name    = "Service ${var.app_name} has a high ${lower(lookup(local.pretty_type, var.type, ""))} on env:${var.environment.private_network.vpc_name}"
  query   = "${lookup(local.queries, var.type, "")} > ${var.critical}"
  message = "${lookup(local.pretty_type, var.type, "")} on ${var.app_name} is too high. ${var.notify}"

  tags = [for k, v in var.tags : "${k}:${v}"]
}
