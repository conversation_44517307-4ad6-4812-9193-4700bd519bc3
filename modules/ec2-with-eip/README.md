<!-- BEGIN_AUTOMATED_TF_DOCS_BLOCK -->
## Requirements

No requirements.
## Usage
Basic usage of this module is as follows:
```hcl
module "example" {
	 source  = "<module-path>"

	 # Required variables
	 ami_id  = 
	 instance_name  = 
	 instance_type  = 
	 key_name  = 
	 security_groups  = 
	 subnet_id  = 
	 volume_size  = 

	 # Optional variables
	 extra_policies  = []
	 user_data  = null
}
```
## Resources

| Name | Type |
|------|------|
| [aws_eip.eip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_iam_instance_profile.main](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_instance_profile) | resource |
| [aws_iam_role.codedeploy_service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role.instance_profile](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.codedeploy_service](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.extra_policies](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_profile_codedeploy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.instance_profile_ecr](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_instance.vm](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/instance) | resource |
## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_ami_id"></a> [ami\_id](#input\_ami\_id) | https://cloud-images.ubuntu.com/locator/ec2/ ubuntu 20.04 amd64 => ami-01b996646377b6619 ubuntu 20.04 arm64 => ami-03e1711813e5a07b1 | `string` | n/a | yes |
| <a name="input_extra_policies"></a> [extra\_policies](#input\_extra\_policies) | example: extra\_policies = ["arn:aws:iam::00000000:policy/some-policy"] | `list(string)` | `[]` | no |
| <a name="input_instance_name"></a> [instance\_name](#input\_instance\_name) | example: instance\_name = "blog-instance" | `string` | n/a | yes |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | https://aws.amazon.com/ec2/instance-types/ | `string` | n/a | yes |
| <a name="input_key_name"></a> [key\_name](#input\_key\_name) | https://console.aws.amazon.com/ec2/v2/home?region=us-east-1#KeyPairs: example: key\_name = "unlock" | `string` | n/a | yes |
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | example: security\_groups = ["sg-xxxxxxxx", "sg-yyyyyyy"] | `list(string)` | n/a | yes |
| <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id) | example: subnet\_id = "subnet-xxxxxxxxxx" | `string` | n/a | yes |
| <a name="input_user_data"></a> [user\_data](#input\_user\_data) | example: user\_data = "echo 'some script goes here'; exit 0" | `string` | `null` | no |
| <a name="input_volume_size"></a> [volume\_size](#input\_volume\_size) | example: volume\_size = 20 (GiB) | `number` | n/a | yes |
## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cname"></a> [cname](#output\_cname) | n/a |
| <a name="output_ec2_id"></a> [ec2\_id](#output\_ec2\_id) | n/a |
| <a name="output_private_ip"></a> [private\_ip](#output\_private\_ip) | n/a |
| <a name="output_public_ip"></a> [public\_ip](#output\_public\_ip) | n/a |
<!-- END_AUTOMATED_TF_DOCS_BLOCK -->