# https://cloud-images.ubuntu.com/locator/ec2/
# ubuntu 20.04 amd64 => ami-01b996646377b6619
# ubuntu 20.04 arm64 => ami-03e1711813e5a07b1
variable "ami_id" {
  type = string
}

# https://aws.amazon.com/ec2/instance-types/
variable "instance_type" {
  type = string
}

# https://console.aws.amazon.com/ec2/v2/home?region=us-east-1#KeyPairs:
# example: key_name = "unlock"
variable "key_name" {
  type = string
}

# example: subnet_id = "subnet-xxxxxxxxxx"
variable "subnet_id" {
  type = string
}

# example: security_groups = ["sg-xxxxxxxx", "sg-yyyyyyy"]
variable "security_groups" {
  type = list(string)
}

# example: volume_size = 20 (GiB)
variable "volume_size" {
  type = number
}

# example: instance_name = "blog-instance"
variable "instance_name" {
  type = string
}

# example: user_data = "echo 'some script goes here'; exit 0"
variable "user_data" {
  default = null
  type    = string
}

# example: extra_policies = ["arn:aws:iam::00000000:policy/some-policy"]
variable "extra_policies" {
  default = []
  type    = list(string)
}

variable "tags" {
  type    = map(string)
  default = {}
}
