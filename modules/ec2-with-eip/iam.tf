# create a service role for ec2 
resource "aws_iam_role" "instance_profile" {
  name = "codedeploy-profile-${var.instance_name}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "",
      "Effect": "Allow",
      "Principal": {
        "Service": [
          "ec2.amazonaws.com"
        ]
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

# create an instance profile with previous role
resource "aws_iam_instance_profile" "main" {
  name = "codedeploy-profile-${var.instance_name}"
  role = aws_iam_role.instance_profile.name
}

# provide read access to ECR
resource "aws_iam_role_policy_attachment" "instance_profile_ecr" {
  role       = aws_iam_role.instance_profile.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
}

# provide access to SSM (for AWS Inspector)
resource "aws_iam_role_policy_attachment" "instance_profile_ssm" {
  role       = aws_iam_role.instance_profile.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

# attach extra policies if needed
resource "aws_iam_role_policy_attachment" "extra_policies" {
  count      = length(var.extra_policies)
  role       = aws_iam_role.instance_profile.name
  policy_arn = var.extra_policies[count.index]
}
