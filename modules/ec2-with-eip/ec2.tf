# Elastic IP
resource "aws_eip" "eip" {
  instance = aws_instance.vm.id
  domain   = "vpc"
  tags = {
    Name = "${var.instance_name}-eip"
  }
}

# EC2 Instance
resource "aws_instance" "vm" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name               = var.key_name
  subnet_id              = var.subnet_id
  vpc_security_group_ids = var.security_groups
  iam_instance_profile   = aws_iam_instance_profile.main.id
  user_data              = var.user_data

  root_block_device {
    volume_size = var.volume_size
    volume_type = "gp3"
    tags = merge(var.tags, {
      Name = "${var.instance_name}-sda1"
    })
  }

  metadata_options {
    http_endpoint = "enabled"
    http_tokens   = "required"
  }

  tags = merge(var.tags, {
    Name = var.instance_name
  })

  lifecycle {
    ignore_changes = [
      root_block_device, user_data
    ]
  }
}
