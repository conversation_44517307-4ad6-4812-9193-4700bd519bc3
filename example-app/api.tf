module "api" {
  app_name = local.ecs_name
  cluster  = var.environment.cluster
  source   = "../../../modules/ecs-service-deployment-v2"

  container_size = {
    cpu    = 1024
    memory = 2048
  }

  chatbot_arn     = var.environment.chatbot_arn
  private_network = var.environment.private_network
  public_endpoint = true

  github_repository  = "unlockre/whats-new-api"
  github_branch_name = "develop"

  codebuild_migration_stage = false

  ecs_variables = {
    # KEY = VALUE
  }

  ecs_secrets = {
    # VAR_NAME = "/env/app/parameter_name"
  }

  tags = local.default_tags
}
