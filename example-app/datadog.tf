module "monitor_error_rate" {
  source      = "../../../modules/datadog-monitor"
  app_name    = local.app_name
  environment = var.environment

  type   = "error_rate"
  notify = "@slack-sre-warnings"

  critical = "0.05"
  warning  = "0.02"

  tags = local.default_tags
}

module "monitor_avg_latency" {
  source      = "../../../modules/datadog-monitor"
  app_name    = local.app_name
  environment = var.environment

  type     = "avg_latency"
  notify   = "@slack-sre-warnings"
  critical = "0.1"
  warning  = "0.05"

  tags = local.default_tags
}

module "monitor_p90_latency" {
  source      = "../../../modules/datadog-monitor"
  app_name    = local.app_name
  environment = var.environment

  type     = "p90_latency"
  notify   = "@slack-sre-warnings"
  critical = "1"
  warning  = "0.8"

  tags = local.default_tags
}
