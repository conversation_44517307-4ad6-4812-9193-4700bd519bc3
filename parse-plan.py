import json
import sys


def parse_plan(plan_file):
    with open(plan_file) as f:
        plan = json.load(f)

    added = []
    changed = []
    destroyed = []

    for resource in plan["resource_changes"]:
        actions = resource["change"]["actions"]
        if "create" in actions:
            added.append(resource["address"])
        if "update" in actions:
            changed.append(resource["address"])
        if "delete" in actions:
            destroyed.append(resource["address"])

    return added, changed, destroyed


def format_changes(added, changed, destroyed, directory):
    if not added and not changed and not destroyed:
        # No changes case
        return f"### Plan for Directory: {directory}\n\nNo changes to apply."

    comment = f"### Plan for Directory: {directory}\n\n"
    comment += "```diff\n"

    for resource in added:
        comment += f"+ {resource}\n"

    for resource in changed:
        comment += f"~ {resource}\n"

    for resource in destroyed:
        comment += f"- {resource}\n"

    comment += "```\n"
    return comment


if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: parse_plan.py <path_to_plan.json> <directory>")
        sys.exit(1)

    plan_file = sys.argv[1]
    directory = sys.argv[2]
    added, changed, destroyed = parse_plan(plan_file)
    formatted_comment = format_changes(added, changed, destroyed, directory)

    print(json.dumps({"body": formatted_comment}))
